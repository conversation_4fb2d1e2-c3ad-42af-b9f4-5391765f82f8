(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[128],{541:(e,r,n)=>{Promise.resolve().then(n.bind(n,5187))},5187:(e,r,n)=>{"use strict";n.d(r,{NewRun:()=>E});var s=n(7093),t=n(4545),l=n(8923),a=n(9860),i=n(8590),o=n(4060),c=n(7783),d=n(5049),u=n.n(d),x=n(2247),m=n(8365),h=n(1040),j=n(1005),f=n(5745),p=n(8977),g=n(8936),v=n(7676),y=n(6701),N=n(3401);let C=(0,N.createServerReference)("405173a3cf25fdf1c0d8eafa47a635bc87618994ab",<PERSON>.callServer,void 0,N.findSourceMapURL,"createRun"),S=(0,N.createServerReference)("7f682fdd58ce3f411717153e1df5ded097cfd6a98d",N.callServer,void 0,N.findSourceMapURL,"getExercises"),b=a.z.object({model:a.z.string().min(1,{message:"Model is required."}),description:a.z.string().optional(),suite:a.z.enum(["full","partial"]),exercises:a.z.array(a.z.string()).optional(),settings:y.us4.optional(),concurrency:a.z.number().int().min(1).max(25),timeout:a.z.number().int().min(5).max(10),systemPrompt:a.z.string().optional()}).refine(e=>"full"===e.suite||(e.exercises||[]).length>0,{message:"Exercises are required when running a partial suite.",path:["exercises"]});var z=n(8322);let w=a.z.object({id:a.z.string(),name:a.z.string()}),R=async()=>{let e=await fetch("https://openrouter.ai/api/v1/models");if(!e.ok)return[];let r=a.z.object({data:a.z.array(w)}).safeParse(await e.json());return r.success?r.data.data.sort((e,r)=>e.name.localeCompare(r.name)):(console.error(r.error),[])},k=()=>(0,i.I)({queryKey:["getOpenRouterModels"],queryFn:R});var A=n(4548);let O=[...y.jK9,...y.LT1];function M(e){let{customSettings:{experiments:r,...n},defaultSettings:{experiments:t,...l},className:a,...i}=e,o={...l,...t},c={...n,...r};return(0,s.jsxs)("div",{className:(0,z.cn)("grid grid-cols-3 gap-2 text-sm p-2",a),...i,children:[(0,s.jsx)("div",{className:"font-medium text-muted-foreground",children:"Setting"}),(0,s.jsx)("div",{className:"font-medium text-muted-foreground",children:"Default"}),(0,s.jsx)("div",{className:"font-medium text-muted-foreground",children:"Custom"}),O.map(e=>{let r=o[e],n=c[e];return JSON.stringify(r)===JSON.stringify(n)?null:(0,s.jsx)(I,{name:e,defaultValue:JSON.stringify(r,null,2),customValue:JSON.stringify(n,null,2)},e)})]})}function I(e){let{name:r,defaultValue:n,customValue:l,...a}=e;return(0,s.jsxs)(t.Fragment,{...a,children:[(0,s.jsx)("div",{className:"overflow-hidden font-mono",title:r,children:r}),(0,s.jsx)("pre",{className:"overflow-hidden inline text-rose-500 line-through",title:n,children:n}),(0,s.jsx)("pre",{className:"overflow-hidden inline text-teal-500",title:l,children:l})]})}function E(){let e=(0,l.useRouter)(),[r,n]=(0,t.useState)("openrouter"),[d,N]=(0,t.useState)(""),[w,R]=(0,t.useState)(!1),O=(0,t.useRef)(new Map),I=(0,t.useRef)(""),E=k(),V=(0,i.I)({queryKey:["getExercises"],queryFn:()=>S()}),J=(0,o.mN)({resolver:(0,c.u)(b),defaultValues:{model:"anthropic/claude-sonnet-4",description:"",suite:"full",exercises:[],settings:void 0,concurrency:1,timeout:5}}),{setValue:$,clearErrors:B,watch:F,formState:{isSubmitting:L}}=J,[_,q,P]=F(["model","suite","settings","concurrency"]),[K,U]=(0,t.useState)(!1),[T,X]=(0,t.useState)(""),D=(0,t.useRef)(null),G=(0,t.useCallback)(async n=>{try{"openrouter"===r&&(n.settings={...n.settings||{},openRouterModelId:_});let{id:s}=await C({...n,systemPrompt:T});e.push("/runs/".concat(s))}catch(e){x.oR.error(e instanceof Error?e.message:"An unknown error occurred.")}},[r,_,e,T]),Z=(0,t.useCallback)((e,r)=>{var n;if(I.current!==r)for(let{obj:{id:e},score:n}of(I.current=r,O.current.clear(),u().go(r,E.data||[],{key:"name"})))O.current.set(e,n);return null!==(n=O.current.get(e))&&void 0!==n?n:0},[E.data]),Q=(0,t.useCallback)(e=>{$("model",e),R(!1)},[$]),W=(0,t.useCallback)(async e=>{var r,s,t;let l=null===(r=e.target.files)||void 0===r?void 0:r[0];if(l){B("settings");try{let{providerProfiles:r,globalSettings:i}=a.z.object({providerProfiles:a.z.object({currentApiConfigName:a.z.string(),apiConfigs:a.z.record(a.z.string(),y.AQ$)}),globalSettings:y.YZX}).parse(JSON.parse(await l.text())),o=null!==(s=r.apiConfigs[r.currentApiConfigName])&&void 0!==s?s:{};$("model",null!==(t=(0,y.XxZ)(o))&&void 0!==t?t:""),$("settings",{...y.Ur7,...o,...i}),n("settings"),e.target.value=""}catch(e){console.error(e),x.oR.error(e instanceof Error?e.message:"An unknown error occurred.")}}},[B,$]);return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(o.Op,{...J,children:(0,s.jsxs)("form",{onSubmit:J.handleSubmit(G),className:"flex flex-col justify-center divide-y divide-primary *:py-5",children:[(0,s.jsxs)("div",{className:"flex flex-row justify-between gap-4",children:["openrouter"===r&&(0,s.jsx)(A.zB,{control:J.control,name:"model",render:()=>{var e,r,n;return(0,s.jsxs)(A.eI,{className:"flex-1",children:[(0,s.jsxs)(A.AM,{open:w,onOpenChange:R,children:[(0,s.jsx)(A.Wv,{asChild:!0,children:(0,s.jsxs)(A.$n,{variant:"input",role:"combobox","aria-expanded":w,className:"flex items-center justify-between",children:[(0,s.jsx)("div",{children:(null===(r=E.data)||void 0===r?void 0:null===(e=r.find(e=>{let{id:r}=e;return r===_}))||void 0===e?void 0:e.name)||_||"Select OpenRouter Model"}),(0,s.jsx)(m.A,{className:"opacity-50"})]})}),(0,s.jsx)(A.hl,{className:"p-0 w-[var(--radix-popover-trigger-width)]",children:(0,s.jsxs)(A.uB,{filter:Z,children:[(0,s.jsx)(A.G7,{placeholder:"Search",value:d,onValueChange:N,className:"h-9"}),(0,s.jsxs)(A.oI,{children:[(0,s.jsx)(A.xL,{children:"No model found."}),(0,s.jsx)(A.L$,{children:null===(n=E.data)||void 0===n?void 0:n.map(e=>{let{id:r,name:n}=e;return(0,s.jsxs)(A.h_,{value:r,onSelect:Q,children:[n,(0,s.jsx)(h.A,{className:(0,z.cn)("ml-auto text-accent group-data-[selected=true]:text-accent-foreground size-4",r===_?"opacity-100":"opacity-0")})]},r)})})]})]})})]}),(0,s.jsx)(A.C5,{})]})}}),(0,s.jsxs)(A.eI,{className:"flex-1",children:[(0,s.jsxs)(A.$n,{type:"button",variant:"secondary",onClick:()=>{var e;return null===(e=document.getElementById("json-upload"))||void 0===e?void 0:e.click()},children:[(0,s.jsx)(j.A,{}),"Import Settings"]}),(0,s.jsx)("input",{id:"json-upload",type:"file",accept:"application/json",className:"hidden",onChange:W}),P&&(0,s.jsx)(A.FK,{className:"max-h-64 border rounded-sm",children:(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"flex items-center gap-1 p-2 border-b",children:[(0,s.jsx)(f.A,{className:"size-4 text-ring"}),(0,s.jsx)("div",{className:"text-sm",children:"Imported valid Roo Code settings. Showing differences from default settings."})]}),(0,s.jsx)(M,{defaultSettings:y.Ur7,customSettings:P})]})}),(0,s.jsx)(A.C5,{})]}),(0,s.jsxs)(A.$n,{type:"button",variant:"secondary",onClick:()=>U(!0),children:[(0,s.jsx)(p.A,{}),"Override System Prompt"]}),(0,s.jsx)(A.lG,{open:K,onOpenChange:U,children:(0,s.jsxs)(A.Cf,{children:[(0,s.jsx)(A.L3,{children:"Override System Prompt"}),(0,s.jsx)(A.TM,{ref:D,value:T,onChange:e=>X(e.target.value)}),(0,s.jsx)(A.Es,{children:(0,s.jsx)(A.$n,{onClick:()=>U(!1),children:"Done"})})]})})]}),(0,s.jsx)(A.zB,{control:J.control,name:"suite",render:()=>{var e;return(0,s.jsxs)(A.eI,{children:[(0,s.jsx)(A.lR,{children:"Exercises"}),(0,s.jsx)(A.tU,{defaultValue:"full",onValueChange:e=>$("suite",e),children:(0,s.jsxs)(A.j7,{children:[(0,s.jsx)(A.Xi,{value:"full",children:"All"}),(0,s.jsx)(A.Xi,{value:"partial",children:"Some"})]})}),"partial"===q&&(0,s.jsx)(A.KF,{options:(null===(e=V.data)||void 0===e?void 0:e.map(e=>({value:e,label:e})))||[],onValueChange:e=>$("exercises",e),placeholder:"Select",variant:"inverted",maxCount:4}),(0,s.jsx)(A.C5,{})]})}}),(0,s.jsx)(A.zB,{control:J.control,name:"concurrency",render:e=>{let{field:r}=e;return(0,s.jsxs)(A.eI,{children:[(0,s.jsx)(A.lR,{children:"Concurrency"}),(0,s.jsx)(A.MJ,{children:(0,s.jsxs)("div",{className:"flex flex-row items-center gap-2",children:[(0,s.jsx)(A.Ap,{defaultValue:[r.value],min:1,max:25,step:1,onValueChange:e=>r.onChange(e[0])}),(0,s.jsx)("div",{children:r.value})]})}),(0,s.jsx)(A.C5,{})]})}}),(0,s.jsx)(A.zB,{control:J.control,name:"timeout",render:e=>{let{field:r}=e;return(0,s.jsxs)(A.eI,{children:[(0,s.jsx)(A.lR,{children:"Timeout (Minutes)"}),(0,s.jsx)(A.MJ,{children:(0,s.jsxs)("div",{className:"flex flex-row items-center gap-2",children:[(0,s.jsx)(A.Ap,{defaultValue:[r.value],min:5,max:10,step:1,onValueChange:e=>r.onChange(e[0])}),(0,s.jsx)("div",{children:r.value})]})}),(0,s.jsx)(A.C5,{})]})}}),(0,s.jsx)(A.zB,{control:J.control,name:"description",render:e=>{let{field:r}=e;return(0,s.jsxs)(A.eI,{children:[(0,s.jsx)(A.lR,{children:"Description / Notes"}),(0,s.jsx)(A.MJ,{children:(0,s.jsx)(A.TM,{placeholder:"Optional",...r})}),(0,s.jsx)(A.C5,{})]})}}),(0,s.jsx)("div",{className:"flex justify-end",children:(0,s.jsxs)(A.$n,{size:"lg",type:"submit",disabled:L,children:[(0,s.jsx)(g.A,{className:"size-4"}),"Launch"]})})]})}),(0,s.jsx)(A.$n,{variant:"default",className:"absolute top-4 right-12 size-12 rounded-full",onClick:()=>e.push("/"),children:(0,s.jsx)(v.A,{className:"size-6"})})]})}}},e=>{var r=r=>e(e.s=r);e.O(0,[361,221,581,838,777,532,335,550,358],()=>r(541)),_N_E=e.O()}]);