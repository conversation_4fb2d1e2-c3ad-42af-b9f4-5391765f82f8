{"version": 3, "file": "chunk-C8lEn-DE.js", "sources": ["../../../../node_modules/.pnpm/@shikijs+langs@3.4.1/node_modules/@shikijs/langs/dist/qmldir.mjs"], "sourcesContent": ["const lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"QML Directory\\\",\\\"name\\\":\\\"qmldir\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#keywords\\\"},{\\\"include\\\":\\\"#version\\\"},{\\\"include\\\":\\\"#names\\\"}],\\\"repository\\\":{\\\"comment\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"#\\\",\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"comment.line.number-sign.qmldir\\\"}]},\\\"file-name\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b\\\\\\\\w+\\\\\\\\.(qmltypes|qml|js)\\\\\\\\b\\\",\\\"name\\\":\\\"string.unquoted.qmldir\\\"}]},\\\"identifier\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b\\\\\\\\w+\\\\\\\\b\\\",\\\"name\\\":\\\"variable.parameter.qmldir\\\"}]},\\\"keywords\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(module|singleton|internal|plugin|classname|typeinfo|depends|designersupported)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.qmldir\\\"}]},\\\"module-name\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b[A-Z]\\\\\\\\w*\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.type.qmldir\\\"}]},\\\"names\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#file-name\\\"},{\\\"include\\\":\\\"#module-name\\\"},{\\\"include\\\":\\\"#identifier\\\"}]},\\\"version\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b\\\\\\\\d+\\\\\\\\.\\\\\\\\d+\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.qml\\\"}]}},\\\"scopeName\\\":\\\"source.qmldir\\\"}\"))\n\nexport default [\nlang\n]\n"], "names": ["lang", "qmldir"], "mappings": "AAAA,MAAMA,EAAO,OAAO,OAAO,KAAK,MAAM,06BAAsiC,CAAC,EAE9jCC,EAAA,CACfD,CACA", "x_google_ignoreList": [0], "sourceRoot": ""}