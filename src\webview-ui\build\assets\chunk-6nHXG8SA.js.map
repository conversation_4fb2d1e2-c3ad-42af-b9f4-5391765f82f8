{"version": 3, "file": "chunk-6nHXG8SA.js", "sources": ["../../../../node_modules/.pnpm/@shikijs+langs@3.4.1/node_modules/@shikijs/langs/dist/bicep.mjs"], "sourcesContent": ["const lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Bicep\\\",\\\"fileTypes\\\":[\\\".bicep\\\",\\\".bicepparam\\\"],\\\"name\\\":\\\"bicep\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#comments\\\"}],\\\"repository\\\":{\\\"array-literal\\\":{\\\"begin\\\":\\\"\\\\\\\\[(?!(?:[\\\\\\\\t\\\\\\\\n\\\\\\\\r ]|/\\\\\\\\*(?:\\\\\\\\*(?!/)|[^*])*\\\\\\\\*/)*\\\\\\\\bfor\\\\\\\\b)\\\",\\\"end\\\":\\\"]\\\",\\\"name\\\":\\\"meta.array-literal.bicep\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#comments\\\"}]},\\\"block-comment\\\":{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.bicep\\\"},\\\"comments\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#line-comment\\\"},{\\\"include\\\":\\\"#block-comment\\\"}]},\\\"decorator\\\":{\\\"begin\\\":\\\"@(?:[\\\\\\\\t\\\\\\\\n\\\\\\\\r ]|/\\\\\\\\*(?:\\\\\\\\*(?!/)|[^*])*\\\\\\\\*/)*(?=\\\\\\\\b[$_[:alpha:]][$_[:alnum:]]*\\\\\\\\b)\\\",\\\"end\\\":\\\"\\\",\\\"name\\\":\\\"meta.decorator.bicep\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#comments\\\"}]},\\\"directive\\\":{\\\"begin\\\":\\\"#\\\\\\\\b[-0-9A-Z_a-z]+\\\\\\\\b\\\",\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"meta.directive.bicep\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#directive-variable\\\"},{\\\"include\\\":\\\"#comments\\\"}]},\\\"directive-variable\\\":{\\\"match\\\":\\\"\\\\\\\\b[-0-9A-Z_a-z]+\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.declaration.bicep\\\"},\\\"escape-character\\\":{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(u\\\\\\\\{\\\\\\\\h+}|['\\\\\\\\\\\\\\\\nrt]|\\\\\\\\$\\\\\\\\{)\\\",\\\"name\\\":\\\"constant.character.escape.bicep\\\"},\\\"expression\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#string-literal\\\"},{\\\"include\\\":\\\"#string-verbatim\\\"},{\\\"include\\\":\\\"#numeric-literal\\\"},{\\\"include\\\":\\\"#named-literal\\\"},{\\\"include\\\":\\\"#object-literal\\\"},{\\\"include\\\":\\\"#array-literal\\\"},{\\\"include\\\":\\\"#keyword\\\"},{\\\"include\\\":\\\"#identifier\\\"},{\\\"include\\\":\\\"#function-call\\\"},{\\\"include\\\":\\\"#decorator\\\"},{\\\"include\\\":\\\"#lambda-start\\\"},{\\\"include\\\":\\\"#directive\\\"}]},\\\"function-call\\\":{\\\"begin\\\":\\\"\\\\\\\\b([$_[:alpha:]][$_[:alnum:]]*)\\\\\\\\b(?:[\\\\\\\\t\\\\\\\\n\\\\\\\\r ]|/\\\\\\\\*(?:\\\\\\\\*(?!/)|[^*])*\\\\\\\\*/)*\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.bicep\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"name\\\":\\\"meta.function-call.bicep\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#comments\\\"}]},\\\"identifier\\\":{\\\"match\\\":\\\"\\\\\\\\b[$_[:alpha:]][$_[:alnum:]]*\\\\\\\\b(?!(?:[\\\\\\\\t\\\\\\\\n\\\\\\\\r ]|/\\\\\\\\*(?:\\\\\\\\*(?!/)|[^*])*\\\\\\\\*/)*\\\\\\\\()\\\",\\\"name\\\":\\\"variable.other.readwrite.bicep\\\"},\\\"keyword\\\":{\\\"match\\\":\\\"\\\\\\\\b(metadata|targetScope|resource|module|param|var|output|for|in|if|existing|import|as|type|with|using|extends|func|assert|extension)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.declaration.bicep\\\"},\\\"lambda-start\\\":{\\\"begin\\\":\\\"(\\\\\\\\((?:[\\\\\\\\t\\\\\\\\n\\\\\\\\r ]|/\\\\\\\\*(?:\\\\\\\\*(?!/)|[^*])*\\\\\\\\*/)*\\\\\\\\b[$_[:alpha:]][$_[:alnum:]]*\\\\\\\\b(?:[\\\\\\\\t\\\\\\\\n\\\\\\\\r ]|/\\\\\\\\*(?:\\\\\\\\*(?!/)|[^*])*\\\\\\\\*/)*(,(?:[\\\\\\\\t\\\\\\\\n\\\\\\\\r ]|/\\\\\\\\*(?:\\\\\\\\*(?!/)|[^*])*\\\\\\\\*/)*\\\\\\\\b[$_[:alpha:]][$_[:alnum:]]*\\\\\\\\b(?:[\\\\\\\\t\\\\\\\\n\\\\\\\\r ]|/\\\\\\\\*(?:\\\\\\\\*(?!/)|[^*])*\\\\\\\\*/)*)*\\\\\\\\)|\\\\\\\\((?:[\\\\\\\\t\\\\\\\\n\\\\\\\\r ]|/\\\\\\\\*(?:\\\\\\\\*(?!/)|[^*])*\\\\\\\\*/)*\\\\\\\\)|(?:[\\\\\\\\t\\\\\\\\n\\\\\\\\r ]|/\\\\\\\\*(?:\\\\\\\\*(?!/)|[^*])*\\\\\\\\*/)*\\\\\\\\b[$_[:alpha:]][$_[:alnum:]]*\\\\\\\\b(?:[\\\\\\\\t\\\\\\\\n\\\\\\\\r ]|/\\\\\\\\*(?:\\\\\\\\*(?!/)|[^*])*\\\\\\\\*/)*)(?=(?:[\\\\\\\\t\\\\\\\\n\\\\\\\\r ]|/\\\\\\\\*(?:\\\\\\\\*(?!/)|[^*])*\\\\\\\\*/)*=>)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.undefined.bicep\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#identifier\\\"},{\\\"include\\\":\\\"#comments\\\"}]}},\\\"end\\\":\\\"(?:[\\\\\\\\t\\\\\\\\n\\\\\\\\r ]|/\\\\\\\\*(?:\\\\\\\\*(?!/)|[^*])*\\\\\\\\*/)*=>\\\",\\\"name\\\":\\\"meta.lambda-start.bicep\\\"},\\\"line-comment\\\":{\\\"match\\\":\\\"//.*(?=$)\\\",\\\"name\\\":\\\"comment.line.double-slash.bicep\\\"},\\\"named-literal\\\":{\\\"match\\\":\\\"\\\\\\\\b(true|false|null)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.bicep\\\"},\\\"numeric-literal\\\":{\\\"match\\\":\\\"[0-9]+\\\",\\\"name\\\":\\\"constant.numeric.bicep\\\"},\\\"object-literal\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"end\\\":\\\"}\\\",\\\"name\\\":\\\"meta.object-literal.bicep\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#object-property-key\\\"},{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#comments\\\"}]},\\\"object-property-key\\\":{\\\"match\\\":\\\"\\\\\\\\b[$_[:alpha:]][$_[:alnum:]]*\\\\\\\\b(?=(?:[\\\\\\\\t\\\\\\\\n\\\\\\\\r ]|/\\\\\\\\*(?:\\\\\\\\*(?!/)|[^*])*\\\\\\\\*/)*:)\\\",\\\"name\\\":\\\"variable.other.property.bicep\\\"},\\\"string-literal\\\":{\\\"begin\\\":\\\"'(?!'')\\\",\\\"end\\\":\\\"'\\\",\\\"name\\\":\\\"string.quoted.single.bicep\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#escape-character\\\"},{\\\"include\\\":\\\"#string-literal-subst\\\"}]},\\\"string-literal-subst\\\":{\\\"begin\\\":\\\"(?<!\\\\\\\\\\\\\\\\)(\\\\\\\\$\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.template-expression.begin.bicep\\\"}},\\\"end\\\":\\\"(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.template-expression.end.bicep\\\"}},\\\"name\\\":\\\"meta.string-literal-subst.bicep\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#comments\\\"}]},\\\"string-verbatim\\\":{\\\"begin\\\":\\\"'''\\\",\\\"end\\\":\\\"'''(?!')\\\",\\\"name\\\":\\\"string.quoted.multi.bicep\\\",\\\"patterns\\\":[]}},\\\"scopeName\\\":\\\"source.bicep\\\"}\"))\n\nexport default [\nlang\n]\n"], "names": ["lang", "bicep"], "mappings": "AAAA,MAAMA,EAAO,OAAO,OAAO,KAAK,MAAM,kpIAAkkJ,CAAC,EAE1lJC,EAAA,CACfD,CACA", "x_google_ignoreList": [0], "sourceRoot": ""}