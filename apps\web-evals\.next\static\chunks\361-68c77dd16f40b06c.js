"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[361],{56:(e,t,r)=>{r.d(t,{Z:()=>o});var n=r(4545);function o(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},436:(e,t,r)=>{r.d(t,{A:()=>d});var n=r(4545);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),i=e=>{let t=a(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},s=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var u={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:o=24,strokeWidth:a=2,absoluteStrokeWidth:i,className:c="",children:d,iconNode:f,...p}=e;return(0,n.createElement)("svg",{ref:t,...u,width:o,height:o,stroke:r,strokeWidth:i?24*Number(a)/Number(o):a,className:l("lucide",c),...!d&&!s(p)&&{"aria-hidden":"true"},...p},[...f.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(d)?d:[d]])}),d=(e,t)=>{let r=(0,n.forwardRef)((r,a)=>{let{className:s,...u}=r;return(0,n.createElement)(c,{ref:a,iconNode:t,className:l("lucide-".concat(o(i(e))),"lucide-".concat(e),s),...u})});return r.displayName=i(e),r}},498:(e,t,r)=>{let n;r.d(t,{_s:()=>I});var o=r(5471),a=r(4545);let i=a.createContext({drawerRef:{current:null},overlayRef:{current:null},onPress:()=>{},onRelease:()=>{},onDrag:()=>{},onNestedDrag:()=>{},onNestedOpenChange:()=>{},onNestedRelease:()=>{},openProp:void 0,dismissible:!1,isOpen:!1,isDragging:!1,keyboardIsOpen:{current:!1},snapPointsOffset:null,snapPoints:null,handleOnly:!1,modal:!1,shouldFade:!1,activeSnapPoint:null,onOpenChange:()=>{},setActiveSnapPoint:()=>{},closeDrawer:()=>{},direction:"bottom",shouldAnimate:{current:!0},shouldScaleBackground:!1,setBackgroundColorOnScale:!0,noBodyStyles:!1,container:null,autoFocus:!1}),l=()=>{let e=a.useContext(i);if(!e)throw Error("useDrawerContext must be used within a Drawer.Root");return e};function s(){return/^((?!chrome|android).)*safari/i.test(navigator.userAgent)}function u(){return c(/^iPhone/)||c(/^iPad/)||c(/^Mac/)&&navigator.maxTouchPoints>1}function c(e){return"undefined"!=typeof window&&null!=window.navigator?e.test(window.navigator.platform):void 0}!function(e){if(!e||"undefined"==typeof document)return;let t=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css",t.appendChild(r),r.styleSheet?r.styleSheet.cssText=e:r.appendChild(document.createTextNode(e))}("[data-vaul-drawer]{touch-action:none;will-change:transform;transition:transform .5s cubic-bezier(.32, .72, 0, 1);animation-duration:.5s;animation-timing-function:cubic-bezier(0.32,0.72,0,1)}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=bottom][data-state=open]{animation-name:slideFromBottom}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=bottom][data-state=closed]{animation-name:slideToBottom}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=top][data-state=open]{animation-name:slideFromTop}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=top][data-state=closed]{animation-name:slideToTop}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=left][data-state=open]{animation-name:slideFromLeft}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=left][data-state=closed]{animation-name:slideToLeft}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=right][data-state=open]{animation-name:slideFromRight}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=right][data-state=closed]{animation-name:slideToRight}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=bottom]{transform:translate3d(0,var(--initial-transform,100%),0)}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=top]{transform:translate3d(0,calc(var(--initial-transform,100%) * -1),0)}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=left]{transform:translate3d(calc(var(--initial-transform,100%) * -1),0,0)}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=right]{transform:translate3d(var(--initial-transform,100%),0,0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=top]{transform:translate3d(0,var(--snap-point-height,0),0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=bottom]{transform:translate3d(0,var(--snap-point-height,0),0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=left]{transform:translate3d(var(--snap-point-height,0),0,0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=right]{transform:translate3d(var(--snap-point-height,0),0,0)}[data-vaul-overlay][data-vaul-snap-points=false]{animation-duration:.5s;animation-timing-function:cubic-bezier(0.32,0.72,0,1)}[data-vaul-overlay][data-vaul-snap-points=false][data-state=open]{animation-name:fadeIn}[data-vaul-overlay][data-state=closed]{animation-name:fadeOut}[data-vaul-animate=false]{animation:none!important}[data-vaul-overlay][data-vaul-snap-points=true]{opacity:0;transition:opacity .5s cubic-bezier(.32, .72, 0, 1)}[data-vaul-overlay][data-vaul-snap-points=true]{opacity:1}[data-vaul-drawer]:not([data-vaul-custom-container=true])::after{content:'';position:absolute;background:inherit;background-color:inherit}[data-vaul-drawer][data-vaul-drawer-direction=top]::after{top:initial;bottom:100%;left:0;right:0;height:200%}[data-vaul-drawer][data-vaul-drawer-direction=bottom]::after{top:100%;bottom:initial;left:0;right:0;height:200%}[data-vaul-drawer][data-vaul-drawer-direction=left]::after{left:initial;right:100%;top:0;bottom:0;width:200%}[data-vaul-drawer][data-vaul-drawer-direction=right]::after{left:100%;right:initial;top:0;bottom:0;width:200%}[data-vaul-overlay][data-vaul-snap-points=true]:not([data-vaul-snap-points-overlay=true]):not(\n[data-state=closed]\n){opacity:0}[data-vaul-overlay][data-vaul-snap-points-overlay=true]{opacity:1}[data-vaul-handle]{display:block;position:relative;opacity:.7;background:#e2e2e4;margin-left:auto;margin-right:auto;height:5px;width:32px;border-radius:1rem;touch-action:pan-y}[data-vaul-handle]:active,[data-vaul-handle]:hover{opacity:1}[data-vaul-handle-hitarea]{position:absolute;left:50%;top:50%;transform:translate(-50%,-50%);width:max(100%,2.75rem);height:max(100%,2.75rem);touch-action:inherit}@media (hover:hover) and (pointer:fine){[data-vaul-drawer]{user-select:none}}@media (pointer:fine){[data-vaul-handle-hitarea]:{width:100%;height:100%}}@keyframes fadeIn{from{opacity:0}to{opacity:1}}@keyframes fadeOut{to{opacity:0}}@keyframes slideFromBottom{from{transform:translate3d(0,var(--initial-transform,100%),0)}to{transform:translate3d(0,0,0)}}@keyframes slideToBottom{to{transform:translate3d(0,var(--initial-transform,100%),0)}}@keyframes slideFromTop{from{transform:translate3d(0,calc(var(--initial-transform,100%) * -1),0)}to{transform:translate3d(0,0,0)}}@keyframes slideToTop{to{transform:translate3d(0,calc(var(--initial-transform,100%) * -1),0)}}@keyframes slideFromLeft{from{transform:translate3d(calc(var(--initial-transform,100%) * -1),0,0)}to{transform:translate3d(0,0,0)}}@keyframes slideToLeft{to{transform:translate3d(calc(var(--initial-transform,100%) * -1),0,0)}}@keyframes slideFromRight{from{transform:translate3d(var(--initial-transform,100%),0,0)}to{transform:translate3d(0,0,0)}}@keyframes slideToRight{to{transform:translate3d(var(--initial-transform,100%),0,0)}}");let d="undefined"!=typeof window?a.useLayoutEffect:a.useEffect;function f(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(){for(var e=arguments.length,r=Array(e),n=0;n<e;n++)r[n]=arguments[n];for(let e of t)"function"==typeof e&&e(...r)}}let p="undefined"!=typeof document&&window.visualViewport;function m(e){let t=window.getComputedStyle(e);return/(auto|scroll)/.test(t.overflow+t.overflowX+t.overflowY)}function h(e){for(m(e)&&(e=e.parentElement);e&&!m(e);)e=e.parentElement;return e||document.scrollingElement||document.documentElement}let v=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]),g=0;function y(e,t,r,n){return e.addEventListener(t,r,n),()=>{e.removeEventListener(t,r,n)}}function b(e){let t=document.scrollingElement||document.documentElement;for(;e&&e!==t;){let t=h(e);if(t!==document.documentElement&&t!==document.body&&t!==e){let r=t.getBoundingClientRect().top,n=e.getBoundingClientRect().top;e.getBoundingClientRect().bottom>t.getBoundingClientRect().bottom+24&&(t.scrollTop+=n-r)}e=t.parentElement}}function w(e){return e instanceof HTMLInputElement&&!v.has(e.type)||e instanceof HTMLTextAreaElement||e instanceof HTMLElement&&e.isContentEditable}function x(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return a.useCallback(function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return e=>t.forEach(t=>{"function"==typeof t?t(e):null!=t&&(t.current=e)})}(...t),t)}let E=new WeakMap;function k(e,t){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(!e||!(e instanceof HTMLElement))return;let n={};Object.entries(t).forEach(t=>{let[r,o]=t;if(r.startsWith("--")){e.style.setProperty(r,o);return}n[r]=e.style[r],e.style[r]=o}),r||E.set(e,n)}let C=e=>{switch(e){case"top":case"bottom":return!0;case"left":case"right":return!1;default:return e}};function S(e,t){if(!e)return null;let r=window.getComputedStyle(e),n=r.transform||r.webkitTransform||r.mozTransform,o=n.match(/^matrix3d\((.+)\)$/);return o?parseFloat(o[1].split(", ")[C(t)?13:12]):(o=n.match(/^matrix\((.+)\)$/))?parseFloat(o[1].split(", ")[C(t)?5:4]):null}function R(e,t){if(!e)return()=>{};let r=e.style.cssText;return Object.assign(e.style,t),()=>{e.style.cssText=r}}let A={DURATION:.5,EASE:[.32,.72,0,1]},T="vaul-dragging";function D(e){let t=a.useRef(e);return a.useEffect(()=>{t.current=e}),a.useMemo(()=>function(){for(var e=arguments.length,r=Array(e),n=0;n<e;n++)r[n]=arguments[n];return null==t.current?void 0:t.current.call(t,...r)},[])}function j(e){let{prop:t,defaultProp:r,onChange:n=()=>{}}=e,[o,i]=function(e){let{defaultProp:t,onChange:r}=e,n=a.useState(t),[o]=n,i=a.useRef(o),l=D(r);return a.useEffect(()=>{i.current!==o&&(l(o),i.current=o)},[o,i,l]),n}({defaultProp:r,onChange:n}),l=void 0!==t,s=l?t:o,u=D(n);return[s,a.useCallback(e=>{if(l){let r="function"==typeof e?e(t):e;r!==t&&u(r)}else i(e)},[l,t,i,u])]}let M=()=>()=>{},P=null;function N(e){var t,r;let{open:l,onOpenChange:c,children:m,onDrag:v,onRelease:x,snapPoints:R,shouldScaleBackground:D=!1,setBackgroundColorOnScale:M=!0,closeThreshold:N=.25,scrollLockTimeout:L=100,dismissible:O=!0,handleOnly:_=!1,fadeFromIndex:I=R&&R.length-1,activeSnapPoint:F,setActiveSnapPoint:V,fixed:B,modal:z=!0,onClose:H,nested:W,noBodyStyles:U=!1,direction:G="bottom",defaultOpen:K=!1,disablePreventScroll:q=!0,snapToSequentialPoint:Y=!1,preventScrollRestoration:X=!1,repositionInputs:$=!0,onAnimationEnd:Z,container:J,autoFocus:Q=!1}=e,[ee=!1,et]=j({defaultProp:K,prop:l,onChange:e=>{null==c||c(e),e||W||eP(),setTimeout(()=>{null==Z||Z(e)},1e3*A.DURATION),e&&!z&&"undefined"!=typeof window&&window.requestAnimationFrame(()=>{document.body.style.pointerEvents="auto"}),e||(document.body.style.pointerEvents="auto")}}),[er,en]=a.useState(!1),[eo,ea]=a.useState(!1),[ei,el]=a.useState(!1),es=a.useRef(null),eu=a.useRef(null),ec=a.useRef(null),ed=a.useRef(null),ef=a.useRef(null),ep=a.useRef(!1),em=a.useRef(null),eh=a.useRef(0),ev=a.useRef(!1),eg=a.useRef(!K),ey=a.useRef(0),eb=a.useRef(null),ew=a.useRef((null==(t=eb.current)?void 0:t.getBoundingClientRect().height)||0),ex=a.useRef((null==(r=eb.current)?void 0:r.getBoundingClientRect().width)||0),eE=a.useRef(0),ek=a.useCallback(e=>{R&&e===eT.length-1&&(eu.current=new Date)},[]),{activeSnapPoint:eC,activeSnapPointIndex:eS,setActiveSnapPoint:eR,onRelease:eA,snapPointsOffset:eT,onDrag:eD,shouldFade:ej,getPercentageDragged:eM}=function(e){let{activeSnapPointProp:t,setActiveSnapPointProp:r,snapPoints:n,drawerRef:o,overlayRef:i,fadeFromIndex:l,onSnapPointChange:s,direction:u="bottom",container:c,snapToSequentialPoint:d}=e,[f,p]=j({prop:t,defaultProp:null==n?void 0:n[0],onChange:r}),[m,h]=a.useState("undefined"!=typeof window?{innerWidth:window.innerWidth,innerHeight:window.innerHeight}:void 0);a.useEffect(()=>{function e(){h({innerWidth:window.innerWidth,innerHeight:window.innerHeight})}return window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);let v=a.useMemo(()=>f===(null==n?void 0:n[n.length-1])||null,[n,f]),g=a.useMemo(()=>{var e;return null!=(e=null==n?void 0:n.findIndex(e=>e===f))?e:null},[n,f]),y=n&&n.length>0&&(l||0===l)&&!Number.isNaN(l)&&n[l]===f||!n,b=a.useMemo(()=>{var e;let t=c?{width:c.getBoundingClientRect().width,height:c.getBoundingClientRect().height}:"undefined"!=typeof window?{width:window.innerWidth,height:window.innerHeight}:{width:0,height:0};return null!=(e=null==n?void 0:n.map(e=>{let r="string"==typeof e,n=0;if(r&&(n=parseInt(e,10)),C(u)){let o=r?n:m?e*t.height:0;return m?"bottom"===u?t.height-o:-t.height+o:o}let o=r?n:m?e*t.width:0;return m?"right"===u?t.width-o:-t.width+o:o}))?e:[]},[n,m,c]),w=a.useMemo(()=>null!==g?null==b?void 0:b[g]:null,[b,g]),x=a.useCallback(e=>{var t;let r=null!=(t=null==b?void 0:b.findIndex(t=>t===e))?t:null;s(r),k(o.current,{transition:"transform ".concat(A.DURATION,"s cubic-bezier(").concat(A.EASE.join(","),")"),transform:C(u)?"translate3d(0, ".concat(e,"px, 0)"):"translate3d(".concat(e,"px, 0, 0)")}),b&&r!==b.length-1&&void 0!==l&&r!==l&&r<l?k(i.current,{transition:"opacity ".concat(A.DURATION,"s cubic-bezier(").concat(A.EASE.join(","),")"),opacity:"0"}):k(i.current,{transition:"opacity ".concat(A.DURATION,"s cubic-bezier(").concat(A.EASE.join(","),")"),opacity:"1"}),p(null==n?void 0:n[Math.max(r,0)])},[o.current,n,b,l,i,p]);return a.useEffect(()=>{if(f||t){var e;let r=null!=(e=null==n?void 0:n.findIndex(e=>e===t||e===f))?e:-1;b&&-1!==r&&"number"==typeof b[r]&&x(b[r])}},[f,t,n,b,x]),{isLastSnapPoint:v,activeSnapPoint:f,shouldFade:y,getPercentageDragged:function(e,t){if(!n||"number"!=typeof g||!b||void 0===l)return null;let r=g===l-1;if(g>=l&&t)return 0;if(r&&!t)return 1;if(!y&&!r)return null;let o=r?g+1:g-1,a=e/Math.abs(r?b[o]-b[o-1]:b[o+1]-b[o]);return r?1-a:a},setActiveSnapPoint:p,activeSnapPointIndex:g,onRelease:function(e){let{draggedDistance:t,closeDrawer:r,velocity:o,dismissible:a}=e;if(void 0===l)return;let s="bottom"===u||"right"===u?(null!=w?w:0)-t:(null!=w?w:0)+t,c=g===l-1,f=0===g,p=t>0;if(c&&k(i.current,{transition:"opacity ".concat(A.DURATION,"s cubic-bezier(").concat(A.EASE.join(","),")")}),!d&&o>2&&!p){a?r():x(b[0]);return}if(!d&&o>2&&p&&b&&n){x(b[n.length-1]);return}let m=null==b?void 0:b.reduce((e,t)=>"number"!=typeof e||"number"!=typeof t?e:Math.abs(t-s)<Math.abs(e-s)?t:e),h=C(u)?window.innerHeight:window.innerWidth;if(o>.4&&Math.abs(t)<.4*h){let e=p?1:-1;if(e>0&&v&&n){x(b[n.length-1]);return}if(f&&e<0&&a&&r(),null===g)return;x(b[g+e]);return}x(m)},onDrag:function(e){let{draggedDistance:t}=e;if(null===w)return;let r="bottom"===u||"right"===u?w-t:w+t;("bottom"!==u&&"right"!==u||!(r<b[b.length-1]))&&(("top"===u||"left"===u)&&r>b[b.length-1]||k(o.current,{transform:C(u)?"translate3d(0, ".concat(r,"px, 0)"):"translate3d(".concat(r,"px, 0, 0)")}))},snapPointsOffset:b}}({snapPoints:R,activeSnapPointProp:F,setActiveSnapPointProp:V,drawerRef:eb,fadeFromIndex:I,overlayRef:es,onSnapPointChange:ek,direction:G,container:J,snapToSequentialPoint:Y});!function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{isDisabled:t}=e;d(()=>{if(!t){var e,r,o;let t,a,i,l,s,c,d;return 1==++g&&u()&&(i=0,l=window.pageXOffset,s=window.pageYOffset,c=f((e=document.documentElement,r="paddingRight",o="".concat(window.innerWidth-document.documentElement.clientWidth,"px"),t=e.style[r],e.style[r]=o,()=>{e.style[r]=t})),window.scrollTo(0,0),d=f(y(document,"touchstart",e=>{((a=h(e.target))!==document.documentElement||a!==document.body)&&(i=e.changedTouches[0].pageY)},{passive:!1,capture:!0}),y(document,"touchmove",e=>{if(!a||a===document.documentElement||a===document.body){e.preventDefault();return}let t=e.changedTouches[0].pageY,r=a.scrollTop,n=a.scrollHeight-a.clientHeight;0!==n&&((r<=0&&t>i||r>=n&&t<i)&&e.preventDefault(),i=t)},{passive:!1,capture:!0}),y(document,"touchend",e=>{let t=e.target;w(t)&&t!==document.activeElement&&(e.preventDefault(),t.style.transform="translateY(-2000px)",t.focus(),requestAnimationFrame(()=>{t.style.transform=""}))},{passive:!1,capture:!0}),y(document,"focus",e=>{let t=e.target;w(t)&&(t.style.transform="translateY(-2000px)",requestAnimationFrame(()=>{t.style.transform="",p&&(p.height<window.innerHeight?requestAnimationFrame(()=>{b(t)}):p.addEventListener("resize",()=>b(t),{once:!0}))}))},!0),y(window,"scroll",()=>{window.scrollTo(0,0)})),n=()=>{c(),d(),window.scrollTo(l,s)}),()=>{0==--g&&(null==n||n())}}},[t])}({isDisabled:!ee||eo||!z||ei||!er||!$||!q});let{restorePositionSetting:eP}=function(e){let{isOpen:t,modal:r,nested:n,hasBeenOpened:o,preventScrollRestoration:i,noBodyStyles:l}=e,[u,c]=a.useState(()=>"undefined"!=typeof window?window.location.href:""),d=a.useRef(0),f=a.useCallback(()=>{if(s()&&null===P&&t&&!l){P={position:document.body.style.position,top:document.body.style.top,left:document.body.style.left,height:document.body.style.height,right:"unset"};let{scrollX:e,innerHeight:t}=window;document.body.style.setProperty("position","fixed","important"),Object.assign(document.body.style,{top:"".concat(-d.current,"px"),left:"".concat(-e,"px"),right:"0px",height:"auto"}),window.setTimeout(()=>window.requestAnimationFrame(()=>{let e=t-window.innerHeight;e&&d.current>=t&&(document.body.style.top="".concat(-(d.current+e),"px"))}),300)}},[t]),p=a.useCallback(()=>{if(s()&&null!==P&&!l){let e=-parseInt(document.body.style.top,10),t=-parseInt(document.body.style.left,10);Object.assign(document.body.style,P),window.requestAnimationFrame(()=>{if(i&&u!==window.location.href){c(window.location.href);return}window.scrollTo(t,e)}),P=null}},[u]);return a.useEffect(()=>{function e(){d.current=window.scrollY}return e(),window.addEventListener("scroll",e),()=>{window.removeEventListener("scroll",e)}},[]),a.useEffect(()=>{if(r)return()=>{"undefined"!=typeof document&&(document.querySelector("[data-vaul-drawer]")||p())}},[r,p]),a.useEffect(()=>{n||!o||(t?(window.matchMedia("(display-mode: standalone)").matches||f(),r||window.setTimeout(()=>{p()},500)):p())},[t,o,u,r,n,f,p]),{restorePositionSetting:p}}({isOpen:ee,modal:z,nested:null!=W&&W,hasBeenOpened:er,preventScrollRestoration:X,noBodyStyles:U});function eN(){return(window.innerWidth-26)/window.innerWidth}function eL(e,t){var r;let n=e,o=null==(r=window.getSelection())?void 0:r.toString(),a=eb.current?S(eb.current,G):null,i=new Date;if("SELECT"===n.tagName||n.hasAttribute("data-vaul-no-drag")||n.closest("[data-vaul-no-drag]"))return!1;if("right"===G||"left"===G)return!0;if(eu.current&&i.getTime()-eu.current.getTime()<500)return!1;if(null!==a&&("bottom"===G?a>0:a<0))return!0;if(o&&o.length>0)return!1;if(ef.current&&i.getTime()-ef.current.getTime()<L&&0===a||t)return ef.current=i,!1;for(;n;){if(n.scrollHeight>n.clientHeight){if(0!==n.scrollTop)return ef.current=new Date,!1;if("dialog"===n.getAttribute("role"))break}n=n.parentNode}return!0}function eO(e){eo&&eb.current&&(eb.current.classList.remove(T),ep.current=!1,ea(!1),ed.current=new Date),null==H||H(),e||et(!1),setTimeout(()=>{R&&eR(R[0])},1e3*A.DURATION)}function e_(){if(!eb.current)return;let e=document.querySelector("[data-vaul-drawer-wrapper]"),t=S(eb.current,G);k(eb.current,{transform:"translate3d(0, 0, 0)",transition:"transform ".concat(A.DURATION,"s cubic-bezier(").concat(A.EASE.join(","),")")}),k(es.current,{transition:"opacity ".concat(A.DURATION,"s cubic-bezier(").concat(A.EASE.join(","),")"),opacity:"1"}),D&&t&&t>0&&ee&&k(e,{borderRadius:"".concat(8,"px"),overflow:"hidden",...C(G)?{transform:"scale(".concat(eN(),") translate3d(0, calc(env(safe-area-inset-top) + 14px), 0)"),transformOrigin:"top"}:{transform:"scale(".concat(eN(),") translate3d(calc(env(safe-area-inset-top) + 14px), 0, 0)"),transformOrigin:"left"},transitionProperty:"transform, border-radius",transitionDuration:"".concat(A.DURATION,"s"),transitionTimingFunction:"cubic-bezier(".concat(A.EASE.join(","),")")},!0)}return a.useEffect(()=>{window.requestAnimationFrame(()=>{eg.current=!0})},[]),a.useEffect(()=>{var e;function t(){if(eb.current&&$&&(w(document.activeElement)||ev.current)){var e;let t=(null==(e=window.visualViewport)?void 0:e.height)||0,r=window.innerHeight,n=r-t,o=eb.current.getBoundingClientRect().height||0;eE.current||(eE.current=o);let a=eb.current.getBoundingClientRect().top;if(Math.abs(ey.current-n)>60&&(ev.current=!ev.current),R&&R.length>0&&eT&&eS&&(n+=eT[eS]||0),ey.current=n,o>t||ev.current){let e=eb.current.getBoundingClientRect().height,i=e;e>t&&(i=t-(o>.8*r?a:26)),B?eb.current.style.height="".concat(e-Math.max(n,0),"px"):eb.current.style.height="".concat(Math.max(i,t-a),"px")}else!function(){let e=navigator.userAgent;return"undefined"!=typeof window&&(/Firefox/.test(e)&&/Mobile/.test(e)||/FxiOS/.test(e))}()&&(eb.current.style.height="".concat(eE.current,"px"));R&&R.length>0&&!ev.current?eb.current.style.bottom="0px":eb.current.style.bottom="".concat(Math.max(n,0),"px")}}return null==(e=window.visualViewport)||e.addEventListener("resize",t),()=>{var e;return null==(e=window.visualViewport)?void 0:e.removeEventListener("resize",t)}},[eS,R,eT]),a.useEffect(()=>(ee&&(k(document.documentElement,{scrollBehavior:"auto"}),eu.current=new Date),()=>{!function(e,t){if(!e||!(e instanceof HTMLElement))return;let r=E.get(e);r&&(e.style[t]=r[t])}(document.documentElement,"scrollBehavior")}),[ee]),a.useEffect(()=>{z||window.requestAnimationFrame(()=>{document.body.style.pointerEvents="auto"})},[z]),a.createElement(o.bL,{defaultOpen:K,onOpenChange:e=>{(O||e)&&(e?en(!0):eO(!0),et(e))},open:ee},a.createElement(i.Provider,{value:{activeSnapPoint:eC,snapPoints:R,setActiveSnapPoint:eR,drawerRef:eb,overlayRef:es,onOpenChange:c,onPress:function(e){var t,r;(O||R)&&(!eb.current||eb.current.contains(e.target))&&(ew.current=(null==(t=eb.current)?void 0:t.getBoundingClientRect().height)||0,ex.current=(null==(r=eb.current)?void 0:r.getBoundingClientRect().width)||0,ea(!0),ec.current=new Date,u()&&window.addEventListener("touchend",()=>ep.current=!1,{once:!0}),e.target.setPointerCapture(e.pointerId),eh.current=C(G)?e.pageY:e.pageX)},onRelease:function(e){var t,r;if(!eo||!eb.current)return;eb.current.classList.remove(T),ep.current=!1,ea(!1),ed.current=new Date;let n=S(eb.current,G);if(!e||!eL(e.target,!1)||!n||Number.isNaN(n)||null===ec.current)return;let o=ed.current.getTime()-ec.current.getTime(),a=eh.current-(C(G)?e.pageY:e.pageX),i=Math.abs(a)/o;if(i>.05&&(el(!0),setTimeout(()=>{el(!1)},200)),R){eA({draggedDistance:a*("bottom"===G||"right"===G?1:-1),closeDrawer:eO,velocity:i,dismissible:O}),null==x||x(e,!0);return}if("bottom"===G||"right"===G?a>0:a<0){e_(),null==x||x(e,!0);return}if(i>.4){eO(),null==x||x(e,!1);return}let l=Math.min(null!=(t=eb.current.getBoundingClientRect().height)?t:0,window.innerHeight),s=Math.min(null!=(r=eb.current.getBoundingClientRect().width)?r:0,window.innerWidth);if(Math.abs(n)>=("left"===G||"right"===G?s:l)*N){eO(),null==x||x(e,!1);return}null==x||x(e,!0),e_()},onDrag:function(e){if(eb.current&&eo){let t="bottom"===G||"right"===G?1:-1,r=(eh.current-(C(G)?e.pageY:e.pageX))*t,n=r>0,o=R&&!O&&!n;if(o&&0===eS)return;let a=Math.abs(r),i=document.querySelector("[data-vaul-drawer-wrapper]"),l=a/("bottom"===G||"top"===G?ew.current:ex.current),s=eM(a,n);if(null!==s&&(l=s),o&&l>=1||!ep.current&&!eL(e.target,n))return;if(eb.current.classList.add(T),ep.current=!0,k(eb.current,{transition:"none"}),k(es.current,{transition:"none"}),R&&eD({draggedDistance:r}),n&&!R){let e=Math.min(-(8*(Math.log(r+1)-2)*1),0)*t;k(eb.current,{transform:C(G)?"translate3d(0, ".concat(e,"px, 0)"):"translate3d(".concat(e,"px, 0, 0)")});return}let u=1-l;if((ej||I&&eS===I-1)&&(null==v||v(e,l),k(es.current,{opacity:"".concat(u),transition:"none"},!0)),i&&es.current&&D){let e=Math.min(eN()+l*(1-eN()),1),t=8-8*l,r=Math.max(0,14-14*l);k(i,{borderRadius:"".concat(t,"px"),transform:C(G)?"scale(".concat(e,") translate3d(0, ").concat(r,"px, 0)"):"scale(".concat(e,") translate3d(").concat(r,"px, 0, 0)"),transition:"none"},!0)}if(!R){let e=a*t;k(eb.current,{transform:C(G)?"translate3d(0, ".concat(e,"px, 0)"):"translate3d(".concat(e,"px, 0, 0)")})}}},dismissible:O,shouldAnimate:eg,handleOnly:_,isOpen:ee,isDragging:eo,shouldFade:ej,closeDrawer:eO,onNestedDrag:function(e,t){if(t<0)return;let r=(window.innerWidth-16)/window.innerWidth,n=r+t*(1-r),o=-16+16*t;k(eb.current,{transform:C(G)?"scale(".concat(n,") translate3d(0, ").concat(o,"px, 0)"):"scale(".concat(n,") translate3d(").concat(o,"px, 0, 0)"),transition:"none"})},onNestedOpenChange:function(e){let t=e?(window.innerWidth-16)/window.innerWidth:1,r=e?-16:0;em.current&&window.clearTimeout(em.current),k(eb.current,{transition:"transform ".concat(A.DURATION,"s cubic-bezier(").concat(A.EASE.join(","),")"),transform:C(G)?"scale(".concat(t,") translate3d(0, ").concat(r,"px, 0)"):"scale(".concat(t,") translate3d(").concat(r,"px, 0, 0)")}),!e&&eb.current&&(em.current=setTimeout(()=>{let e=S(eb.current,G);k(eb.current,{transition:"none",transform:C(G)?"translate3d(0, ".concat(e,"px, 0)"):"translate3d(".concat(e,"px, 0, 0)")})},500))},onNestedRelease:function(e,t){let r=C(G)?window.innerHeight:window.innerWidth,n=t?(r-16)/r:1,o=t?-16:0;t&&k(eb.current,{transition:"transform ".concat(A.DURATION,"s cubic-bezier(").concat(A.EASE.join(","),")"),transform:C(G)?"scale(".concat(n,") translate3d(0, ").concat(o,"px, 0)"):"scale(".concat(n,") translate3d(").concat(o,"px, 0, 0)")})},keyboardIsOpen:ev,modal:z,snapPointsOffset:eT,activeSnapPointIndex:eS,direction:G,shouldScaleBackground:D,setBackgroundColorOnScale:M,noBodyStyles:U,container:J,autoFocus:Q}},m))}let L=a.forwardRef(function(e,t){let{...r}=e,{overlayRef:n,snapPoints:i,onRelease:s,shouldFade:u,isOpen:c,modal:d,shouldAnimate:f}=l(),p=x(t,n),m=i&&i.length>0;if(!d)return null;let h=a.useCallback(e=>s(e),[s]);return a.createElement(o.hJ,{onMouseUp:h,ref:p,"data-vaul-overlay":"","data-vaul-snap-points":c&&m?"true":"false","data-vaul-snap-points-overlay":c&&u?"true":"false","data-vaul-animate":(null==f?void 0:f.current)?"true":"false",...r})});L.displayName="Drawer.Overlay";let O=a.forwardRef(function(e,t){let{onPointerDownOutside:r,style:n,onOpenAutoFocus:i,...s}=e,{drawerRef:u,onPress:c,onRelease:d,onDrag:f,keyboardIsOpen:p,snapPointsOffset:m,activeSnapPointIndex:h,modal:v,isOpen:g,direction:y,snapPoints:b,container:w,handleOnly:E,shouldAnimate:k,autoFocus:S}=l(),[T,D]=a.useState(!1),j=x(t,u),P=a.useRef(null),N=a.useRef(null),L=a.useRef(!1),O=b&&b.length>0,{direction:_,isOpen:I,shouldScaleBackground:F,setBackgroundColorOnScale:V,noBodyStyles:B}=l(),z=a.useRef(null),H=(0,a.useMemo)(()=>document.body.style.backgroundColor,[]);function W(){return(window.innerWidth-26)/window.innerWidth}a.useEffect(()=>{if(I&&F){z.current&&clearTimeout(z.current);let e=document.querySelector("[data-vaul-drawer-wrapper]")||document.querySelector("[vaul-drawer-wrapper]");if(!e)return;!function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r]}(V&&!B?R(document.body,{background:"black"}):M,R(e,{transformOrigin:C(_)?"top":"left",transitionProperty:"transform, border-radius",transitionDuration:"".concat(A.DURATION,"s"),transitionTimingFunction:"cubic-bezier(".concat(A.EASE.join(","),")")}));let t=R(e,{borderRadius:"".concat(8,"px"),overflow:"hidden",...C(_)?{transform:"scale(".concat(W(),") translate3d(0, calc(env(safe-area-inset-top) + 14px), 0)")}:{transform:"scale(".concat(W(),") translate3d(calc(env(safe-area-inset-top) + 14px), 0, 0)")}});return()=>{t(),z.current=window.setTimeout(()=>{H?document.body.style.background=H:document.body.style.removeProperty("background")},1e3*A.DURATION)}}},[I,F,H]);let U=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;if(L.current)return!0;let n=Math.abs(e.y),o=Math.abs(e.x),a=o>n,i=["bottom","right"].includes(t)?1:-1;if("left"===t||"right"===t){if(!(e.x*i<0)&&o>=0&&o<=r)return a}else if(!(e.y*i<0)&&n>=0&&n<=r)return!a;return L.current=!0,!0};function G(e){P.current=null,L.current=!1,d(e)}return a.useEffect(()=>{O&&window.requestAnimationFrame(()=>{D(!0)})},[]),a.createElement(o.UC,{"data-vaul-drawer-direction":y,"data-vaul-drawer":"","data-vaul-delayed-snap-points":T?"true":"false","data-vaul-snap-points":g&&O?"true":"false","data-vaul-custom-container":w?"true":"false","data-vaul-animate":(null==k?void 0:k.current)?"true":"false",...s,ref:j,style:m&&m.length>0?{"--snap-point-height":"".concat(m[null!=h?h:0],"px"),...n}:n,onPointerDown:e=>{E||(null==s.onPointerDown||s.onPointerDown.call(s,e),P.current={x:e.pageX,y:e.pageY},c(e))},onOpenAutoFocus:e=>{null==i||i(e),S||e.preventDefault()},onPointerDownOutside:e=>{if(null==r||r(e),!v||e.defaultPrevented){e.preventDefault();return}p.current&&(p.current=!1)},onFocusOutside:e=>{if(!v){e.preventDefault();return}},onPointerMove:e=>{if(N.current=e,E||(null==s.onPointerMove||s.onPointerMove.call(s,e),!P.current))return;let t=e.pageY-P.current.y,r=e.pageX-P.current.x,n="touch"===e.pointerType?10:2;U({x:r,y:t},y,n)?f(e):(Math.abs(r)>n||Math.abs(t)>n)&&(P.current=null)},onPointerUp:e=>{null==s.onPointerUp||s.onPointerUp.call(s,e),P.current=null,L.current=!1,d(e)},onPointerOut:e=>{null==s.onPointerOut||s.onPointerOut.call(s,e),G(N.current)},onContextMenu:e=>{null==s.onContextMenu||s.onContextMenu.call(s,e),N.current&&G(N.current)}})});O.displayName="Drawer.Content";let _=a.forwardRef(function(e,t){let{preventCycle:r=!1,children:n,...o}=e,{closeDrawer:i,isDragging:s,snapPoints:u,activeSnapPoint:c,setActiveSnapPoint:d,dismissible:f,handleOnly:p,isOpen:m,onPress:h,onDrag:v}=l(),g=a.useRef(null),y=a.useRef(!1);function b(){g.current&&window.clearTimeout(g.current),y.current=!1}return a.createElement("div",{onClick:function(){if(y.current){b();return}window.setTimeout(()=>{!function(){if(s||r||y.current){b();return}if(b(),!u||0===u.length){f||i();return}if(c===u[u.length-1]&&f){i();return}let e=u.findIndex(e=>e===c);-1!==e&&d(u[e+1])}()},120)},onPointerCancel:b,onPointerDown:e=>{p&&h(e),g.current=window.setTimeout(()=>{y.current=!0},250)},onPointerMove:e=>{p&&v(e)},ref:t,"data-vaul-drawer-visible":m?"true":"false","data-vaul-handle":"","aria-hidden":"true",...o},a.createElement("span",{"data-vaul-handle-hitarea":"","aria-hidden":"true"},n))});_.displayName="Drawer.Handle";let I={Root:N,NestedRoot:function(e){let{onDrag:t,onOpenChange:r,open:n,...o}=e,{onNestedDrag:i,onNestedOpenChange:s,onNestedRelease:u}=l();if(!i)throw Error("Drawer.NestedRoot must be placed in another drawer");return a.createElement(N,{nested:!0,open:n,onClose:()=>{s(!1)},onDrag:(e,r)=>{i(e,r),null==t||t(e,r)},onOpenChange:e=>{e&&s(e),null==r||r(e)},onRelease:u,...o})},Content:O,Overlay:L,Trigger:o.l9,Portal:function(e){let t=l(),{container:r=t.container,...n}=e;return a.createElement(o.ZL,{container:r,...n})},Handle:_,Close:o.bm,Title:o.hE,Description:o.VY}},502:(e,t,r)=>{r.r(t),r.d(t,{ThemeProvider:()=>d,useTheme:()=>c});var n=r(4545),o=(e,t,r,n,o,a,i,l)=>{let s=document.documentElement,u=["light","dark"];function c(t){var r;(Array.isArray(e)?e:[e]).forEach(e=>{let r="class"===e,n=r&&a?o.map(e=>a[e]||e):o;r?(s.classList.remove(...n),s.classList.add(a&&a[t]?a[t]:t)):s.setAttribute(e,t)}),r=t,l&&u.includes(r)&&(s.style.colorScheme=r)}if(n)c(n);else try{let e=localStorage.getItem(t)||r,n=i&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;c(n)}catch(e){}},a=["light","dark"],i="(prefers-color-scheme: dark)",l="undefined"==typeof window,s=n.createContext(void 0),u={setTheme:e=>{},themes:[]},c=()=>{var e;return null!=(e=n.useContext(s))?e:u},d=e=>n.useContext(s)?n.createElement(n.Fragment,null,e.children):n.createElement(p,{...e}),f=["light","dark"],p=e=>{let{forcedTheme:t,disableTransitionOnChange:r=!1,enableSystem:o=!0,enableColorScheme:l=!0,storageKey:u="theme",themes:c=f,defaultTheme:d=o?"system":"light",attribute:p="data-theme",value:y,children:b,nonce:w,scriptProps:x}=e,[E,k]=n.useState(()=>h(u,d)),[C,S]=n.useState(()=>"system"===E?g():E),R=y?Object.values(y):c,A=n.useCallback(e=>{let t=e;if(!t)return;"system"===e&&o&&(t=g());let n=y?y[t]:t,i=r?v(w):null,s=document.documentElement,u=e=>{"class"===e?(s.classList.remove(...R),n&&s.classList.add(n)):e.startsWith("data-")&&(n?s.setAttribute(e,n):s.removeAttribute(e))};if(Array.isArray(p)?p.forEach(u):u(p),l){let e=a.includes(d)?d:null,r=a.includes(t)?t:e;s.style.colorScheme=r}null==i||i()},[w]),T=n.useCallback(e=>{let t="function"==typeof e?e(E):e;k(t);try{localStorage.setItem(u,t)}catch(e){}},[E]),D=n.useCallback(e=>{S(g(e)),"system"===E&&o&&!t&&A("system")},[E,t]);n.useEffect(()=>{let e=window.matchMedia(i);return e.addListener(D),D(e),()=>e.removeListener(D)},[D]),n.useEffect(()=>{let e=e=>{e.key===u&&(e.newValue?k(e.newValue):T(d))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[T]),n.useEffect(()=>{A(null!=t?t:E)},[t,E]);let j=n.useMemo(()=>({theme:E,setTheme:T,forcedTheme:t,resolvedTheme:"system"===E?C:E,themes:o?[...c,"system"]:c,systemTheme:o?C:void 0}),[E,T,t,C,o,c]);return n.createElement(s.Provider,{value:j},n.createElement(m,{forcedTheme:t,storageKey:u,attribute:p,enableSystem:o,enableColorScheme:l,defaultTheme:d,value:y,themes:c,nonce:w,scriptProps:x}),b)},m=n.memo(e=>{let{forcedTheme:t,storageKey:r,attribute:a,enableSystem:i,enableColorScheme:l,defaultTheme:s,value:u,themes:c,nonce:d,scriptProps:f}=e,p=JSON.stringify([a,r,s,t,c,u,i,l]).slice(1,-1);return n.createElement("script",{...f,suppressHydrationWarning:!0,nonce:"undefined"==typeof window?d:"",dangerouslySetInnerHTML:{__html:"(".concat(o.toString(),")(").concat(p,")")}})}),h=(e,t)=>{let r;if(!l){try{r=localStorage.getItem(e)||void 0}catch(e){}return r||t}},v=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},g=e=>(e||(e=window.matchMedia(i)),e.matches?"dark":"light")},536:(e,t,r)=>{r.d(t,{Mz:()=>e$,i3:()=>eJ,UC:()=>eZ,bL:()=>eX,Bk:()=>eN});var n=r(4545);let o=["top","right","bottom","left"],a=Math.min,i=Math.max,l=Math.round,s=Math.floor,u=e=>({x:e,y:e}),c={left:"right",right:"left",bottom:"top",top:"bottom"},d={start:"end",end:"start"};function f(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function m(e){return e.split("-")[1]}function h(e){return"x"===e?"y":"x"}function v(e){return"y"===e?"height":"width"}function g(e){return["top","bottom"].includes(p(e))?"y":"x"}function y(e){return e.replace(/start|end/g,e=>d[e])}function b(e){return e.replace(/left|right|bottom|top/g,e=>c[e])}function w(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function x(e){let{x:t,y:r,width:n,height:o}=e;return{width:n,height:o,top:r,left:t,right:t+n,bottom:r+o,x:t,y:r}}function E(e,t,r){let n,{reference:o,floating:a}=e,i=g(t),l=h(g(t)),s=v(l),u=p(t),c="y"===i,d=o.x+o.width/2-a.width/2,f=o.y+o.height/2-a.height/2,y=o[s]/2-a[s]/2;switch(u){case"top":n={x:d,y:o.y-a.height};break;case"bottom":n={x:d,y:o.y+o.height};break;case"right":n={x:o.x+o.width,y:f};break;case"left":n={x:o.x-a.width,y:f};break;default:n={x:o.x,y:o.y}}switch(m(t)){case"start":n[l]-=y*(r&&c?-1:1);break;case"end":n[l]+=y*(r&&c?-1:1)}return n}let k=async(e,t,r)=>{let{placement:n="bottom",strategy:o="absolute",middleware:a=[],platform:i}=r,l=a.filter(Boolean),s=await (null==i.isRTL?void 0:i.isRTL(t)),u=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:d}=E(u,n,s),f=n,p={},m=0;for(let r=0;r<l.length;r++){let{name:a,fn:h}=l[r],{x:v,y:g,data:y,reset:b}=await h({x:c,y:d,initialPlacement:n,placement:f,strategy:o,middlewareData:p,rects:u,platform:i,elements:{reference:e,floating:t}});c=null!=v?v:c,d=null!=g?g:d,p={...p,[a]:{...p[a],...y}},b&&m<=50&&(m++,"object"==typeof b&&(b.placement&&(f=b.placement),b.rects&&(u=!0===b.rects?await i.getElementRects({reference:e,floating:t,strategy:o}):b.rects),{x:c,y:d}=E(u,f,s)),r=-1)}return{x:c,y:d,placement:f,strategy:o,middlewareData:p}};async function C(e,t){var r;void 0===t&&(t={});let{x:n,y:o,platform:a,rects:i,elements:l,strategy:s}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:p=!1,padding:m=0}=f(t,e),h=w(m),v=l[p?"floating"===d?"reference":"floating":d],g=x(await a.getClippingRect({element:null==(r=await (null==a.isElement?void 0:a.isElement(v)))||r?v:v.contextElement||await (null==a.getDocumentElement?void 0:a.getDocumentElement(l.floating)),boundary:u,rootBoundary:c,strategy:s})),y="floating"===d?{x:n,y:o,width:i.floating.width,height:i.floating.height}:i.reference,b=await (null==a.getOffsetParent?void 0:a.getOffsetParent(l.floating)),E=await (null==a.isElement?void 0:a.isElement(b))&&await (null==a.getScale?void 0:a.getScale(b))||{x:1,y:1},k=x(a.convertOffsetParentRelativeRectToViewportRelativeRect?await a.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:y,offsetParent:b,strategy:s}):y);return{top:(g.top-k.top+h.top)/E.y,bottom:(k.bottom-g.bottom+h.bottom)/E.y,left:(g.left-k.left+h.left)/E.x,right:(k.right-g.right+h.right)/E.x}}function S(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function R(e){return o.some(t=>e[t]>=0)}async function A(e,t){let{placement:r,platform:n,elements:o}=e,a=await (null==n.isRTL?void 0:n.isRTL(o.floating)),i=p(r),l=m(r),s="y"===g(r),u=["left","top"].includes(i)?-1:1,c=a&&s?-1:1,d=f(t,e),{mainAxis:h,crossAxis:v,alignmentAxis:y}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return l&&"number"==typeof y&&(v="end"===l?-+y:y),s?{x:v*c,y:h*u}:{x:h*u,y:v*c}}function T(){return"undefined"!=typeof window}function D(e){return P(e)?(e.nodeName||"").toLowerCase():"#document"}function j(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function M(e){var t;return null==(t=(P(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function P(e){return!!T()&&(e instanceof Node||e instanceof j(e).Node)}function N(e){return!!T()&&(e instanceof Element||e instanceof j(e).Element)}function L(e){return!!T()&&(e instanceof HTMLElement||e instanceof j(e).HTMLElement)}function O(e){return!!T()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof j(e).ShadowRoot)}function _(e){let{overflow:t,overflowX:r,overflowY:n,display:o}=z(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+r)&&!["inline","contents"].includes(o)}function I(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function F(e){let t=V(),r=N(e)?z(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!r[e]&&"none"!==r[e])||!!r.containerType&&"normal"!==r.containerType||!t&&!!r.backdropFilter&&"none"!==r.backdropFilter||!t&&!!r.filter&&"none"!==r.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(r.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(r.contain||"").includes(e))}function V(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function B(e){return["html","body","#document"].includes(D(e))}function z(e){return j(e).getComputedStyle(e)}function H(e){return N(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function W(e){if("html"===D(e))return e;let t=e.assignedSlot||e.parentNode||O(e)&&e.host||M(e);return O(t)?t.host:t}function U(e,t,r){var n;void 0===t&&(t=[]),void 0===r&&(r=!0);let o=function e(t){let r=W(t);return B(r)?t.ownerDocument?t.ownerDocument.body:t.body:L(r)&&_(r)?r:e(r)}(e),a=o===(null==(n=e.ownerDocument)?void 0:n.body),i=j(o);if(a){let e=G(i);return t.concat(i,i.visualViewport||[],_(o)?o:[],e&&r?U(e):[])}return t.concat(o,U(o,[],r))}function G(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function K(e){let t=z(e),r=parseFloat(t.width)||0,n=parseFloat(t.height)||0,o=L(e),a=o?e.offsetWidth:r,i=o?e.offsetHeight:n,s=l(r)!==a||l(n)!==i;return s&&(r=a,n=i),{width:r,height:n,$:s}}function q(e){return N(e)?e:e.contextElement}function Y(e){let t=q(e);if(!L(t))return u(1);let r=t.getBoundingClientRect(),{width:n,height:o,$:a}=K(t),i=(a?l(r.width):r.width)/n,s=(a?l(r.height):r.height)/o;return i&&Number.isFinite(i)||(i=1),s&&Number.isFinite(s)||(s=1),{x:i,y:s}}let X=u(0);function $(e){let t=j(e);return V()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:X}function Z(e,t,r,n){var o;void 0===t&&(t=!1),void 0===r&&(r=!1);let a=e.getBoundingClientRect(),i=q(e),l=u(1);t&&(n?N(n)&&(l=Y(n)):l=Y(e));let s=(void 0===(o=r)&&(o=!1),n&&(!o||n===j(i))&&o)?$(i):u(0),c=(a.left+s.x)/l.x,d=(a.top+s.y)/l.y,f=a.width/l.x,p=a.height/l.y;if(i){let e=j(i),t=n&&N(n)?j(n):n,r=e,o=G(r);for(;o&&n&&t!==r;){let e=Y(o),t=o.getBoundingClientRect(),n=z(o),a=t.left+(o.clientLeft+parseFloat(n.paddingLeft))*e.x,i=t.top+(o.clientTop+parseFloat(n.paddingTop))*e.y;c*=e.x,d*=e.y,f*=e.x,p*=e.y,c+=a,d+=i,o=G(r=j(o))}}return x({width:f,height:p,x:c,y:d})}function J(e,t){let r=H(e).scrollLeft;return t?t.left+r:Z(M(e)).left+r}function Q(e,t,r){void 0===r&&(r=!1);let n=e.getBoundingClientRect();return{x:n.left+t.scrollLeft-(r?0:J(e,n)),y:n.top+t.scrollTop}}function ee(e,t,r){let n;if("viewport"===t)n=function(e,t){let r=j(e),n=M(e),o=r.visualViewport,a=n.clientWidth,i=n.clientHeight,l=0,s=0;if(o){a=o.width,i=o.height;let e=V();(!e||e&&"fixed"===t)&&(l=o.offsetLeft,s=o.offsetTop)}return{width:a,height:i,x:l,y:s}}(e,r);else if("document"===t)n=function(e){let t=M(e),r=H(e),n=e.ownerDocument.body,o=i(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),a=i(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight),l=-r.scrollLeft+J(e),s=-r.scrollTop;return"rtl"===z(n).direction&&(l+=i(t.clientWidth,n.clientWidth)-o),{width:o,height:a,x:l,y:s}}(M(e));else if(N(t))n=function(e,t){let r=Z(e,!0,"fixed"===t),n=r.top+e.clientTop,o=r.left+e.clientLeft,a=L(e)?Y(e):u(1),i=e.clientWidth*a.x,l=e.clientHeight*a.y;return{width:i,height:l,x:o*a.x,y:n*a.y}}(t,r);else{let r=$(e);n={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return x(n)}function et(e){return"static"===z(e).position}function er(e,t){if(!L(e)||"fixed"===z(e).position)return null;if(t)return t(e);let r=e.offsetParent;return M(e)===r&&(r=r.ownerDocument.body),r}function en(e,t){let r=j(e);if(I(e))return r;if(!L(e)){let t=W(e);for(;t&&!B(t);){if(N(t)&&!et(t))return t;t=W(t)}return r}let n=er(e,t);for(;n&&["table","td","th"].includes(D(n))&&et(n);)n=er(n,t);return n&&B(n)&&et(n)&&!F(n)?r:n||function(e){let t=W(e);for(;L(t)&&!B(t);){if(F(t))return t;if(I(t))break;t=W(t)}return null}(e)||r}let eo=async function(e){let t=this.getOffsetParent||en,r=this.getDimensions,n=await r(e.floating);return{reference:function(e,t,r){let n=L(t),o=M(t),a="fixed"===r,i=Z(e,!0,a,t),l={scrollLeft:0,scrollTop:0},s=u(0);if(n||!n&&!a)if(("body"!==D(t)||_(o))&&(l=H(t)),n){let e=Z(t,!0,a,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else o&&(s.x=J(o));a&&!n&&o&&(s.x=J(o));let c=!o||n||a?u(0):Q(o,l);return{x:i.left+l.scrollLeft-s.x-c.x,y:i.top+l.scrollTop-s.y-c.y,width:i.width,height:i.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}},ea={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:r,offsetParent:n,strategy:o}=e,a="fixed"===o,i=M(n),l=!!t&&I(t.floating);if(n===i||l&&a)return r;let s={scrollLeft:0,scrollTop:0},c=u(1),d=u(0),f=L(n);if((f||!f&&!a)&&(("body"!==D(n)||_(i))&&(s=H(n)),L(n))){let e=Z(n);c=Y(n),d.x=e.x+n.clientLeft,d.y=e.y+n.clientTop}let p=!i||f||a?u(0):Q(i,s,!0);return{width:r.width*c.x,height:r.height*c.y,x:r.x*c.x-s.scrollLeft*c.x+d.x+p.x,y:r.y*c.y-s.scrollTop*c.y+d.y+p.y}},getDocumentElement:M,getClippingRect:function(e){let{element:t,boundary:r,rootBoundary:n,strategy:o}=e,l=[..."clippingAncestors"===r?I(t)?[]:function(e,t){let r=t.get(e);if(r)return r;let n=U(e,[],!1).filter(e=>N(e)&&"body"!==D(e)),o=null,a="fixed"===z(e).position,i=a?W(e):e;for(;N(i)&&!B(i);){let t=z(i),r=F(i);r||"fixed"!==t.position||(o=null),(a?!r&&!o:!r&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||_(i)&&!r&&function e(t,r){let n=W(t);return!(n===r||!N(n)||B(n))&&("fixed"===z(n).position||e(n,r))}(e,i))?n=n.filter(e=>e!==i):o=t,i=W(i)}return t.set(e,n),n}(t,this._c):[].concat(r),n],s=l[0],u=l.reduce((e,r)=>{let n=ee(t,r,o);return e.top=i(n.top,e.top),e.right=a(n.right,e.right),e.bottom=a(n.bottom,e.bottom),e.left=i(n.left,e.left),e},ee(t,s,o));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:en,getElementRects:eo,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:r}=K(e);return{width:t,height:r}},getScale:Y,isElement:N,isRTL:function(e){return"rtl"===z(e).direction}};function ei(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let el=e=>({name:"arrow",options:e,async fn(t){let{x:r,y:n,placement:o,rects:l,platform:s,elements:u,middlewareData:c}=t,{element:d,padding:p=0}=f(e,t)||{};if(null==d)return{};let y=w(p),b={x:r,y:n},x=h(g(o)),E=v(x),k=await s.getDimensions(d),C="y"===x,S=C?"clientHeight":"clientWidth",R=l.reference[E]+l.reference[x]-b[x]-l.floating[E],A=b[x]-l.reference[x],T=await (null==s.getOffsetParent?void 0:s.getOffsetParent(d)),D=T?T[S]:0;D&&await (null==s.isElement?void 0:s.isElement(T))||(D=u.floating[S]||l.floating[E]);let j=D/2-k[E]/2-1,M=a(y[C?"top":"left"],j),P=a(y[C?"bottom":"right"],j),N=D-k[E]-P,L=D/2-k[E]/2+(R/2-A/2),O=i(M,a(L,N)),_=!c.arrow&&null!=m(o)&&L!==O&&l.reference[E]/2-(L<M?M:P)-k[E]/2<0,I=_?L<M?L-M:L-N:0;return{[x]:b[x]+I,data:{[x]:O,centerOffset:L-O-I,..._&&{alignmentOffset:I}},reset:_}}}),es=(e,t,r)=>{let n=new Map,o={platform:ea,...r},a={...o.platform,_c:n};return k(e,t,{...o,platform:a})};var eu=r(1076),ec="undefined"!=typeof document?n.useLayoutEffect:n.useEffect;function ed(e,t){let r,n,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((r=e.length)!==t.length)return!1;for(n=r;0!=n--;)if(!ed(e[n],t[n]))return!1;return!0}if((r=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(n=r;0!=n--;)if(!({}).hasOwnProperty.call(t,o[n]))return!1;for(n=r;0!=n--;){let r=o[n];if(("_owner"!==r||!e.$$typeof)&&!ed(e[r],t[r]))return!1}return!0}return e!=e&&t!=t}function ef(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ep(e,t){let r=ef(e);return Math.round(t*r)/r}function em(e){let t=n.useRef(e);return ec(()=>{t.current=e}),t}let eh=e=>({name:"arrow",options:e,fn(t){let{element:r,padding:n}="function"==typeof e?e(t):e;return r&&({}).hasOwnProperty.call(r,"current")?null!=r.current?el({element:r.current,padding:n}).fn(t):{}:r?el({element:r,padding:n}).fn(t):{}}}),ev=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var r,n;let{x:o,y:a,placement:i,middlewareData:l}=t,s=await A(t,e);return i===(null==(r=l.offset)?void 0:r.placement)&&null!=(n=l.arrow)&&n.alignmentOffset?{}:{x:o+s.x,y:a+s.y,data:{...s,placement:i}}}}}(e),options:[e,t]}),eg=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:r,y:n,placement:o}=t,{mainAxis:l=!0,crossAxis:s=!1,limiter:u={fn:e=>{let{x:t,y:r}=e;return{x:t,y:r}}},...c}=f(e,t),d={x:r,y:n},m=await C(t,c),v=g(p(o)),y=h(v),b=d[y],w=d[v];if(l){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",r=b+m[e],n=b-m[t];b=i(r,a(b,n))}if(s){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",r=w+m[e],n=w-m[t];w=i(r,a(w,n))}let x=u.fn({...t,[y]:b,[v]:w});return{...x,data:{x:x.x-r,y:x.y-n,enabled:{[y]:l,[v]:s}}}}}}(e),options:[e,t]}),ey=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:r,y:n,placement:o,rects:a,middlewareData:i}=t,{offset:l=0,mainAxis:s=!0,crossAxis:u=!0}=f(e,t),c={x:r,y:n},d=g(o),m=h(d),v=c[m],y=c[d],b=f(l,t),w="number"==typeof b?{mainAxis:b,crossAxis:0}:{mainAxis:0,crossAxis:0,...b};if(s){let e="y"===m?"height":"width",t=a.reference[m]-a.floating[e]+w.mainAxis,r=a.reference[m]+a.reference[e]-w.mainAxis;v<t?v=t:v>r&&(v=r)}if(u){var x,E;let e="y"===m?"width":"height",t=["top","left"].includes(p(o)),r=a.reference[d]-a.floating[e]+(t&&(null==(x=i.offset)?void 0:x[d])||0)+(t?0:w.crossAxis),n=a.reference[d]+a.reference[e]+(t?0:(null==(E=i.offset)?void 0:E[d])||0)-(t?w.crossAxis:0);y<r?y=r:y>n&&(y=n)}return{[m]:v,[d]:y}}}}(e),options:[e,t]}),eb=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var r,n,o,a,i,l;let{placement:s,middlewareData:u,rects:c,initialPlacement:d,platform:w,elements:x}=t,{mainAxis:E=!0,crossAxis:k=!0,fallbackPlacements:S,fallbackStrategy:R="bestFit",fallbackAxisSideDirection:A="none",flipAlignment:T=!0,...D}=f(e,t);if(null!=(r=u.arrow)&&r.alignmentOffset)return{};let j=p(s),M=g(d),P=p(d)===d,N=await (null==w.isRTL?void 0:w.isRTL(x.floating)),L=S||(P||!T?[b(d)]:function(e){let t=b(e);return[y(e),t,y(t)]}(d)),O="none"!==A;!S&&O&&L.push(...function(e,t,r,n){let o=m(e),a=function(e,t,r){let n=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(r)return t?o:n;return t?n:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===r,n);return o&&(a=a.map(e=>e+"-"+o),t&&(a=a.concat(a.map(y)))),a}(d,T,A,N));let _=[d,...L],I=await C(t,D),F=[],V=(null==(n=u.flip)?void 0:n.overflows)||[];if(E&&F.push(I[j]),k){let e=function(e,t,r){void 0===r&&(r=!1);let n=m(e),o=h(g(e)),a=v(o),i="x"===o?n===(r?"end":"start")?"right":"left":"start"===n?"bottom":"top";return t.reference[a]>t.floating[a]&&(i=b(i)),[i,b(i)]}(s,c,N);F.push(I[e[0]],I[e[1]])}if(V=[...V,{placement:s,overflows:F}],!F.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=_[e];if(t){let r="alignment"===k&&M!==g(t),n=(null==(i=V[0])?void 0:i.overflows[0])>0;if(!r||n)return{data:{index:e,overflows:V},reset:{placement:t}}}let r=null==(a=V.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:a.placement;if(!r)switch(R){case"bestFit":{let e=null==(l=V.filter(e=>{if(O){let t=g(e.placement);return t===M||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(r=e);break}case"initialPlacement":r=d}if(s!==r)return{reset:{placement:r}}}return{}}}}(e),options:[e,t]}),ew=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var r,n;let o,l,{placement:s,rects:u,platform:c,elements:d}=t,{apply:h=()=>{},...v}=f(e,t),y=await C(t,v),b=p(s),w=m(s),x="y"===g(s),{width:E,height:k}=u.floating;"top"===b||"bottom"===b?(o=b,l=w===(await (null==c.isRTL?void 0:c.isRTL(d.floating))?"start":"end")?"left":"right"):(l=b,o="end"===w?"top":"bottom");let S=k-y.top-y.bottom,R=E-y.left-y.right,A=a(k-y[o],S),T=a(E-y[l],R),D=!t.middlewareData.shift,j=A,M=T;if(null!=(r=t.middlewareData.shift)&&r.enabled.x&&(M=R),null!=(n=t.middlewareData.shift)&&n.enabled.y&&(j=S),D&&!w){let e=i(y.left,0),t=i(y.right,0),r=i(y.top,0),n=i(y.bottom,0);x?M=E-2*(0!==e||0!==t?e+t:i(y.left,y.right)):j=k-2*(0!==r||0!==n?r+n:i(y.top,y.bottom))}await h({...t,availableWidth:M,availableHeight:j});let P=await c.getDimensions(d.floating);return E!==P.width||k!==P.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),ex=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:r}=t,{strategy:n="referenceHidden",...o}=f(e,t);switch(n){case"referenceHidden":{let e=S(await C(t,{...o,elementContext:"reference"}),r.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:R(e)}}}case"escaped":{let e=S(await C(t,{...o,altBoundary:!0}),r.floating);return{data:{escapedOffsets:e,escaped:R(e)}}}default:return{}}}}}(e),options:[e,t]}),eE=(e,t)=>({...eh(e),options:[e,t]});var ek=r(6856),eC=r(7093),eS=n.forwardRef((e,t)=>{let{children:r,width:n=10,height:o=5,...a}=e;return(0,eC.jsx)(ek.sG.svg,{...a,ref:t,width:n,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,eC.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eS.displayName="Arrow";var eR=r(4489),eA=r(563),eT=r(5196),eD=r(5366),ej=r(2080),eM="Popper",[eP,eN]=(0,eA.A)(eM),[eL,eO]=eP(eM),e_=e=>{let{__scopePopper:t,children:r}=e,[o,a]=n.useState(null);return(0,eC.jsx)(eL,{scope:t,anchor:o,onAnchorChange:a,children:r})};e_.displayName=eM;var eI="PopperAnchor",eF=n.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:o,...a}=e,i=eO(eI,r),l=n.useRef(null),s=(0,eR.s)(t,l);return n.useEffect(()=>{i.onAnchorChange((null==o?void 0:o.current)||l.current)}),o?null:(0,eC.jsx)(ek.sG.div,{...a,ref:s})});eF.displayName=eI;var eV="PopperContent",[eB,ez]=eP(eV),eH=n.forwardRef((e,t)=>{var r,o,l,u,c,d,f,p;let{__scopePopper:m,side:h="bottom",sideOffset:v=0,align:g="center",alignOffset:y=0,arrowPadding:b=0,avoidCollisions:w=!0,collisionBoundary:x=[],collisionPadding:E=0,sticky:k="partial",hideWhenDetached:C=!1,updatePositionStrategy:S="optimized",onPlaced:R,...A}=e,T=eO(eV,m),[D,j]=n.useState(null),P=(0,eR.s)(t,e=>j(e)),[N,L]=n.useState(null),O=(0,ej.X)(N),_=null!==(f=null==O?void 0:O.width)&&void 0!==f?f:0,I=null!==(p=null==O?void 0:O.height)&&void 0!==p?p:0,F="number"==typeof E?E:{top:0,right:0,bottom:0,left:0,...E},V=Array.isArray(x)?x:[x],B=V.length>0,z={padding:F,boundary:V.filter(eK),altBoundary:B},{refs:H,floatingStyles:W,placement:G,isPositioned:K,middlewareData:Y}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:r="absolute",middleware:o=[],platform:a,elements:{reference:i,floating:l}={},transform:s=!0,whileElementsMounted:u,open:c}=e,[d,f]=n.useState({x:0,y:0,strategy:r,placement:t,middlewareData:{},isPositioned:!1}),[p,m]=n.useState(o);ed(p,o)||m(o);let[h,v]=n.useState(null),[g,y]=n.useState(null),b=n.useCallback(e=>{e!==k.current&&(k.current=e,v(e))},[]),w=n.useCallback(e=>{e!==C.current&&(C.current=e,y(e))},[]),x=i||h,E=l||g,k=n.useRef(null),C=n.useRef(null),S=n.useRef(d),R=null!=u,A=em(u),T=em(a),D=em(c),j=n.useCallback(()=>{if(!k.current||!C.current)return;let e={placement:t,strategy:r,middleware:p};T.current&&(e.platform=T.current),es(k.current,C.current,e).then(e=>{let t={...e,isPositioned:!1!==D.current};M.current&&!ed(S.current,t)&&(S.current=t,eu.flushSync(()=>{f(t)}))})},[p,t,r,T,D]);ec(()=>{!1===c&&S.current.isPositioned&&(S.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[c]);let M=n.useRef(!1);ec(()=>(M.current=!0,()=>{M.current=!1}),[]),ec(()=>{if(x&&(k.current=x),E&&(C.current=E),x&&E){if(A.current)return A.current(x,E,j);j()}},[x,E,j,A,R]);let P=n.useMemo(()=>({reference:k,floating:C,setReference:b,setFloating:w}),[b,w]),N=n.useMemo(()=>({reference:x,floating:E}),[x,E]),L=n.useMemo(()=>{let e={position:r,left:0,top:0};if(!N.floating)return e;let t=ep(N.floating,d.x),n=ep(N.floating,d.y);return s?{...e,transform:"translate("+t+"px, "+n+"px)",...ef(N.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:t,top:n}},[r,s,N.floating,d.x,d.y]);return n.useMemo(()=>({...d,update:j,refs:P,elements:N,floatingStyles:L}),[d,j,P,N,L])}({strategy:"fixed",placement:h+("center"!==g?"-"+g:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(e,t,r,n){let o;void 0===n&&(n={});let{ancestorScroll:l=!0,ancestorResize:u=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:f=!1}=n,p=q(e),m=l||u?[...p?U(p):[],...U(t)]:[];m.forEach(e=>{l&&e.addEventListener("scroll",r,{passive:!0}),u&&e.addEventListener("resize",r)});let h=p&&d?function(e,t){let r,n=null,o=M(e);function l(){var e;clearTimeout(r),null==(e=n)||e.disconnect(),n=null}return!function u(c,d){void 0===c&&(c=!1),void 0===d&&(d=1),l();let f=e.getBoundingClientRect(),{left:p,top:m,width:h,height:v}=f;if(c||t(),!h||!v)return;let g=s(m),y=s(o.clientWidth-(p+h)),b={rootMargin:-g+"px "+-y+"px "+-s(o.clientHeight-(m+v))+"px "+-s(p)+"px",threshold:i(0,a(1,d))||1},w=!0;function x(t){let n=t[0].intersectionRatio;if(n!==d){if(!w)return u();n?u(!1,n):r=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==n||ei(f,e.getBoundingClientRect())||u(),w=!1}try{n=new IntersectionObserver(x,{...b,root:o.ownerDocument})}catch(e){n=new IntersectionObserver(x,b)}n.observe(e)}(!0),l}(p,r):null,v=-1,g=null;c&&(g=new ResizeObserver(e=>{let[n]=e;n&&n.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),r()}),p&&!f&&g.observe(p),g.observe(t));let y=f?Z(e):null;return f&&function t(){let n=Z(e);y&&!ei(y,n)&&r(),y=n,o=requestAnimationFrame(t)}(),r(),()=>{var e;m.forEach(e=>{l&&e.removeEventListener("scroll",r),u&&e.removeEventListener("resize",r)}),null==h||h(),null==(e=g)||e.disconnect(),g=null,f&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===S})},elements:{reference:T.anchor},middleware:[ev({mainAxis:v+I,alignmentAxis:y}),w&&eg({mainAxis:!0,crossAxis:!1,limiter:"partial"===k?ey():void 0,...z}),w&&eb({...z}),ew({...z,apply:e=>{let{elements:t,rects:r,availableWidth:n,availableHeight:o}=e,{width:a,height:i}=r.reference,l=t.floating.style;l.setProperty("--radix-popper-available-width","".concat(n,"px")),l.setProperty("--radix-popper-available-height","".concat(o,"px")),l.setProperty("--radix-popper-anchor-width","".concat(a,"px")),l.setProperty("--radix-popper-anchor-height","".concat(i,"px"))}}),N&&eE({element:N,padding:b}),eq({arrowWidth:_,arrowHeight:I}),C&&ex({strategy:"referenceHidden",...z})]}),[X,$]=eY(G),J=(0,eT.c)(R);(0,eD.N)(()=>{K&&(null==J||J())},[K,J]);let Q=null===(r=Y.arrow)||void 0===r?void 0:r.x,ee=null===(o=Y.arrow)||void 0===o?void 0:o.y,et=(null===(l=Y.arrow)||void 0===l?void 0:l.centerOffset)!==0,[er,en]=n.useState();return(0,eD.N)(()=>{D&&en(window.getComputedStyle(D).zIndex)},[D]),(0,eC.jsx)("div",{ref:H.setFloating,"data-radix-popper-content-wrapper":"",style:{...W,transform:K?W.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:er,"--radix-popper-transform-origin":[null===(u=Y.transformOrigin)||void 0===u?void 0:u.x,null===(c=Y.transformOrigin)||void 0===c?void 0:c.y].join(" "),...(null===(d=Y.hide)||void 0===d?void 0:d.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eC.jsx)(eB,{scope:m,placedSide:X,onArrowChange:L,arrowX:Q,arrowY:ee,shouldHideArrow:et,children:(0,eC.jsx)(ek.sG.div,{"data-side":X,"data-align":$,...A,ref:P,style:{...A.style,animation:K?void 0:"none"}})})})});eH.displayName=eV;var eW="PopperArrow",eU={top:"bottom",right:"left",bottom:"top",left:"right"},eG=n.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,o=ez(eW,r),a=eU[o.placedSide];return(0,eC.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[a]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eC.jsx)(eS,{...n,ref:t,style:{...n.style,display:"block"}})})});function eK(e){return null!==e}eG.displayName=eW;var eq=e=>({name:"transformOrigin",options:e,fn(t){var r,n,o,a,i;let{placement:l,rects:s,middlewareData:u}=t,c=(null===(r=u.arrow)||void 0===r?void 0:r.centerOffset)!==0,d=c?0:e.arrowWidth,f=c?0:e.arrowHeight,[p,m]=eY(l),h={start:"0%",center:"50%",end:"100%"}[m],v=(null!==(a=null===(n=u.arrow)||void 0===n?void 0:n.x)&&void 0!==a?a:0)+d/2,g=(null!==(i=null===(o=u.arrow)||void 0===o?void 0:o.y)&&void 0!==i?i:0)+f/2,y="",b="";return"bottom"===p?(y=c?h:"".concat(v,"px"),b="".concat(-f,"px")):"top"===p?(y=c?h:"".concat(v,"px"),b="".concat(s.floating.height+f,"px")):"right"===p?(y="".concat(-f,"px"),b=c?h:"".concat(g,"px")):"left"===p&&(y="".concat(s.floating.width+f,"px"),b=c?h:"".concat(g,"px")),{data:{x:y,y:b}}}});function eY(e){let[t,r="center"]=e.split("-");return[t,r]}var eX=e_,e$=eF,eZ=eH,eJ=eG},563:(e,t,r)=>{r.d(t,{A:()=>i,q:()=>a});var n=r(4545),o=r(7093);function a(e,t){let r=n.createContext(t),a=e=>{let{children:t,...a}=e,i=n.useMemo(()=>a,Object.values(a));return(0,o.jsx)(r.Provider,{value:i,children:t})};return a.displayName=e+"Provider",[a,function(o){let a=n.useContext(r);if(a)return a;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function i(e,t=[]){let r=[],a=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return a.scopeName=e,[function(t,a){let i=n.createContext(a),l=r.length;r=[...r,a];let s=t=>{let{scope:r,children:a,...s}=t,u=r?.[e]?.[l]||i,c=n.useMemo(()=>s,Object.values(s));return(0,o.jsx)(u.Provider,{value:c,children:a})};return s.displayName=t+"Provider",[s,function(r,o){let s=o?.[e]?.[l]||i,u=n.useContext(s);if(u)return u;if(void 0!==a)return a;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(a,...t)]}},667:(e,t,r)=>{r.d(t,{Q:()=>n});var n=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}}},1040:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(436).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},1046:(e,t,r)=>{r.d(t,{QP:()=>eu});let n=e=>{let t=l(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),o(r,t)||i(e)},getConflictingClassGroupIds:(e,t)=>{let o=r[e]||[];return t&&n[e]?[...o,...n[e]]:o}}},o=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],n=t.nextPart.get(r),a=n?o(e.slice(1),n):void 0;if(a)return a;if(0===t.validators.length)return;let i=e.join("-");return t.validators.find(({validator:e})=>e(i))?.classGroupId},a=/^\[(.+)\]$/,i=e=>{if(a.test(e)){let t=a.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},l=e=>{let{theme:t,classGroups:r}=e,n={nextPart:new Map,validators:[]};for(let e in r)s(r[e],n,e,t);return n},s=(e,t,r,n)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:u(t,e)).classGroupId=r;return}if("function"==typeof e){if(c(e)){s(e(n),t,r,n);return}t.validators.push({validator:e,classGroupId:r});return}Object.entries(e).forEach(([e,o])=>{s(o,u(t,e),r,n)})})},u=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},c=e=>e.isThemeGetter,d=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,n=new Map,o=(o,a)=>{r.set(o,a),++t>e&&(t=0,n=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=n.get(e))?(o(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):o(e,t)}}},f=e=>{let{prefix:t,experimentalParseClassName:r}=e,n=e=>{let t,r=[],n=0,o=0,a=0;for(let i=0;i<e.length;i++){let l=e[i];if(0===n&&0===o){if(":"===l){r.push(e.slice(a,i)),a=i+1;continue}if("/"===l){t=i;continue}}"["===l?n++:"]"===l?n--:"("===l?o++:")"===l&&o--}let i=0===r.length?e:e.substring(a),l=p(i);return{modifiers:r,hasImportantModifier:l!==i,baseClassName:l,maybePostfixModifierPosition:t&&t>a?t-a:void 0}};if(t){let e=t+":",r=n;n=t=>t.startsWith(e)?r(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(r){let e=n;n=t=>r({className:t,parseClassName:e})}return n},p=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,m=e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let r=[],n=[];return e.forEach(e=>{"["===e[0]||t[e]?(r.push(...n.sort(),e),n=[]):n.push(e)}),r.push(...n.sort()),r}},h=e=>({cache:d(e.cacheSize),parseClassName:f(e),sortModifiers:m(e),...n(e)}),v=/\s+/,g=(e,t)=>{let{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:o,sortModifiers:a}=t,i=[],l=e.trim().split(v),s="";for(let e=l.length-1;e>=0;e-=1){let t=l[e],{isExternal:u,modifiers:c,hasImportantModifier:d,baseClassName:f,maybePostfixModifierPosition:p}=r(t);if(u){s=t+(s.length>0?" "+s:s);continue}let m=!!p,h=n(m?f.substring(0,p):f);if(!h){if(!m||!(h=n(f))){s=t+(s.length>0?" "+s:s);continue}m=!1}let v=a(c).join(":"),g=d?v+"!":v,y=g+h;if(i.includes(y))continue;i.push(y);let b=o(h,m);for(let e=0;e<b.length;++e){let t=b[e];i.push(g+t)}s=t+(s.length>0?" "+s:s)}return s};function y(){let e,t,r=0,n="";for(;r<arguments.length;)(e=arguments[r++])&&(t=b(e))&&(n&&(n+=" "),n+=t);return n}let b=e=>{let t;if("string"==typeof e)return e;let r="";for(let n=0;n<e.length;n++)e[n]&&(t=b(e[n]))&&(r&&(r+=" "),r+=t);return r},w=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},x=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,E=/^\((?:(\w[\w-]*):)?(.+)\)$/i,k=/^\d+\/\d+$/,C=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,S=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,R=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,A=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,T=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,D=e=>k.test(e),j=e=>!!e&&!Number.isNaN(Number(e)),M=e=>!!e&&Number.isInteger(Number(e)),P=e=>e.endsWith("%")&&j(e.slice(0,-1)),N=e=>C.test(e),L=()=>!0,O=e=>S.test(e)&&!R.test(e),_=()=>!1,I=e=>A.test(e),F=e=>T.test(e),V=e=>!z(e)&&!q(e),B=e=>ee(e,eo,_),z=e=>x.test(e),H=e=>ee(e,ea,O),W=e=>ee(e,ei,j),U=e=>ee(e,er,_),G=e=>ee(e,en,F),K=e=>ee(e,es,I),q=e=>E.test(e),Y=e=>et(e,ea),X=e=>et(e,el),$=e=>et(e,er),Z=e=>et(e,eo),J=e=>et(e,en),Q=e=>et(e,es,!0),ee=(e,t,r)=>{let n=x.exec(e);return!!n&&(n[1]?t(n[1]):r(n[2]))},et=(e,t,r=!1)=>{let n=E.exec(e);return!!n&&(n[1]?t(n[1]):r)},er=e=>"position"===e||"percentage"===e,en=e=>"image"===e||"url"===e,eo=e=>"length"===e||"size"===e||"bg-size"===e,ea=e=>"length"===e,ei=e=>"number"===e,el=e=>"family-name"===e,es=e=>"shadow"===e;Symbol.toStringTag;let eu=function(e,...t){let r,n,o,a=function(l){return n=(r=h(t.reduce((e,t)=>t(e),e()))).cache.get,o=r.cache.set,a=i,i(l)};function i(e){let t=n(e);if(t)return t;let a=g(e,r);return o(e,a),a}return function(){return a(y.apply(null,arguments))}}(()=>{let e=w("color"),t=w("font"),r=w("text"),n=w("font-weight"),o=w("tracking"),a=w("leading"),i=w("breakpoint"),l=w("container"),s=w("spacing"),u=w("radius"),c=w("shadow"),d=w("inset-shadow"),f=w("text-shadow"),p=w("drop-shadow"),m=w("blur"),h=w("perspective"),v=w("aspect"),g=w("ease"),y=w("animate"),b=()=>["auto","avoid","all","avoid-page","page","left","right","column"],x=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],E=()=>[...x(),q,z],k=()=>["auto","hidden","clip","visible","scroll"],C=()=>["auto","contain","none"],S=()=>[q,z,s],R=()=>[D,"full","auto",...S()],A=()=>[M,"none","subgrid",q,z],T=()=>["auto",{span:["full",M,q,z]},M,q,z],O=()=>[M,"auto",q,z],_=()=>["auto","min","max","fr",q,z],I=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],F=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...S()],et=()=>[D,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...S()],er=()=>[e,q,z],en=()=>[...x(),$,U,{position:[q,z]}],eo=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ea=()=>["auto","cover","contain",Z,B,{size:[q,z]}],ei=()=>[P,Y,H],el=()=>["","none","full",u,q,z],es=()=>["",j,Y,H],eu=()=>["solid","dashed","dotted","double"],ec=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],ed=()=>[j,P,$,U],ef=()=>["","none",m,q,z],ep=()=>["none",j,q,z],em=()=>["none",j,q,z],eh=()=>[j,q,z],ev=()=>[D,"full",...S()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[N],breakpoint:[N],color:[L],container:[N],"drop-shadow":[N],ease:["in","out","in-out"],font:[V],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[N],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[N],shadow:[N],spacing:["px",j],text:[N],"text-shadow":[N],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",D,z,q,v]}],container:["container"],columns:[{columns:[j,z,q,l]}],"break-after":[{"break-after":b()}],"break-before":[{"break-before":b()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:E()}],overflow:[{overflow:k()}],"overflow-x":[{"overflow-x":k()}],"overflow-y":[{"overflow-y":k()}],overscroll:[{overscroll:C()}],"overscroll-x":[{"overscroll-x":C()}],"overscroll-y":[{"overscroll-y":C()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:R()}],"inset-x":[{"inset-x":R()}],"inset-y":[{"inset-y":R()}],start:[{start:R()}],end:[{end:R()}],top:[{top:R()}],right:[{right:R()}],bottom:[{bottom:R()}],left:[{left:R()}],visibility:["visible","invisible","collapse"],z:[{z:[M,"auto",q,z]}],basis:[{basis:[D,"full","auto",l,...S()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[j,D,"auto","initial","none",z]}],grow:[{grow:["",j,q,z]}],shrink:[{shrink:["",j,q,z]}],order:[{order:[M,"first","last","none",q,z]}],"grid-cols":[{"grid-cols":A()}],"col-start-end":[{col:T()}],"col-start":[{"col-start":O()}],"col-end":[{"col-end":O()}],"grid-rows":[{"grid-rows":A()}],"row-start-end":[{row:T()}],"row-start":[{"row-start":O()}],"row-end":[{"row-end":O()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":_()}],"auto-rows":[{"auto-rows":_()}],gap:[{gap:S()}],"gap-x":[{"gap-x":S()}],"gap-y":[{"gap-y":S()}],"justify-content":[{justify:[...I(),"normal"]}],"justify-items":[{"justify-items":[...F(),"normal"]}],"justify-self":[{"justify-self":["auto",...F()]}],"align-content":[{content:["normal",...I()]}],"align-items":[{items:[...F(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...F(),{baseline:["","last"]}]}],"place-content":[{"place-content":I()}],"place-items":[{"place-items":[...F(),"baseline"]}],"place-self":[{"place-self":["auto",...F()]}],p:[{p:S()}],px:[{px:S()}],py:[{py:S()}],ps:[{ps:S()}],pe:[{pe:S()}],pt:[{pt:S()}],pr:[{pr:S()}],pb:[{pb:S()}],pl:[{pl:S()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":S()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":S()}],"space-y-reverse":["space-y-reverse"],size:[{size:et()}],w:[{w:[l,"screen",...et()]}],"min-w":[{"min-w":[l,"screen","none",...et()]}],"max-w":[{"max-w":[l,"screen","none","prose",{screen:[i]},...et()]}],h:[{h:["screen","lh",...et()]}],"min-h":[{"min-h":["screen","lh","none",...et()]}],"max-h":[{"max-h":["screen","lh",...et()]}],"font-size":[{text:["base",r,Y,H]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[n,q,W]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",P,z]}],"font-family":[{font:[X,z,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[o,q,z]}],"line-clamp":[{"line-clamp":[j,"none",q,W]}],leading:[{leading:[a,...S()]}],"list-image":[{"list-image":["none",q,z]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",q,z]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:er()}],"text-color":[{text:er()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...eu(),"wavy"]}],"text-decoration-thickness":[{decoration:[j,"from-font","auto",q,H]}],"text-decoration-color":[{decoration:er()}],"underline-offset":[{"underline-offset":[j,"auto",q,z]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:S()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",q,z]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",q,z]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:en()}],"bg-repeat":[{bg:eo()}],"bg-size":[{bg:ea()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},M,q,z],radial:["",q,z],conic:[M,q,z]},J,G]}],"bg-color":[{bg:er()}],"gradient-from-pos":[{from:ei()}],"gradient-via-pos":[{via:ei()}],"gradient-to-pos":[{to:ei()}],"gradient-from":[{from:er()}],"gradient-via":[{via:er()}],"gradient-to":[{to:er()}],rounded:[{rounded:el()}],"rounded-s":[{"rounded-s":el()}],"rounded-e":[{"rounded-e":el()}],"rounded-t":[{"rounded-t":el()}],"rounded-r":[{"rounded-r":el()}],"rounded-b":[{"rounded-b":el()}],"rounded-l":[{"rounded-l":el()}],"rounded-ss":[{"rounded-ss":el()}],"rounded-se":[{"rounded-se":el()}],"rounded-ee":[{"rounded-ee":el()}],"rounded-es":[{"rounded-es":el()}],"rounded-tl":[{"rounded-tl":el()}],"rounded-tr":[{"rounded-tr":el()}],"rounded-br":[{"rounded-br":el()}],"rounded-bl":[{"rounded-bl":el()}],"border-w":[{border:es()}],"border-w-x":[{"border-x":es()}],"border-w-y":[{"border-y":es()}],"border-w-s":[{"border-s":es()}],"border-w-e":[{"border-e":es()}],"border-w-t":[{"border-t":es()}],"border-w-r":[{"border-r":es()}],"border-w-b":[{"border-b":es()}],"border-w-l":[{"border-l":es()}],"divide-x":[{"divide-x":es()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":es()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...eu(),"hidden","none"]}],"divide-style":[{divide:[...eu(),"hidden","none"]}],"border-color":[{border:er()}],"border-color-x":[{"border-x":er()}],"border-color-y":[{"border-y":er()}],"border-color-s":[{"border-s":er()}],"border-color-e":[{"border-e":er()}],"border-color-t":[{"border-t":er()}],"border-color-r":[{"border-r":er()}],"border-color-b":[{"border-b":er()}],"border-color-l":[{"border-l":er()}],"divide-color":[{divide:er()}],"outline-style":[{outline:[...eu(),"none","hidden"]}],"outline-offset":[{"outline-offset":[j,q,z]}],"outline-w":[{outline:["",j,Y,H]}],"outline-color":[{outline:er()}],shadow:[{shadow:["","none",c,Q,K]}],"shadow-color":[{shadow:er()}],"inset-shadow":[{"inset-shadow":["none",d,Q,K]}],"inset-shadow-color":[{"inset-shadow":er()}],"ring-w":[{ring:es()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:er()}],"ring-offset-w":[{"ring-offset":[j,H]}],"ring-offset-color":[{"ring-offset":er()}],"inset-ring-w":[{"inset-ring":es()}],"inset-ring-color":[{"inset-ring":er()}],"text-shadow":[{"text-shadow":["none",f,Q,K]}],"text-shadow-color":[{"text-shadow":er()}],opacity:[{opacity:[j,q,z]}],"mix-blend":[{"mix-blend":[...ec(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ec()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[j]}],"mask-image-linear-from-pos":[{"mask-linear-from":ed()}],"mask-image-linear-to-pos":[{"mask-linear-to":ed()}],"mask-image-linear-from-color":[{"mask-linear-from":er()}],"mask-image-linear-to-color":[{"mask-linear-to":er()}],"mask-image-t-from-pos":[{"mask-t-from":ed()}],"mask-image-t-to-pos":[{"mask-t-to":ed()}],"mask-image-t-from-color":[{"mask-t-from":er()}],"mask-image-t-to-color":[{"mask-t-to":er()}],"mask-image-r-from-pos":[{"mask-r-from":ed()}],"mask-image-r-to-pos":[{"mask-r-to":ed()}],"mask-image-r-from-color":[{"mask-r-from":er()}],"mask-image-r-to-color":[{"mask-r-to":er()}],"mask-image-b-from-pos":[{"mask-b-from":ed()}],"mask-image-b-to-pos":[{"mask-b-to":ed()}],"mask-image-b-from-color":[{"mask-b-from":er()}],"mask-image-b-to-color":[{"mask-b-to":er()}],"mask-image-l-from-pos":[{"mask-l-from":ed()}],"mask-image-l-to-pos":[{"mask-l-to":ed()}],"mask-image-l-from-color":[{"mask-l-from":er()}],"mask-image-l-to-color":[{"mask-l-to":er()}],"mask-image-x-from-pos":[{"mask-x-from":ed()}],"mask-image-x-to-pos":[{"mask-x-to":ed()}],"mask-image-x-from-color":[{"mask-x-from":er()}],"mask-image-x-to-color":[{"mask-x-to":er()}],"mask-image-y-from-pos":[{"mask-y-from":ed()}],"mask-image-y-to-pos":[{"mask-y-to":ed()}],"mask-image-y-from-color":[{"mask-y-from":er()}],"mask-image-y-to-color":[{"mask-y-to":er()}],"mask-image-radial":[{"mask-radial":[q,z]}],"mask-image-radial-from-pos":[{"mask-radial-from":ed()}],"mask-image-radial-to-pos":[{"mask-radial-to":ed()}],"mask-image-radial-from-color":[{"mask-radial-from":er()}],"mask-image-radial-to-color":[{"mask-radial-to":er()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":x()}],"mask-image-conic-pos":[{"mask-conic":[j]}],"mask-image-conic-from-pos":[{"mask-conic-from":ed()}],"mask-image-conic-to-pos":[{"mask-conic-to":ed()}],"mask-image-conic-from-color":[{"mask-conic-from":er()}],"mask-image-conic-to-color":[{"mask-conic-to":er()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:en()}],"mask-repeat":[{mask:eo()}],"mask-size":[{mask:ea()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",q,z]}],filter:[{filter:["","none",q,z]}],blur:[{blur:ef()}],brightness:[{brightness:[j,q,z]}],contrast:[{contrast:[j,q,z]}],"drop-shadow":[{"drop-shadow":["","none",p,Q,K]}],"drop-shadow-color":[{"drop-shadow":er()}],grayscale:[{grayscale:["",j,q,z]}],"hue-rotate":[{"hue-rotate":[j,q,z]}],invert:[{invert:["",j,q,z]}],saturate:[{saturate:[j,q,z]}],sepia:[{sepia:["",j,q,z]}],"backdrop-filter":[{"backdrop-filter":["","none",q,z]}],"backdrop-blur":[{"backdrop-blur":ef()}],"backdrop-brightness":[{"backdrop-brightness":[j,q,z]}],"backdrop-contrast":[{"backdrop-contrast":[j,q,z]}],"backdrop-grayscale":[{"backdrop-grayscale":["",j,q,z]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[j,q,z]}],"backdrop-invert":[{"backdrop-invert":["",j,q,z]}],"backdrop-opacity":[{"backdrop-opacity":[j,q,z]}],"backdrop-saturate":[{"backdrop-saturate":[j,q,z]}],"backdrop-sepia":[{"backdrop-sepia":["",j,q,z]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":S()}],"border-spacing-x":[{"border-spacing-x":S()}],"border-spacing-y":[{"border-spacing-y":S()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",q,z]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[j,"initial",q,z]}],ease:[{ease:["linear","initial",g,q,z]}],delay:[{delay:[j,q,z]}],animate:[{animate:["none",y,q,z]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[h,q,z]}],"perspective-origin":[{"perspective-origin":E()}],rotate:[{rotate:ep()}],"rotate-x":[{"rotate-x":ep()}],"rotate-y":[{"rotate-y":ep()}],"rotate-z":[{"rotate-z":ep()}],scale:[{scale:em()}],"scale-x":[{"scale-x":em()}],"scale-y":[{"scale-y":em()}],"scale-z":[{"scale-z":em()}],"scale-3d":["scale-3d"],skew:[{skew:eh()}],"skew-x":[{"skew-x":eh()}],"skew-y":[{"skew-y":eh()}],transform:[{transform:[q,z,"","none","gpu","cpu"]}],"transform-origin":[{origin:E()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:ev()}],"translate-x":[{"translate-x":ev()}],"translate-y":[{"translate-y":ev()}],"translate-z":[{"translate-z":ev()}],"translate-none":["translate-none"],accent:[{accent:er()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:er()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",q,z]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":S()}],"scroll-mx":[{"scroll-mx":S()}],"scroll-my":[{"scroll-my":S()}],"scroll-ms":[{"scroll-ms":S()}],"scroll-me":[{"scroll-me":S()}],"scroll-mt":[{"scroll-mt":S()}],"scroll-mr":[{"scroll-mr":S()}],"scroll-mb":[{"scroll-mb":S()}],"scroll-ml":[{"scroll-ml":S()}],"scroll-p":[{"scroll-p":S()}],"scroll-px":[{"scroll-px":S()}],"scroll-py":[{"scroll-py":S()}],"scroll-ps":[{"scroll-ps":S()}],"scroll-pe":[{"scroll-pe":S()}],"scroll-pt":[{"scroll-pt":S()}],"scroll-pr":[{"scroll-pr":S()}],"scroll-pb":[{"scroll-pb":S()}],"scroll-pl":[{"scroll-pl":S()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",q,z]}],fill:[{fill:["none",...er()]}],"stroke-w":[{stroke:[j,Y,H,W]}],stroke:[{stroke:["none",...er()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},1451:(e,t,r)=>{r.d(t,{_:()=>o});var n=r(9730);function o(e,t,r){var o=(0,n._)(e,t,"set");if(o.set)o.set.call(e,r);else{if(!o.writable)throw TypeError("attempted to set read only private field");o.value=r}return r}},1796:(e,t,r)=>{r.d(t,{UC:()=>er,B8:()=>ee,bL:()=>Q,l9:()=>et});var n,o=r(4545),a=r(4157),i=r(563),l=r(7778),s=r(5993),u=r(1451),c=r(4489),d=r(6255),f=r(7093),p=new WeakMap;function m(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let r=function(e,t){let r=e.length,n=h(t),o=n>=0?n:r+n;return o<0||o>=r?-1:o}(e,t);return -1===r?void 0:e[r]}function h(e){return e!=e||0===e?0:Math.trunc(e)}n=new WeakMap;var v=r(3328),g=r(4617),y=r(5196),b=r(3028),w=r(6084),x="rovingFocusGroup.onEntryFocus",E={bubbles:!1,cancelable:!0},k="RovingFocusGroup",[C,S,R]=function(e){let t=e+"CollectionProvider",[r,n]=(0,i.A)(t),[a,l]=r(t,{collectionRef:{current:null},itemMap:new Map}),s=e=>{let{scope:t,children:r}=e,n=o.useRef(null),i=o.useRef(new Map).current;return(0,f.jsx)(a,{scope:t,itemMap:i,collectionRef:n,children:r})};s.displayName=t;let u=e+"CollectionSlot",p=(0,d.TL)(u),m=o.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=l(u,r),a=(0,c.s)(t,o.collectionRef);return(0,f.jsx)(p,{ref:a,children:n})});m.displayName=u;let h=e+"CollectionItemSlot",v="data-radix-collection-item",g=(0,d.TL)(h),y=o.forwardRef((e,t)=>{let{scope:r,children:n,...a}=e,i=o.useRef(null),s=(0,c.s)(t,i),u=l(h,r);return o.useEffect(()=>(u.itemMap.set(i,{ref:i,...a}),()=>void u.itemMap.delete(i))),(0,f.jsx)(g,{[v]:"",ref:s,children:n})});return y.displayName=h,[{Provider:s,Slot:m,ItemSlot:y},function(t){let r=l(e+"CollectionConsumer",t);return o.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(v,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},n]}(k),[A,T]=(0,i.A)(k,[R]),[D,j]=A(k),M=o.forwardRef((e,t)=>(0,f.jsx)(C.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(C.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(P,{...e,ref:t})})}));M.displayName=k;var P=o.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:n,loop:i=!1,dir:l,currentTabStopId:s,defaultCurrentTabStopId:u,onCurrentTabStopIdChange:d,onEntryFocus:p,preventScrollOnEntryFocus:m=!1,...h}=e,v=o.useRef(null),C=(0,c.s)(t,v),R=(0,w.jH)(l),[A,T]=(0,b.i)({prop:s,defaultProp:null!=u?u:null,onChange:d,caller:k}),[j,M]=o.useState(!1),P=(0,y.c)(p),N=S(r),L=o.useRef(!1),[O,I]=o.useState(0);return o.useEffect(()=>{let e=v.current;if(e)return e.addEventListener(x,P),()=>e.removeEventListener(x,P)},[P]),(0,f.jsx)(D,{scope:r,orientation:n,dir:R,loop:i,currentTabStopId:A,onItemFocus:o.useCallback(e=>T(e),[T]),onItemShiftTab:o.useCallback(()=>M(!0),[]),onFocusableItemAdd:o.useCallback(()=>I(e=>e+1),[]),onFocusableItemRemove:o.useCallback(()=>I(e=>e-1),[]),children:(0,f.jsx)(g.sG.div,{tabIndex:j||0===O?-1:0,"data-orientation":n,...h,ref:C,style:{outline:"none",...e.style},onMouseDown:(0,a.m)(e.onMouseDown,()=>{L.current=!0}),onFocus:(0,a.m)(e.onFocus,e=>{let t=!L.current;if(e.target===e.currentTarget&&t&&!j){let t=new CustomEvent(x,E);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=N().filter(e=>e.focusable);_([e.find(e=>e.active),e.find(e=>e.id===A),...e].filter(Boolean).map(e=>e.ref.current),m)}}L.current=!1}),onBlur:(0,a.m)(e.onBlur,()=>M(!1))})})}),N="RovingFocusGroupItem",L=o.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:n=!0,active:i=!1,tabStopId:l,children:s,...u}=e,c=(0,v.B)(),d=l||c,p=j(N,r),m=p.currentTabStopId===d,h=S(r),{onFocusableItemAdd:y,onFocusableItemRemove:b,currentTabStopId:w}=p;return o.useEffect(()=>{if(n)return y(),()=>b()},[n,y,b]),(0,f.jsx)(C.ItemSlot,{scope:r,id:d,focusable:n,active:i,children:(0,f.jsx)(g.sG.span,{tabIndex:m?0:-1,"data-orientation":p.orientation,...u,ref:t,onMouseDown:(0,a.m)(e.onMouseDown,e=>{n?p.onItemFocus(d):e.preventDefault()}),onFocus:(0,a.m)(e.onFocus,()=>p.onItemFocus(d)),onKeyDown:(0,a.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){p.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return O[o]}(e,p.orientation,p.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=h().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=p.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>_(r))}}),children:"function"==typeof s?s({isCurrentTabStop:m,hasTabStop:null!=w}):s})})});L.displayName=N;var O={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function _(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var I=r(3182),F="Tabs",[V,B]=(0,i.A)(F,[T]),z=T(),[H,W]=V(F),U=o.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:o,defaultValue:a,orientation:i="horizontal",dir:l,activationMode:s="automatic",...u}=e,c=(0,w.jH)(l),[d,p]=(0,b.i)({prop:n,onChange:o,defaultProp:null!=a?a:"",caller:F});return(0,f.jsx)(H,{scope:r,baseId:(0,v.B)(),value:d,onValueChange:p,orientation:i,dir:c,activationMode:s,children:(0,f.jsx)(g.sG.div,{dir:c,"data-orientation":i,...u,ref:t})})});U.displayName=F;var G="TabsList",K=o.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...o}=e,a=W(G,r),i=z(r);return(0,f.jsx)(M,{asChild:!0,...i,orientation:a.orientation,dir:a.dir,loop:n,children:(0,f.jsx)(g.sG.div,{role:"tablist","aria-orientation":a.orientation,...o,ref:t})})});K.displayName=G;var q="TabsTrigger",Y=o.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:o=!1,...i}=e,l=W(q,r),s=z(r),u=Z(l.baseId,n),c=J(l.baseId,n),d=n===l.value;return(0,f.jsx)(L,{asChild:!0,...s,focusable:!o,active:d,children:(0,f.jsx)(g.sG.button,{type:"button",role:"tab","aria-selected":d,"aria-controls":c,"data-state":d?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:u,...i,ref:t,onMouseDown:(0,a.m)(e.onMouseDown,e=>{o||0!==e.button||!1!==e.ctrlKey?e.preventDefault():l.onValueChange(n)}),onKeyDown:(0,a.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&l.onValueChange(n)}),onFocus:(0,a.m)(e.onFocus,()=>{let e="manual"!==l.activationMode;d||o||!e||l.onValueChange(n)})})})});Y.displayName=q;var X="TabsContent",$=o.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,forceMount:a,children:i,...l}=e,s=W(X,r),u=Z(s.baseId,n),c=J(s.baseId,n),d=n===s.value,p=o.useRef(d);return o.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,f.jsx)(I.C,{present:a||d,children:r=>{let{present:n}=r;return(0,f.jsx)(g.sG.div,{"data-state":d?"active":"inactive","data-orientation":s.orientation,role:"tabpanel","aria-labelledby":u,hidden:!n,id:c,tabIndex:0,...l,ref:t,style:{...e.style,animationDuration:p.current?"0s":void 0},children:n&&i})}})});function Z(e,t){return"".concat(e,"-trigger-").concat(t)}function J(e,t){return"".concat(e,"-content-").concat(t)}$.displayName=X;var Q=U,ee=K,et=Y,er=$},2071:(e,t,r)=>{r.d(t,{Z:()=>s});var n=r(4545),o=r(1076),a=r(6856),i=r(5366),l=r(7093),s=n.forwardRef((e,t)=>{var r,s;let{container:u,...c}=e,[d,f]=n.useState(!1);(0,i.N)(()=>f(!0),[]);let p=u||d&&(null===(s=globalThis)||void 0===s?void 0:null===(r=s.document)||void 0===r?void 0:r.body);return p?o.createPortal((0,l.jsx)(a.sG.div,{...c,ref:t}),p):null});s.displayName="Portal"},2080:(e,t,r)=>{r.d(t,{X:()=>a});var n=r(4545),o=r(5366);function a(e){let[t,r]=n.useState(void 0);return(0,o.N)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let a=t[0];if("borderBoxSize"in a){let e=a.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}},2148:(e,t,r)=>{r.d(t,{Kq:()=>U,UC:()=>Y,ZL:()=>q,bL:()=>G,i3:()=>X,l9:()=>K});var n=r(4545),o=r(4157),a=r(4489),i=r(563),l=r(9817),s=r(3328),u=r(536),c=r(2071),d=r(3182),f=r(6856),p=r(8020),m=r(3028),h=r(6132),v=r(7093),[g,y]=(0,i.A)("Tooltip",[u.Bk]),b=(0,u.Bk)(),w="TooltipProvider",x="tooltip.open",[E,k]=g(w),C=e=>{let{__scopeTooltip:t,delayDuration:r=700,skipDelayDuration:o=300,disableHoverableContent:a=!1,children:i}=e,l=n.useRef(!0),s=n.useRef(!1),u=n.useRef(0);return n.useEffect(()=>{let e=u.current;return()=>window.clearTimeout(e)},[]),(0,v.jsx)(E,{scope:t,isOpenDelayedRef:l,delayDuration:r,onOpen:n.useCallback(()=>{window.clearTimeout(u.current),l.current=!1},[]),onClose:n.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>l.current=!0,o)},[o]),isPointerInTransitRef:s,onPointerInTransitChange:n.useCallback(e=>{s.current=e},[]),disableHoverableContent:a,children:i})};C.displayName=w;var S="Tooltip",[R,A]=g(S),T=e=>{let{__scopeTooltip:t,children:r,open:o,defaultOpen:a,onOpenChange:i,disableHoverableContent:l,delayDuration:c}=e,d=k(S,e.__scopeTooltip),f=b(t),[p,h]=n.useState(null),g=(0,s.B)(),y=n.useRef(0),w=null!=l?l:d.disableHoverableContent,E=null!=c?c:d.delayDuration,C=n.useRef(!1),[A,T]=(0,m.i)({prop:o,defaultProp:null!=a&&a,onChange:e=>{e?(d.onOpen(),document.dispatchEvent(new CustomEvent(x))):d.onClose(),null==i||i(e)},caller:S}),D=n.useMemo(()=>A?C.current?"delayed-open":"instant-open":"closed",[A]),j=n.useCallback(()=>{window.clearTimeout(y.current),y.current=0,C.current=!1,T(!0)},[T]),M=n.useCallback(()=>{window.clearTimeout(y.current),y.current=0,T(!1)},[T]),P=n.useCallback(()=>{window.clearTimeout(y.current),y.current=window.setTimeout(()=>{C.current=!0,T(!0),y.current=0},E)},[E,T]);return n.useEffect(()=>()=>{y.current&&(window.clearTimeout(y.current),y.current=0)},[]),(0,v.jsx)(u.bL,{...f,children:(0,v.jsx)(R,{scope:t,contentId:g,open:A,stateAttribute:D,trigger:p,onTriggerChange:h,onTriggerEnter:n.useCallback(()=>{d.isOpenDelayedRef.current?P():j()},[d.isOpenDelayedRef,P,j]),onTriggerLeave:n.useCallback(()=>{w?M():(window.clearTimeout(y.current),y.current=0)},[M,w]),onOpen:j,onClose:M,disableHoverableContent:w,children:r})})};T.displayName=S;var D="TooltipTrigger",j=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...i}=e,l=A(D,r),s=k(D,r),c=b(r),d=n.useRef(null),p=(0,a.s)(t,d,l.onTriggerChange),m=n.useRef(!1),h=n.useRef(!1),g=n.useCallback(()=>m.current=!1,[]);return n.useEffect(()=>()=>document.removeEventListener("pointerup",g),[g]),(0,v.jsx)(u.Mz,{asChild:!0,...c,children:(0,v.jsx)(f.sG.button,{"aria-describedby":l.open?l.contentId:void 0,"data-state":l.stateAttribute,...i,ref:p,onPointerMove:(0,o.m)(e.onPointerMove,e=>{"touch"===e.pointerType||h.current||s.isPointerInTransitRef.current||(l.onTriggerEnter(),h.current=!0)}),onPointerLeave:(0,o.m)(e.onPointerLeave,()=>{l.onTriggerLeave(),h.current=!1}),onPointerDown:(0,o.m)(e.onPointerDown,()=>{l.open&&l.onClose(),m.current=!0,document.addEventListener("pointerup",g,{once:!0})}),onFocus:(0,o.m)(e.onFocus,()=>{m.current||l.onOpen()}),onBlur:(0,o.m)(e.onBlur,l.onClose),onClick:(0,o.m)(e.onClick,l.onClose)})})});j.displayName=D;var M="TooltipPortal",[P,N]=g(M,{forceMount:void 0}),L=e=>{let{__scopeTooltip:t,forceMount:r,children:n,container:o}=e,a=A(M,t);return(0,v.jsx)(P,{scope:t,forceMount:r,children:(0,v.jsx)(d.C,{present:r||a.open,children:(0,v.jsx)(c.Z,{asChild:!0,container:o,children:n})})})};L.displayName=M;var O="TooltipContent",_=n.forwardRef((e,t)=>{let r=N(O,e.__scopeTooltip),{forceMount:n=r.forceMount,side:o="top",...a}=e,i=A(O,e.__scopeTooltip);return(0,v.jsx)(d.C,{present:n||i.open,children:i.disableHoverableContent?(0,v.jsx)(z,{side:o,...a,ref:t}):(0,v.jsx)(I,{side:o,...a,ref:t})})}),I=n.forwardRef((e,t)=>{let r=A(O,e.__scopeTooltip),o=k(O,e.__scopeTooltip),i=n.useRef(null),l=(0,a.s)(t,i),[s,u]=n.useState(null),{trigger:c,onClose:d}=r,f=i.current,{onPointerInTransitChange:p}=o,m=n.useCallback(()=>{u(null),p(!1)},[p]),h=n.useCallback((e,t)=>{let r=e.currentTarget,n={x:e.clientX,y:e.clientY},o=function(e,t){let r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),a=Math.abs(t.left-e.x);switch(Math.min(r,n,o,a)){case a:return"left";case o:return"right";case r:return"top";case n:return"bottom";default:throw Error("unreachable")}}(n,r.getBoundingClientRect());u(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let r=0;r<e.length;r++){let n=e[r];for(;t.length>=2;){let e=t[t.length-1],r=t[t.length-2];if((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x))t.pop();else break}t.push(n)}t.pop();let r=[];for(let t=e.length-1;t>=0;t--){let n=e[t];for(;r.length>=2;){let e=r[r.length-1],t=r[r.length-2];if((e.x-t.x)*(n.y-t.y)>=(e.y-t.y)*(n.x-t.x))r.pop();else break}r.push(n)}return(r.pop(),1===t.length&&1===r.length&&t[0].x===r[0].x&&t[0].y===r[0].y)?t:t.concat(r)}(t)}([...function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r})}return n}(n,o),...function(e){let{top:t,right:r,bottom:n,left:o}=e;return[{x:o,y:t},{x:r,y:t},{x:r,y:n},{x:o,y:n}]}(t.getBoundingClientRect())])),p(!0)},[p]);return n.useEffect(()=>()=>m(),[m]),n.useEffect(()=>{if(c&&f){let e=e=>h(e,f),t=e=>h(e,c);return c.addEventListener("pointerleave",e),f.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),f.removeEventListener("pointerleave",t)}}},[c,f,h,m]),n.useEffect(()=>{if(s){let e=e=>{let t=e.target,r={x:e.clientX,y:e.clientY},n=(null==c?void 0:c.contains(t))||(null==f?void 0:f.contains(t)),o=!function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,a=t.length-1;e<t.length;a=e++){let i=t[e],l=t[a],s=i.x,u=i.y,c=l.x,d=l.y;u>n!=d>n&&r<(c-s)*(n-u)/(d-u)+s&&(o=!o)}return o}(r,s);n?m():o&&(m(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,f,s,d,m]),(0,v.jsx)(z,{...e,ref:l})}),[F,V]=g(S,{isInside:!1}),B=(0,p.createSlottable)("TooltipContent"),z=n.forwardRef((e,t)=>{let{__scopeTooltip:r,children:o,"aria-label":a,onEscapeKeyDown:i,onPointerDownOutside:s,...c}=e,d=A(O,r),f=b(r),{onClose:p}=d;return n.useEffect(()=>(document.addEventListener(x,p),()=>document.removeEventListener(x,p)),[p]),n.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(d.trigger))&&p()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,p]),(0,v.jsx)(l.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:p,children:(0,v.jsxs)(u.UC,{"data-state":d.stateAttribute,...f,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,v.jsx)(B,{children:o}),(0,v.jsx)(F,{scope:r,isInside:!0,children:(0,v.jsx)(h.bL,{id:d.contentId,role:"tooltip",children:a||o})})]})})});_.displayName=O;var H="TooltipArrow",W=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,o=b(r);return V(H,r).isInside?null:(0,v.jsx)(u.i3,{...o,...n,ref:t})});W.displayName=H;var U=C,G=T,K=j,q=L,Y=_,X=W},2247:(e,t,r)=>{r.d(t,{l$:()=>k,oR:()=>y});var n=r(4545),o=r(1076);let a=e=>{switch(e){case"success":return s;case"info":return c;case"warning":return u;case"error":return d;default:return null}},i=Array(12).fill(0),l=e=>{let{visible:t,className:r}=e;return n.createElement("div",{className:["sonner-loading-wrapper",r].filter(Boolean).join(" "),"data-visible":t},n.createElement("div",{className:"sonner-spinner"},i.map((e,t)=>n.createElement("div",{className:"sonner-loading-bar",key:"spinner-bar-".concat(t)}))))},s=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),u=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),c=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),d=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),f=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},n.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),n.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"})),p=()=>{let[e,t]=n.useState(document.hidden);return n.useEffect(()=>{let e=()=>{t(document.hidden)};return document.addEventListener("visibilitychange",e),()=>window.removeEventListener("visibilitychange",e)},[]),e},m=1;class h{constructor(){this.subscribe=e=>(this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);this.subscribers.splice(t,1)}),this.publish=e=>{this.subscribers.forEach(t=>t(e))},this.addToast=e=>{this.publish(e),this.toasts=[...this.toasts,e]},this.create=e=>{var t;let{message:r,...n}=e,o="number"==typeof(null==e?void 0:e.id)||(null==(t=e.id)?void 0:t.length)>0?e.id:m++,a=this.toasts.find(e=>e.id===o),i=void 0===e.dismissible||e.dismissible;return this.dismissedToasts.has(o)&&this.dismissedToasts.delete(o),a?this.toasts=this.toasts.map(t=>t.id===o?(this.publish({...t,...e,id:o,title:r}),{...t,...e,id:o,dismissible:i,title:r}):t):this.addToast({title:r,...n,dismissible:i,id:o}),o},this.dismiss=e=>(e?(this.dismissedToasts.add(e),requestAnimationFrame(()=>this.subscribers.forEach(t=>t({id:e,dismiss:!0})))):this.toasts.forEach(e=>{this.subscribers.forEach(t=>t({id:e.id,dismiss:!0}))}),e),this.message=(e,t)=>this.create({...t,message:e}),this.error=(e,t)=>this.create({...t,message:e,type:"error"}),this.success=(e,t)=>this.create({...t,type:"success",message:e}),this.info=(e,t)=>this.create({...t,type:"info",message:e}),this.warning=(e,t)=>this.create({...t,type:"warning",message:e}),this.loading=(e,t)=>this.create({...t,type:"loading",message:e}),this.promise=(e,t)=>{let r,o;if(!t)return;void 0!==t.loading&&(o=this.create({...t,promise:e,type:"loading",message:t.loading,description:"function"!=typeof t.description?t.description:void 0}));let a=Promise.resolve(e instanceof Function?e():e),i=void 0!==o,l=a.then(async e=>{if(r=["resolve",e],n.isValidElement(e))i=!1,this.create({id:o,type:"default",message:e});else if(g(e)&&!e.ok){i=!1;let r="function"==typeof t.error?await t.error("HTTP error! status: ".concat(e.status)):t.error,a="function"==typeof t.description?await t.description("HTTP error! status: ".concat(e.status)):t.description,l="object"!=typeof r||n.isValidElement(r)?{message:r}:r;this.create({id:o,type:"error",description:a,...l})}else if(e instanceof Error){i=!1;let r="function"==typeof t.error?await t.error(e):t.error,a="function"==typeof t.description?await t.description(e):t.description,l="object"!=typeof r||n.isValidElement(r)?{message:r}:r;this.create({id:o,type:"error",description:a,...l})}else if(void 0!==t.success){i=!1;let r="function"==typeof t.success?await t.success(e):t.success,a="function"==typeof t.description?await t.description(e):t.description,l="object"!=typeof r||n.isValidElement(r)?{message:r}:r;this.create({id:o,type:"success",description:a,...l})}}).catch(async e=>{if(r=["reject",e],void 0!==t.error){i=!1;let r="function"==typeof t.error?await t.error(e):t.error,a="function"==typeof t.description?await t.description(e):t.description,l="object"!=typeof r||n.isValidElement(r)?{message:r}:r;this.create({id:o,type:"error",description:a,...l})}}).finally(()=>{i&&(this.dismiss(o),o=void 0),null==t.finally||t.finally.call(t)}),s=()=>new Promise((e,t)=>l.then(()=>"reject"===r[0]?t(r[1]):e(r[1])).catch(t));return"string"!=typeof o&&"number"!=typeof o?{unwrap:s}:Object.assign(o,{unwrap:s})},this.custom=(e,t)=>{let r=(null==t?void 0:t.id)||m++;return this.create({jsx:e(r),id:r,...t}),r},this.getActiveToasts=()=>this.toasts.filter(e=>!this.dismissedToasts.has(e.id)),this.subscribers=[],this.toasts=[],this.dismissedToasts=new Set}}let v=new h,g=e=>e&&"object"==typeof e&&"ok"in e&&"boolean"==typeof e.ok&&"status"in e&&"number"==typeof e.status,y=Object.assign((e,t)=>{let r=(null==t?void 0:t.id)||m++;return v.addToast({title:e,...t,id:r}),r},{success:v.success,info:v.info,warning:v.warning,error:v.error,custom:v.custom,message:v.message,promise:v.promise,dismiss:v.dismiss,loading:v.loading},{getHistory:()=>v.toasts,getToasts:()=>v.getActiveToasts()});function b(e){return void 0!==e.label}function w(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter(Boolean).join(" ")}!function(e){if(!e||"undefined"==typeof document)return;let t=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css",t.appendChild(r),r.styleSheet?r.styleSheet.cssText=e:r.appendChild(document.createTextNode(e))}("[data-sonner-toaster][dir=ltr],html[dir=ltr]{--toast-icon-margin-start:-3px;--toast-icon-margin-end:4px;--toast-svg-margin-start:-1px;--toast-svg-margin-end:0px;--toast-button-margin-start:auto;--toast-button-margin-end:0;--toast-close-button-start:0;--toast-close-button-end:unset;--toast-close-button-transform:translate(-35%, -35%)}[data-sonner-toaster][dir=rtl],html[dir=rtl]{--toast-icon-margin-start:4px;--toast-icon-margin-end:-3px;--toast-svg-margin-start:0px;--toast-svg-margin-end:-1px;--toast-button-margin-start:0;--toast-button-margin-end:auto;--toast-close-button-start:unset;--toast-close-button-end:0;--toast-close-button-transform:translate(35%, -35%)}[data-sonner-toaster]{position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1:hsl(0, 0%, 99%);--gray2:hsl(0, 0%, 97.3%);--gray3:hsl(0, 0%, 95.1%);--gray4:hsl(0, 0%, 93%);--gray5:hsl(0, 0%, 90.9%);--gray6:hsl(0, 0%, 88.7%);--gray7:hsl(0, 0%, 85.8%);--gray8:hsl(0, 0%, 78%);--gray9:hsl(0, 0%, 56.1%);--gray10:hsl(0, 0%, 52.3%);--gray11:hsl(0, 0%, 43.5%);--gray12:hsl(0, 0%, 9%);--border-radius:8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:0;z-index:999999999;transition:transform .4s ease}@media (hover:none) and (pointer:coarse){[data-sonner-toaster][data-lifted=true]{transform:none}}[data-sonner-toaster][data-x-position=right]{right:var(--offset-right)}[data-sonner-toaster][data-x-position=left]{left:var(--offset-left)}[data-sonner-toaster][data-x-position=center]{left:50%;transform:translateX(-50%)}[data-sonner-toaster][data-y-position=top]{top:var(--offset-top)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--offset-bottom)}[data-sonner-toast]{--y:translateY(100%);--lift-amount:calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:0;overflow-wrap:anywhere}[data-sonner-toast][data-styled=true]{padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px rgba(0,0,0,.1);width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}[data-sonner-toast]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-y-position=top]{top:0;--y:translateY(-100%);--lift:1;--lift-amount:calc(1 * var(--gap))}[data-sonner-toast][data-y-position=bottom]{bottom:0;--y:translateY(100%);--lift:-1;--lift-amount:calc(var(--lift) * var(--gap))}[data-sonner-toast][data-styled=true] [data-description]{font-weight:400;line-height:1.4;color:#3f3f3f}[data-rich-colors=true][data-sonner-toast][data-styled=true] [data-description]{color:inherit}[data-sonner-toaster][data-sonner-theme=dark] [data-description]{color:#e8e8e8}[data-sonner-toast][data-styled=true] [data-title]{font-weight:500;line-height:1.5;color:inherit}[data-sonner-toast][data-styled=true] [data-icon]{display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}[data-sonner-toast][data-promise=true] [data-icon]>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}[data-sonner-toast][data-styled=true] [data-icon]>*{flex-shrink:0}[data-sonner-toast][data-styled=true] [data-icon] svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}[data-sonner-toast][data-styled=true] [data-content]{display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;font-weight:500;cursor:pointer;outline:0;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}[data-sonner-toast][data-styled=true] [data-button]:focus-visible{box-shadow:0 0 0 2px rgba(0,0,0,.4)}[data-sonner-toast][data-styled=true] [data-button]:first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}[data-sonner-toast][data-styled=true] [data-cancel]{color:var(--normal-text);background:rgba(0,0,0,.08)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-styled=true] [data-cancel]{background:rgba(255,255,255,.3)}[data-sonner-toast][data-styled=true] [data-close-button]{position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);background:var(--normal-bg);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast][data-styled=true] [data-close-button]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-styled=true] [data-disabled=true]{cursor:not-allowed}[data-sonner-toast][data-styled=true]:hover [data-close-button]:hover{background:var(--gray2);border-color:var(--gray5)}[data-sonner-toast][data-swiping=true]::before{content:'';position:absolute;left:-100%;right:-100%;height:100%;z-index:-1}[data-sonner-toast][data-y-position=top][data-swiping=true]::before{bottom:50%;transform:scaleY(3) translateY(50%)}[data-sonner-toast][data-y-position=bottom][data-swiping=true]::before{top:50%;transform:scaleY(3) translateY(-50%)}[data-sonner-toast][data-swiping=false][data-removed=true]::before{content:'';position:absolute;inset:0;transform:scaleY(2)}[data-sonner-toast][data-expanded=true]::after{content:'';position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}[data-sonner-toast][data-mounted=true]{--y:translateY(0);opacity:1}[data-sonner-toast][data-expanded=false][data-front=false]{--scale:var(--toasts-before) * 0.05 + 1;--y:translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}[data-sonner-toast]>*{transition:opacity .4s}[data-sonner-toast][data-x-position=right]{right:0}[data-sonner-toast][data-x-position=left]{left:0}[data-sonner-toast][data-expanded=false][data-front=false][data-styled=true]>*{opacity:0}[data-sonner-toast][data-visible=false]{opacity:0;pointer-events:none}[data-sonner-toast][data-mounted=true][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}[data-sonner-toast][data-removed=true][data-front=true][data-swipe-out=false]{--y:translateY(calc(var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=false]{--y:translateY(40%);opacity:0;transition:transform .5s,opacity .2s}[data-sonner-toast][data-removed=true][data-front=false]::before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y,0)) translateX(var(--swipe-amount-x,0));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width:600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-sonner-theme=light]{--normal-bg:#fff;--normal-border:var(--gray4);--normal-text:var(--gray12);--success-bg:hsl(143, 85%, 96%);--success-border:hsl(145, 92%, 87%);--success-text:hsl(140, 100%, 27%);--info-bg:hsl(208, 100%, 97%);--info-border:hsl(221, 91%, 93%);--info-text:hsl(210, 92%, 45%);--warning-bg:hsl(49, 100%, 97%);--warning-border:hsl(49, 91%, 84%);--warning-text:hsl(31, 92%, 45%);--error-bg:hsl(359, 100%, 97%);--error-border:hsl(359, 100%, 94%);--error-text:hsl(360, 100%, 45%)}[data-sonner-toaster][data-sonner-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg:#000;--normal-border:hsl(0, 0%, 20%);--normal-text:var(--gray1)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg:#fff;--normal-border:var(--gray3);--normal-text:var(--gray12)}[data-sonner-toaster][data-sonner-theme=dark]{--normal-bg:#000;--normal-bg-hover:hsl(0, 0%, 12%);--normal-border:hsl(0, 0%, 20%);--normal-border-hover:hsl(0, 0%, 25%);--normal-text:var(--gray1);--success-bg:hsl(150, 100%, 6%);--success-border:hsl(147, 100%, 12%);--success-text:hsl(150, 86%, 65%);--info-bg:hsl(215, 100%, 6%);--info-border:hsl(223, 43%, 17%);--info-text:hsl(216, 87%, 65%);--warning-bg:hsl(64, 100%, 6%);--warning-border:hsl(60, 100%, 9%);--warning-text:hsl(46, 87%, 65%);--error-bg:hsl(358, 76%, 10%);--error-border:hsl(357, 89%, 16%);--error-text:hsl(358, 100%, 81%)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size:16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:first-child{animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}100%{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}100%{opacity:.15}}@media (prefers-reduced-motion){.sonner-loading-bar,[data-sonner-toast],[data-sonner-toast]>*{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}");let x=e=>{var t,r,o,i,s,u,c,d,m,h,v;let{invert:g,toast:y,unstyled:x,interacting:E,setHeights:k,visibleToasts:C,heights:S,index:R,toasts:A,expanded:T,removeToast:D,defaultRichColors:j,closeButton:M,style:P,cancelButtonStyle:N,actionButtonStyle:L,className:O="",descriptionClassName:_="",duration:I,position:F,gap:V,expandByDefault:B,classNames:z,icons:H,closeButtonAriaLabel:W="Close toast"}=e,[U,G]=n.useState(null),[K,q]=n.useState(null),[Y,X]=n.useState(!1),[$,Z]=n.useState(!1),[J,Q]=n.useState(!1),[ee,et]=n.useState(!1),[er,en]=n.useState(!1),[eo,ea]=n.useState(0),[ei,el]=n.useState(0),es=n.useRef(y.duration||I||4e3),eu=n.useRef(null),ec=n.useRef(null),ed=0===R,ef=R+1<=C,ep=y.type,em=!1!==y.dismissible,eh=y.className||"",ev=y.descriptionClassName||"",eg=n.useMemo(()=>S.findIndex(e=>e.toastId===y.id)||0,[S,y.id]),ey=n.useMemo(()=>{var e;return null!=(e=y.closeButton)?e:M},[y.closeButton,M]),eb=n.useMemo(()=>y.duration||I||4e3,[y.duration,I]),ew=n.useRef(0),ex=n.useRef(0),eE=n.useRef(0),ek=n.useRef(null),[eC,eS]=F.split("-"),eR=n.useMemo(()=>S.reduce((e,t,r)=>r>=eg?e:e+t.height,0),[S,eg]),eA=p(),eT=y.invert||g,eD="loading"===ep;ex.current=n.useMemo(()=>eg*V+eR,[eg,eR]),n.useEffect(()=>{es.current=eb},[eb]),n.useEffect(()=>{X(!0)},[]),n.useEffect(()=>{let e=ec.current;if(e){let t=e.getBoundingClientRect().height;return el(t),k(e=>[{toastId:y.id,height:t,position:y.position},...e]),()=>k(e=>e.filter(e=>e.toastId!==y.id))}},[k,y.id]),n.useLayoutEffect(()=>{if(!Y)return;let e=ec.current,t=e.style.height;e.style.height="auto";let r=e.getBoundingClientRect().height;e.style.height=t,el(r),k(e=>e.find(e=>e.toastId===y.id)?e.map(e=>e.toastId===y.id?{...e,height:r}:e):[{toastId:y.id,height:r,position:y.position},...e])},[Y,y.title,y.description,k,y.id,y.jsx,y.action,y.cancel]);let ej=n.useCallback(()=>{Z(!0),ea(ex.current),k(e=>e.filter(e=>e.toastId!==y.id)),setTimeout(()=>{D(y)},200)},[y,D,k,ex]);n.useEffect(()=>{let e;if((!y.promise||"loading"!==ep)&&y.duration!==1/0&&"loading"!==y.type)return T||E||eA?(()=>{if(eE.current<ew.current){let e=new Date().getTime()-ew.current;es.current=es.current-e}eE.current=new Date().getTime()})():es.current!==1/0&&(ew.current=new Date().getTime(),e=setTimeout(()=>{null==y.onAutoClose||y.onAutoClose.call(y,y),ej()},es.current)),()=>clearTimeout(e)},[T,E,y,ep,eA,ej]),n.useEffect(()=>{y.delete&&(ej(),null==y.onDismiss||y.onDismiss.call(y,y))},[ej,y.delete]);let eM=y.icon||(null==H?void 0:H[ep])||a(ep);return n.createElement("li",{tabIndex:0,ref:ec,className:w(O,eh,null==z?void 0:z.toast,null==y?void 0:null==(t=y.classNames)?void 0:t.toast,null==z?void 0:z.default,null==z?void 0:z[ep],null==y?void 0:null==(r=y.classNames)?void 0:r[ep]),"data-sonner-toast":"","data-rich-colors":null!=(h=y.richColors)?h:j,"data-styled":!(y.jsx||y.unstyled||x),"data-mounted":Y,"data-promise":!!y.promise,"data-swiped":er,"data-removed":$,"data-visible":ef,"data-y-position":eC,"data-x-position":eS,"data-index":R,"data-front":ed,"data-swiping":J,"data-dismissible":em,"data-type":ep,"data-invert":eT,"data-swipe-out":ee,"data-swipe-direction":K,"data-expanded":!!(T||B&&Y),style:{"--index":R,"--toasts-before":R,"--z-index":A.length-R,"--offset":"".concat($?eo:ex.current,"px"),"--initial-height":B?"auto":"".concat(ei,"px"),...P,...y.style},onDragEnd:()=>{Q(!1),G(null),ek.current=null},onPointerDown:e=>{!eD&&em&&(eu.current=new Date,ea(ex.current),e.target.setPointerCapture(e.pointerId),"BUTTON"!==e.target.tagName&&(Q(!0),ek.current={x:e.clientX,y:e.clientY}))},onPointerUp:()=>{var e,t,r,n,o;if(ee||!em)return;ek.current=null;let a=Number((null==(e=ec.current)?void 0:e.style.getPropertyValue("--swipe-amount-x").replace("px",""))||0),i=Number((null==(t=ec.current)?void 0:t.style.getPropertyValue("--swipe-amount-y").replace("px",""))||0),l=new Date().getTime()-(null==(r=eu.current)?void 0:r.getTime()),s="x"===U?a:i,u=Math.abs(s)/l;if(Math.abs(s)>=45||u>.11){ea(ex.current),null==y.onDismiss||y.onDismiss.call(y,y),"x"===U?q(a>0?"right":"left"):q(i>0?"down":"up"),ej(),et(!0);return}null==(n=ec.current)||n.style.setProperty("--swipe-amount-x","0px"),null==(o=ec.current)||o.style.setProperty("--swipe-amount-y","0px"),en(!1),Q(!1),G(null)},onPointerMove:t=>{var r,n,o,a;if(!ek.current||!em||(null==(r=window.getSelection())?void 0:r.toString().length)>0)return;let i=t.clientY-ek.current.y,l=t.clientX-ek.current.x,s=null!=(a=e.swipeDirections)?a:function(e){let[t,r]=e.split("-"),n=[];return t&&n.push(t),r&&n.push(r),n}(F);!U&&(Math.abs(l)>1||Math.abs(i)>1)&&G(Math.abs(l)>Math.abs(i)?"x":"y");let u={x:0,y:0},c=e=>1/(1.5+Math.abs(e)/20);if("y"===U){if(s.includes("top")||s.includes("bottom"))if(s.includes("top")&&i<0||s.includes("bottom")&&i>0)u.y=i;else{let e=i*c(i);u.y=Math.abs(e)<Math.abs(i)?e:i}}else if("x"===U&&(s.includes("left")||s.includes("right")))if(s.includes("left")&&l<0||s.includes("right")&&l>0)u.x=l;else{let e=l*c(l);u.x=Math.abs(e)<Math.abs(l)?e:l}(Math.abs(u.x)>0||Math.abs(u.y)>0)&&en(!0),null==(n=ec.current)||n.style.setProperty("--swipe-amount-x","".concat(u.x,"px")),null==(o=ec.current)||o.style.setProperty("--swipe-amount-y","".concat(u.y,"px"))}},ey&&!y.jsx&&"loading"!==ep?n.createElement("button",{"aria-label":W,"data-disabled":eD,"data-close-button":!0,onClick:eD||!em?()=>{}:()=>{ej(),null==y.onDismiss||y.onDismiss.call(y,y)},className:w(null==z?void 0:z.closeButton,null==y?void 0:null==(o=y.classNames)?void 0:o.closeButton)},null!=(v=null==H?void 0:H.close)?v:f):null,(ep||y.icon||y.promise)&&null!==y.icon&&((null==H?void 0:H[ep])!==null||y.icon)?n.createElement("div",{"data-icon":"",className:w(null==z?void 0:z.icon,null==y?void 0:null==(i=y.classNames)?void 0:i.icon)},y.promise||"loading"===y.type&&!y.icon?y.icon||function(){var e,t;return(null==H?void 0:H.loading)?n.createElement("div",{className:w(null==z?void 0:z.loader,null==y?void 0:null==(t=y.classNames)?void 0:t.loader,"sonner-loader"),"data-visible":"loading"===ep},H.loading):n.createElement(l,{className:w(null==z?void 0:z.loader,null==y?void 0:null==(e=y.classNames)?void 0:e.loader),visible:"loading"===ep})}():null,"loading"!==y.type?eM:null):null,n.createElement("div",{"data-content":"",className:w(null==z?void 0:z.content,null==y?void 0:null==(s=y.classNames)?void 0:s.content)},n.createElement("div",{"data-title":"",className:w(null==z?void 0:z.title,null==y?void 0:null==(u=y.classNames)?void 0:u.title)},y.jsx?y.jsx:"function"==typeof y.title?y.title():y.title),y.description?n.createElement("div",{"data-description":"",className:w(_,ev,null==z?void 0:z.description,null==y?void 0:null==(c=y.classNames)?void 0:c.description)},"function"==typeof y.description?y.description():y.description):null),n.isValidElement(y.cancel)?y.cancel:y.cancel&&b(y.cancel)?n.createElement("button",{"data-button":!0,"data-cancel":!0,style:y.cancelButtonStyle||N,onClick:e=>{b(y.cancel)&&em&&(null==y.cancel.onClick||y.cancel.onClick.call(y.cancel,e),ej())},className:w(null==z?void 0:z.cancelButton,null==y?void 0:null==(d=y.classNames)?void 0:d.cancelButton)},y.cancel.label):null,n.isValidElement(y.action)?y.action:y.action&&b(y.action)?n.createElement("button",{"data-button":!0,"data-action":!0,style:y.actionButtonStyle||L,onClick:e=>{b(y.action)&&(null==y.action.onClick||y.action.onClick.call(y.action,e),e.defaultPrevented||ej())},className:w(null==z?void 0:z.actionButton,null==y?void 0:null==(m=y.classNames)?void 0:m.actionButton)},y.action.label):null)};function E(){if("undefined"==typeof window||"undefined"==typeof document)return"ltr";let e=document.documentElement.getAttribute("dir");return"auto"!==e&&e?e:window.getComputedStyle(document.documentElement).direction}let k=n.forwardRef(function(e,t){let{invert:r,position:a="bottom-right",hotkey:i=["altKey","KeyT"],expand:l,closeButton:s,className:u,offset:c,mobileOffset:d,theme:f="light",richColors:p,duration:m,style:h,visibleToasts:g=3,toastOptions:y,dir:b=E(),gap:w=14,icons:k,containerAriaLabel:C="Notifications"}=e,[S,R]=n.useState([]),A=n.useMemo(()=>Array.from(new Set([a].concat(S.filter(e=>e.position).map(e=>e.position)))),[S,a]),[T,D]=n.useState([]),[j,M]=n.useState(!1),[P,N]=n.useState(!1),[L,O]=n.useState("system"!==f?f:"undefined"!=typeof window&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),_=n.useRef(null),I=i.join("+").replace(/Key/g,"").replace(/Digit/g,""),F=n.useRef(null),V=n.useRef(!1),B=n.useCallback(e=>{R(t=>{var r;return(null==(r=t.find(t=>t.id===e.id))?void 0:r.delete)||v.dismiss(e.id),t.filter(t=>{let{id:r}=t;return r!==e.id})})},[]);return n.useEffect(()=>v.subscribe(e=>{if(e.dismiss){requestAnimationFrame(()=>{R(t=>t.map(t=>t.id===e.id?{...t,delete:!0}:t))});return}setTimeout(()=>{o.flushSync(()=>{R(t=>{let r=t.findIndex(t=>t.id===e.id);return -1!==r?[...t.slice(0,r),{...t[r],...e},...t.slice(r+1)]:[e,...t]})})})}),[S]),n.useEffect(()=>{if("system"!==f){O(f);return}if("system"===f&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?O("dark"):O("light")),"undefined"==typeof window)return;let e=window.matchMedia("(prefers-color-scheme: dark)");try{e.addEventListener("change",e=>{let{matches:t}=e;t?O("dark"):O("light")})}catch(t){e.addListener(e=>{let{matches:t}=e;try{t?O("dark"):O("light")}catch(e){console.error(e)}})}},[f]),n.useEffect(()=>{S.length<=1&&M(!1)},[S]),n.useEffect(()=>{let e=e=>{var t,r;i.every(t=>e[t]||e.code===t)&&(M(!0),null==(r=_.current)||r.focus()),"Escape"===e.code&&(document.activeElement===_.current||(null==(t=_.current)?void 0:t.contains(document.activeElement)))&&M(!1)};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[i]),n.useEffect(()=>{if(_.current)return()=>{F.current&&(F.current.focus({preventScroll:!0}),F.current=null,V.current=!1)}},[_.current]),n.createElement("section",{ref:t,"aria-label":"".concat(C," ").concat(I),tabIndex:-1,"aria-live":"polite","aria-relevant":"additions text","aria-atomic":"false",suppressHydrationWarning:!0},A.map((t,o)=>{var a;let[i,f]=t.split("-");return S.length?n.createElement("ol",{key:t,dir:"auto"===b?E():b,tabIndex:-1,ref:_,className:u,"data-sonner-toaster":!0,"data-sonner-theme":L,"data-y-position":i,"data-x-position":f,style:{"--front-toast-height":"".concat((null==(a=T[0])?void 0:a.height)||0,"px"),"--width":"".concat(356,"px"),"--gap":"".concat(w,"px"),...h,...function(e,t){let r={};return[e,t].forEach((e,t)=>{let n=1===t,o=n?"--mobile-offset":"--offset",a=n?"16px":"24px";function i(e){["top","right","bottom","left"].forEach(t=>{r["".concat(o,"-").concat(t)]="number"==typeof e?"".concat(e,"px"):e})}"number"==typeof e||"string"==typeof e?i(e):"object"==typeof e?["top","right","bottom","left"].forEach(t=>{void 0===e[t]?r["".concat(o,"-").concat(t)]=a:r["".concat(o,"-").concat(t)]="number"==typeof e[t]?"".concat(e[t],"px"):e[t]}):i(a)}),r}(c,d)},onBlur:e=>{V.current&&!e.currentTarget.contains(e.relatedTarget)&&(V.current=!1,F.current&&(F.current.focus({preventScroll:!0}),F.current=null))},onFocus:e=>{e.target instanceof HTMLElement&&"false"===e.target.dataset.dismissible||V.current||(V.current=!0,F.current=e.relatedTarget)},onMouseEnter:()=>M(!0),onMouseMove:()=>M(!0),onMouseLeave:()=>{P||M(!1)},onDragEnd:()=>M(!1),onPointerDown:e=>{e.target instanceof HTMLElement&&"false"===e.target.dataset.dismissible||N(!0)},onPointerUp:()=>N(!1)},S.filter(e=>!e.position&&0===o||e.position===t).map((o,a)=>{var i,u;return n.createElement(x,{key:o.id,icons:k,index:a,toast:o,defaultRichColors:p,duration:null!=(i=null==y?void 0:y.duration)?i:m,className:null==y?void 0:y.className,descriptionClassName:null==y?void 0:y.descriptionClassName,invert:r,visibleToasts:g,closeButton:null!=(u=null==y?void 0:y.closeButton)?u:s,interacting:P,position:t,style:null==y?void 0:y.style,unstyled:null==y?void 0:y.unstyled,classNames:null==y?void 0:y.classNames,cancelButtonStyle:null==y?void 0:y.cancelButtonStyle,actionButtonStyle:null==y?void 0:y.actionButtonStyle,closeButtonAriaLabel:null==y?void 0:y.closeButtonAriaLabel,removeToast:B,toasts:S.filter(e=>e.position==o.position),heights:T.filter(e=>e.position==o.position),setHeights:D,expandByDefault:l,gap:w,expanded:j,swipeDirections:e.swipeDirections})})):null}))})},2447:(e,t,r)=>{r.d(t,{F:()=>i});var n=r(7111);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=n.$,i=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return a(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:l}=t,s=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],n=null==l?void 0:l[e];if(null===t)return null;let a=o(t)||o(n);return i[e][a]}),u=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return a(e,s,null==t?void 0:null===(n=t.compoundVariants)||void 0===n?void 0:n.reduce((e,t)=>{let{class:r,className:n,...o}=t;return Object.entries(o).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...l,...u}[t]):({...l,...u})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},2448:(e,t,r)=>{r.d(t,{N:()=>f});var n,o=r(7778),a=r(5993),i=r(1451),l=r(4545),s=r(563),u=r(4489),c=r(8020),d=r(7093);function f(e){let t=e+"CollectionProvider",[r,n]=(0,s.A)(t),[o,a]=r(t,{collectionRef:{current:null},itemMap:new Map}),i=e=>{let{scope:t,children:r}=e,n=l.useRef(null),a=l.useRef(new Map).current;return(0,d.jsx)(o,{scope:t,itemMap:a,collectionRef:n,children:r})};i.displayName=t;let f=e+"CollectionSlot",p=(0,c.createSlot)(f),m=l.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=a(f,r),i=(0,u.s)(t,o.collectionRef);return(0,d.jsx)(p,{ref:i,children:n})});m.displayName=f;let h=e+"CollectionItemSlot",v="data-radix-collection-item",g=(0,c.createSlot)(h),y=l.forwardRef((e,t)=>{let{scope:r,children:n,...o}=e,i=l.useRef(null),s=(0,u.s)(t,i),c=a(h,r);return l.useEffect(()=>(c.itemMap.set(i,{ref:i,...o}),()=>void c.itemMap.delete(i))),(0,d.jsx)(g,{[v]:"",ref:s,children:n})});return y.displayName=h,[{Provider:i,Slot:m,ItemSlot:y},function(t){let r=a(e+"CollectionConsumer",t);return l.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(v,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},n]}var p=new WeakMap;function m(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let r=function(e,t){let r=e.length,n=h(t),o=n>=0?n:r+n;return o<0||o>=r?-1:o}(e,t);return -1===r?void 0:e[r]}function h(e){return e!=e||0===e?0:Math.trunc(e)}n=new WeakMap},2977:(e,t,r)=>{r.d(t,{Mz:()=>U,UC:()=>q,ZL:()=>K,bL:()=>W,l9:()=>G});var n=r(4545),o=r(4157),a=r(4489),i=r(563),l=r(9817),s=r(6929),u=r(5290),c=r(3328),d=r(536),f=r(2071),p=r(3182),m=r(6856),h=r(8020),v=r(3028),g=r(8126),y=r(9803),b=r(7093),w="Popover",[x,E]=(0,i.A)(w,[d.Bk]),k=(0,d.Bk)(),[C,S]=x(w),R=e=>{let{__scopePopover:t,children:r,open:o,defaultOpen:a,onOpenChange:i,modal:l=!1}=e,s=k(t),u=n.useRef(null),[f,p]=n.useState(!1),[m,h]=(0,v.i)({prop:o,defaultProp:null!=a&&a,onChange:i,caller:w});return(0,b.jsx)(d.bL,{...s,children:(0,b.jsx)(C,{scope:t,contentId:(0,c.B)(),triggerRef:u,open:m,onOpenChange:h,onOpenToggle:n.useCallback(()=>h(e=>!e),[h]),hasCustomAnchor:f,onCustomAnchorAdd:n.useCallback(()=>p(!0),[]),onCustomAnchorRemove:n.useCallback(()=>p(!1),[]),modal:l,children:r})})};R.displayName=w;var A="PopoverAnchor",T=n.forwardRef((e,t)=>{let{__scopePopover:r,...o}=e,a=S(A,r),i=k(r),{onCustomAnchorAdd:l,onCustomAnchorRemove:s}=a;return n.useEffect(()=>(l(),()=>s()),[l,s]),(0,b.jsx)(d.Mz,{...i,...o,ref:t})});T.displayName=A;var D="PopoverTrigger",j=n.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,i=S(D,r),l=k(r),s=(0,a.s)(t,i.triggerRef),u=(0,b.jsx)(m.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":H(i.open),...n,ref:s,onClick:(0,o.m)(e.onClick,i.onOpenToggle)});return i.hasCustomAnchor?u:(0,b.jsx)(d.Mz,{asChild:!0,...l,children:u})});j.displayName=D;var M="PopoverPortal",[P,N]=x(M,{forceMount:void 0}),L=e=>{let{__scopePopover:t,forceMount:r,children:n,container:o}=e,a=S(M,t);return(0,b.jsx)(P,{scope:t,forceMount:r,children:(0,b.jsx)(p.C,{present:r||a.open,children:(0,b.jsx)(f.Z,{asChild:!0,container:o,children:n})})})};L.displayName=M;var O="PopoverContent",_=n.forwardRef((e,t)=>{let r=N(O,e.__scopePopover),{forceMount:n=r.forceMount,...o}=e,a=S(O,e.__scopePopover);return(0,b.jsx)(p.C,{present:n||a.open,children:a.modal?(0,b.jsx)(F,{...o,ref:t}):(0,b.jsx)(V,{...o,ref:t})})});_.displayName=O;var I=(0,h.createSlot)("PopoverContent.RemoveScroll"),F=n.forwardRef((e,t)=>{let r=S(O,e.__scopePopover),i=n.useRef(null),l=(0,a.s)(t,i),s=n.useRef(!1);return n.useEffect(()=>{let e=i.current;if(e)return(0,g.Eq)(e)},[]),(0,b.jsx)(y.A,{as:I,allowPinchZoom:!0,children:(0,b.jsx)(B,{...e,ref:l,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),s.current||null===(t=r.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;s.current=2===t.button||r},{checkForDefaultPrevented:!1}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),V=n.forwardRef((e,t)=>{let r=S(O,e.__scopePopover),o=n.useRef(!1),a=n.useRef(!1);return(0,b.jsx)(B,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,i;null===(n=e.onCloseAutoFocus)||void 0===n||n.call(e,t),t.defaultPrevented||(o.current||null===(i=r.triggerRef.current)||void 0===i||i.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{var n,i;null===(n=e.onInteractOutside)||void 0===n||n.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(a.current=!0));let l=t.target;(null===(i=r.triggerRef.current)||void 0===i?void 0:i.contains(l))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),B=n.forwardRef((e,t)=>{let{__scopePopover:r,trapFocus:n,onOpenAutoFocus:o,onCloseAutoFocus:a,disableOutsidePointerEvents:i,onEscapeKeyDown:c,onPointerDownOutside:f,onFocusOutside:p,onInteractOutside:m,...h}=e,v=S(O,r),g=k(r);return(0,s.Oh)(),(0,b.jsx)(u.n,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:o,onUnmountAutoFocus:a,children:(0,b.jsx)(l.qW,{asChild:!0,disableOutsidePointerEvents:i,onInteractOutside:m,onEscapeKeyDown:c,onPointerDownOutside:f,onFocusOutside:p,onDismiss:()=>v.onOpenChange(!1),children:(0,b.jsx)(d.UC,{"data-state":H(v.open),role:"dialog",id:v.contentId,...g,...h,ref:t,style:{...h.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),z="PopoverClose";function H(e){return e?"open":"closed"}n.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,a=S(z,r);return(0,b.jsx)(m.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})}).displayName=z,n.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,o=k(r);return(0,b.jsx)(d.i3,{...o,...n,ref:t})}).displayName="PopoverArrow";var W=R,U=T,G=j,K=L,q=_},3028:(e,t,r)=>{r.d(t,{i:()=>l});var n,o=r(4545),a=r(5366),i=(n||(n=r.t(o,2)))[" useInsertionEffect ".trim().toString()]||a.N;function l({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[a,l,s]=function({defaultProp:e,onChange:t}){let[r,n]=o.useState(e),a=o.useRef(r),l=o.useRef(t);return i(()=>{l.current=t},[t]),o.useEffect(()=>{a.current!==r&&(l.current?.(r),a.current=r)},[r,a]),[r,n,l]}({defaultProp:t,onChange:r}),u=void 0!==e,c=u?e:a;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,n])}return[c,o.useCallback(t=>{if(u){let r="function"==typeof t?t(e):t;r!==e&&s.current?.(r)}else l(t)},[u,e,l,s])]}Symbol("RADIX:SYNC_STATE")},3176:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(436).A)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},3182:(e,t,r)=>{r.d(t,{C:()=>i});var n=r(4545),o=r(4489),a=r(5366),i=e=>{let{present:t,children:r}=e,i=function(e){var t,r;let[o,i]=n.useState(),s=n.useRef(null),u=n.useRef(e),c=n.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return n.useEffect(()=>{let e=l(s.current);c.current="mounted"===d?e:"none"},[d]),(0,a.N)(()=>{let t=s.current,r=u.current;if(r!==e){let n=c.current,o=l(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):r&&n!==o?f("ANIMATION_OUT"):f("UNMOUNT"),u.current=e}},[e,f]),(0,a.N)(()=>{if(o){var e;let t,r=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,n=e=>{let n=l(s.current).includes(e.animationName);if(e.target===o&&n&&(f("ANIMATION_END"),!u.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},a=e=>{e.target===o&&(c.current=l(s.current))};return o.addEventListener("animationstart",a),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{r.clearTimeout(t),o.removeEventListener("animationstart",a),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:n.useCallback(e=>{s.current=e?getComputedStyle(e):null,i(e)},[])}}(t),s="function"==typeof r?r({present:i.isPresent}):n.Children.only(r),u=(0,o.s)(i.ref,function(e){var t,r;let n=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,o=n&&"isReactWarning"in n&&n.isReactWarning;return o?e.ref:(o=(n=null===(r=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===r?void 0:r.get)&&"isReactWarning"in n&&n.isReactWarning)?e.props.ref:e.props.ref||e.ref}(s));return"function"==typeof r||i.isPresent?n.cloneElement(s,{ref:u}):null};function l(e){return(null==e?void 0:e.animationName)||"none"}i.displayName="Presence"},3328:(e,t,r)=>{r.d(t,{B:()=>s});var n,o=r(4545),a=r(5366),i=(n||(n=r.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),l=0;function s(e){let[t,r]=o.useState(i());return(0,a.N)(()=>{e||r(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}},3558:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(436).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},4048:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(436).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},4060:(e,t,r)=>{r.d(t,{Gb:()=>L,Jt:()=>g,Op:()=>A,hZ:()=>x,lN:()=>j,mN:()=>eC,xI:()=>N,xW:()=>R});var n=r(4545),o=e=>"checkbox"===e.type,a=e=>e instanceof Date,i=e=>null==e;let l=e=>"object"==typeof e;var s=e=>!i(e)&&!Array.isArray(e)&&l(e)&&!a(e),u=e=>s(e)&&e.target?o(e.target)?e.target.checked:e.target.value:e,c=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,d=(e,t)=>e.has(c(t)),f=e=>{let t=e.constructor&&e.constructor.prototype;return s(t)&&t.hasOwnProperty("isPrototypeOf")},p="undefined"!=typeof window&&void 0!==window.HTMLElement&&"undefined"!=typeof document;function m(e){let t,r=Array.isArray(e),n="undefined"!=typeof FileList&&e instanceof FileList;if(e instanceof Date)t=new Date(e);else if(e instanceof Set)t=new Set(e);else if(!(!(p&&(e instanceof Blob||n))&&(r||s(e))))return e;else if(t=r?[]:{},r||f(e))for(let r in e)e.hasOwnProperty(r)&&(t[r]=m(e[r]));else t=e;return t}var h=e=>Array.isArray(e)?e.filter(Boolean):[],v=e=>void 0===e,g=(e,t,r)=>{if(!t||!s(e))return r;let n=h(t.split(/[,[\].]+?/)).reduce((e,t)=>i(e)?e:e[t],e);return v(n)||n===e?v(e[t])?r:e[t]:n},y=e=>"boolean"==typeof e,b=e=>/^\w*$/.test(e),w=e=>h(e.replace(/["|']|\]/g,"").split(/\.|\[/)),x=(e,t,r)=>{let n=-1,o=b(t)?[t]:w(t),a=o.length,i=a-1;for(;++n<a;){let t=o[n],a=r;if(n!==i){let r=e[t];a=s(r)||Array.isArray(r)?r:isNaN(+o[n+1])?{}:[]}if("__proto__"===t||"constructor"===t||"prototype"===t)return;e[t]=a,e=e[t]}};let E={BLUR:"blur",FOCUS_OUT:"focusout",CHANGE:"change"},k={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},C={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},S=n.createContext(null),R=()=>n.useContext(S),A=e=>{let{children:t,...r}=e;return n.createElement(S.Provider,{value:r},t)};var T=(e,t,r,n=!0)=>{let o={defaultValues:t._defaultValues};for(let a in e)Object.defineProperty(o,a,{get:()=>(t._proxyFormState[a]!==k.all&&(t._proxyFormState[a]=!n||k.all),r&&(r[a]=!0),e[a])});return o};let D="undefined"!=typeof window?n.useLayoutEffect:n.useEffect;function j(e){let t=R(),{control:r=t.control,disabled:o,name:a,exact:i}=e||{},[l,s]=n.useState(r._formState),u=n.useRef({isDirty:!1,isLoading:!1,dirtyFields:!1,touchedFields:!1,validatingFields:!1,isValidating:!1,isValid:!1,errors:!1});return D(()=>r._subscribe({name:a,formState:u.current,exact:i,callback:e=>{o||s({...r._formState,...e})}}),[a,o,i]),n.useEffect(()=>{u.current.isValid&&r._setValid(!0)},[r]),n.useMemo(()=>T(l,r,u.current,!1),[l,r])}var M=e=>"string"==typeof e,P=(e,t,r,n,o)=>M(e)?(n&&t.watch.add(e),g(r,e,o)):Array.isArray(e)?e.map(e=>(n&&t.watch.add(e),g(r,e))):(n&&(t.watchAll=!0),r);let N=e=>e.render(function(e){let t=R(),{name:r,disabled:o,control:a=t.control,shouldUnregister:i}=e,l=d(a._names.array,r),s=function(e){let t=R(),{control:r=t.control,name:o,defaultValue:a,disabled:i,exact:l}=e||{},s=n.useRef(a),[u,c]=n.useState(r._getWatch(o,s.current));return D(()=>r._subscribe({name:o,formState:{values:!0},exact:l,callback:e=>!i&&c(P(o,r._names,e.values||r._formValues,!1,s.current))}),[o,r,i,l]),n.useEffect(()=>r._removeUnmounted()),u}({control:a,name:r,defaultValue:g(a._formValues,r,g(a._defaultValues,r,e.defaultValue)),exact:!0}),c=j({control:a,name:r,exact:!0}),f=n.useRef(e),p=n.useRef(a.register(r,{...e.rules,value:s,...y(e.disabled)?{disabled:e.disabled}:{}})),h=n.useMemo(()=>Object.defineProperties({},{invalid:{enumerable:!0,get:()=>!!g(c.errors,r)},isDirty:{enumerable:!0,get:()=>!!g(c.dirtyFields,r)},isTouched:{enumerable:!0,get:()=>!!g(c.touchedFields,r)},isValidating:{enumerable:!0,get:()=>!!g(c.validatingFields,r)},error:{enumerable:!0,get:()=>g(c.errors,r)}}),[c,r]),b=n.useCallback(e=>p.current.onChange({target:{value:u(e),name:r},type:E.CHANGE}),[r]),w=n.useCallback(()=>p.current.onBlur({target:{value:g(a._formValues,r),name:r},type:E.BLUR}),[r,a._formValues]),k=n.useCallback(e=>{let t=g(a._fields,r);t&&e&&(t._f.ref={focus:()=>e.focus&&e.focus(),select:()=>e.select&&e.select(),setCustomValidity:t=>e.setCustomValidity(t),reportValidity:()=>e.reportValidity()})},[a._fields,r]),C=n.useMemo(()=>({name:r,value:s,...y(o)||c.disabled?{disabled:c.disabled||o}:{},onChange:b,onBlur:w,ref:k}),[r,o,c.disabled,b,w,k,s]);return n.useEffect(()=>{let e=a._options.shouldUnregister||i;a.register(r,{...f.current.rules,...y(f.current.disabled)?{disabled:f.current.disabled}:{}});let t=(e,t)=>{let r=g(a._fields,e);r&&r._f&&(r._f.mount=t)};if(t(r,!0),e){let e=m(g(a._options.defaultValues,r));x(a._defaultValues,r,e),v(g(a._formValues,r))&&x(a._formValues,r,e)}return l||a.register(r),()=>{(l?e&&!a._state.action:e)?a.unregister(r):t(r,!1)}},[r,a,l,i]),n.useEffect(()=>{a._setDisabledField({disabled:o,name:r})},[o,r,a]),n.useMemo(()=>({field:C,formState:c,fieldState:h}),[C,c,h])}(e));var L=(e,t,r,n,o)=>t?{...r[e],types:{...r[e]&&r[e].types?r[e].types:{},[n]:o||!0}}:{},O=e=>Array.isArray(e)?e:[e],_=()=>{let e=[];return{get observers(){return e},next:t=>{for(let r of e)r.next&&r.next(t)},subscribe:t=>(e.push(t),{unsubscribe:()=>{e=e.filter(e=>e!==t)}}),unsubscribe:()=>{e=[]}}},I=e=>i(e)||!l(e);function F(e,t){if(I(e)||I(t))return e===t;if(a(e)&&a(t))return e.getTime()===t.getTime();let r=Object.keys(e),n=Object.keys(t);if(r.length!==n.length)return!1;for(let o of r){let r=e[o];if(!n.includes(o))return!1;if("ref"!==o){let e=t[o];if(a(r)&&a(e)||s(r)&&s(e)||Array.isArray(r)&&Array.isArray(e)?!F(r,e):r!==e)return!1}}return!0}var V=e=>s(e)&&!Object.keys(e).length,B=e=>"file"===e.type,z=e=>"function"==typeof e,H=e=>{if(!p)return!1;let t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},W=e=>"select-multiple"===e.type,U=e=>"radio"===e.type,G=e=>U(e)||o(e),K=e=>H(e)&&e.isConnected;function q(e,t){let r=Array.isArray(t)?t:b(t)?[t]:w(t),n=1===r.length?e:function(e,t){let r=t.slice(0,-1).length,n=0;for(;n<r;)e=v(e)?n++:e[t[n++]];return e}(e,r),o=r.length-1,a=r[o];return n&&delete n[a],0!==o&&(s(n)&&V(n)||Array.isArray(n)&&function(e){for(let t in e)if(e.hasOwnProperty(t)&&!v(e[t]))return!1;return!0}(n))&&q(e,r.slice(0,-1)),e}var Y=e=>{for(let t in e)if(z(e[t]))return!0;return!1};function X(e,t={}){let r=Array.isArray(e);if(s(e)||r)for(let r in e)Array.isArray(e[r])||s(e[r])&&!Y(e[r])?(t[r]=Array.isArray(e[r])?[]:{},X(e[r],t[r])):i(e[r])||(t[r]=!0);return t}var $=(e,t)=>(function e(t,r,n){let o=Array.isArray(t);if(s(t)||o)for(let o in t)Array.isArray(t[o])||s(t[o])&&!Y(t[o])?v(r)||I(n[o])?n[o]=Array.isArray(t[o])?X(t[o],[]):{...X(t[o])}:e(t[o],i(r)?{}:r[o],n[o]):n[o]=!F(t[o],r[o]);return n})(e,t,X(t));let Z={value:!1,isValid:!1},J={value:!0,isValid:!0};var Q=e=>{if(Array.isArray(e)){if(e.length>1){let t=e.filter(e=>e&&e.checked&&!e.disabled).map(e=>e.value);return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!v(e[0].attributes.value)?v(e[0].value)||""===e[0].value?J:{value:e[0].value,isValid:!0}:J:Z}return Z},ee=(e,{valueAsNumber:t,valueAsDate:r,setValueAs:n})=>v(e)?e:t?""===e?NaN:e?+e:e:r&&M(e)?new Date(e):n?n(e):e;let et={isValid:!1,value:null};var er=e=>Array.isArray(e)?e.reduce((e,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:e,et):et;function en(e){let t=e.ref;return B(t)?t.files:U(t)?er(e.refs).value:W(t)?[...t.selectedOptions].map(({value:e})=>e):o(t)?Q(e.refs).value:ee(v(t.value)?e.ref.value:t.value,e)}var eo=(e,t,r,n)=>{let o={};for(let r of e){let e=g(t,r);e&&x(o,r,e._f)}return{criteriaMode:r,names:[...e],fields:o,shouldUseNativeValidation:n}},ea=e=>e instanceof RegExp,ei=e=>v(e)?e:ea(e)?e.source:s(e)?ea(e.value)?e.value.source:e.value:e,el=e=>({isOnSubmit:!e||e===k.onSubmit,isOnBlur:e===k.onBlur,isOnChange:e===k.onChange,isOnAll:e===k.all,isOnTouch:e===k.onTouched});let es="AsyncFunction";var eu=e=>!!e&&!!e.validate&&!!(z(e.validate)&&e.validate.constructor.name===es||s(e.validate)&&Object.values(e.validate).find(e=>e.constructor.name===es)),ec=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate),ed=(e,t,r)=>!r&&(t.watchAll||t.watch.has(e)||[...t.watch].some(t=>e.startsWith(t)&&/^\.\w+/.test(e.slice(t.length))));let ef=(e,t,r,n)=>{for(let o of r||Object.keys(e)){let r=g(e,o);if(r){let{_f:e,...a}=r;if(e){if(e.refs&&e.refs[0]&&t(e.refs[0],o)&&!n)return!0;else if(e.ref&&t(e.ref,e.name)&&!n)return!0;else if(ef(a,t))break}else if(s(a)&&ef(a,t))break}}};function ep(e,t,r){let n=g(e,r);if(n||b(r))return{error:n,name:r};let o=r.split(".");for(;o.length;){let n=o.join("."),a=g(t,n),i=g(e,n);if(a&&!Array.isArray(a)&&r!==n)break;if(i&&i.type)return{name:n,error:i};if(i&&i.root&&i.root.type)return{name:`${n}.root`,error:i.root};o.pop()}return{name:r}}var em=(e,t,r,n)=>{r(e);let{name:o,...a}=e;return V(a)||Object.keys(a).length>=Object.keys(t).length||Object.keys(a).find(e=>t[e]===(!n||k.all))},eh=(e,t,r)=>!e||!t||e===t||O(e).some(e=>e&&(r?e===t:e.startsWith(t)||t.startsWith(e))),ev=(e,t,r,n,o)=>!o.isOnAll&&(!r&&o.isOnTouch?!(t||e):(r?n.isOnBlur:o.isOnBlur)?!e:(r?!n.isOnChange:!o.isOnChange)||e),eg=(e,t)=>!h(g(e,t)).length&&q(e,t),ey=(e,t,r)=>{let n=O(g(e,r));return x(n,"root",t[r]),x(e,r,n),e},eb=e=>M(e);function ew(e,t,r="validate"){if(eb(e)||Array.isArray(e)&&e.every(eb)||y(e)&&!e)return{type:r,message:eb(e)?e:"",ref:t}}var ex=e=>s(e)&&!ea(e)?e:{value:e,message:""},eE=async(e,t,r,n,a,l)=>{let{ref:u,refs:c,required:d,maxLength:f,minLength:p,min:m,max:h,pattern:b,validate:w,name:x,valueAsNumber:E,mount:k}=e._f,S=g(r,x);if(!k||t.has(x))return{};let R=c?c[0]:u,A=e=>{a&&R.reportValidity&&(R.setCustomValidity(y(e)?"":e||""),R.reportValidity())},T={},D=U(u),j=o(u),P=(E||B(u))&&v(u.value)&&v(S)||H(u)&&""===u.value||""===S||Array.isArray(S)&&!S.length,N=L.bind(null,x,n,T),O=(e,t,r,n=C.maxLength,o=C.minLength)=>{let a=e?t:r;T[x]={type:e?n:o,message:a,ref:u,...N(e?n:o,a)}};if(l?!Array.isArray(S)||!S.length:d&&(!(D||j)&&(P||i(S))||y(S)&&!S||j&&!Q(c).isValid||D&&!er(c).isValid)){let{value:e,message:t}=eb(d)?{value:!!d,message:d}:ex(d);if(e&&(T[x]={type:C.required,message:t,ref:R,...N(C.required,t)},!n))return A(t),T}if(!P&&(!i(m)||!i(h))){let e,t,r=ex(h),o=ex(m);if(i(S)||isNaN(S)){let n=u.valueAsDate||new Date(S),a=e=>new Date(new Date().toDateString()+" "+e),i="time"==u.type,l="week"==u.type;M(r.value)&&S&&(e=i?a(S)>a(r.value):l?S>r.value:n>new Date(r.value)),M(o.value)&&S&&(t=i?a(S)<a(o.value):l?S<o.value:n<new Date(o.value))}else{let n=u.valueAsNumber||(S?+S:S);i(r.value)||(e=n>r.value),i(o.value)||(t=n<o.value)}if((e||t)&&(O(!!e,r.message,o.message,C.max,C.min),!n))return A(T[x].message),T}if((f||p)&&!P&&(M(S)||l&&Array.isArray(S))){let e=ex(f),t=ex(p),r=!i(e.value)&&S.length>+e.value,o=!i(t.value)&&S.length<+t.value;if((r||o)&&(O(r,e.message,t.message),!n))return A(T[x].message),T}if(b&&!P&&M(S)){let{value:e,message:t}=ex(b);if(ea(e)&&!S.match(e)&&(T[x]={type:C.pattern,message:t,ref:u,...N(C.pattern,t)},!n))return A(t),T}if(w){if(z(w)){let e=ew(await w(S,r),R);if(e&&(T[x]={...e,...N(C.validate,e.message)},!n))return A(e.message),T}else if(s(w)){let e={};for(let t in w){if(!V(e)&&!n)break;let o=ew(await w[t](S,r),R,t);o&&(e={...o,...N(t,o.message)},A(o.message),n&&(T[x]=e))}if(!V(e)&&(T[x]={ref:R,...e},!n))return T}}return A(!0),T};let ek={mode:k.onSubmit,reValidateMode:k.onChange,shouldFocusError:!0};function eC(e={}){let t=n.useRef(void 0),r=n.useRef(void 0),[l,c]=n.useState({isDirty:!1,isValidating:!1,isLoading:z(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:z(e.defaultValues)?void 0:e.defaultValues});!t.current&&(t.current={...e.formControl?e.formControl:function(e={}){let t,r={...ek,...e},n={submitCount:0,isDirty:!1,isReady:!1,isLoading:z(r.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:r.errors||{},disabled:r.disabled||!1},l={},c=(s(r.defaultValues)||s(r.values))&&m(r.defaultValues||r.values)||{},f=r.shouldUnregister?{}:m(c),b={action:!1,mount:!1,watch:!1},w={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},C=0,S={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1},R={...S},A={array:_(),state:_()},T=r.criteriaMode===k.all,D=e=>t=>{clearTimeout(C),C=setTimeout(e,t)},j=async e=>{if(!r.disabled&&(S.isValid||R.isValid||e)){let e=r.resolver?V((await X()).errors):await J(l,!0);e!==n.isValid&&A.state.next({isValid:e})}},N=(e,t)=>{!r.disabled&&(S.isValidating||S.validatingFields||R.isValidating||R.validatingFields)&&((e||Array.from(w.mount)).forEach(e=>{e&&(t?x(n.validatingFields,e,t):q(n.validatingFields,e))}),A.state.next({validatingFields:n.validatingFields,isValidating:!V(n.validatingFields)}))},L=(e,t)=>{x(n.errors,e,t),A.state.next({errors:n.errors})},I=(e,t,r,n)=>{let o=g(l,e);if(o){let a=g(f,e,v(r)?g(c,e):r);v(a)||n&&n.defaultChecked||t?x(f,e,t?a:en(o._f)):er(e,a),b.mount&&j()}},U=(e,t,o,a,i)=>{let l=!1,s=!1,u={name:e};if(!r.disabled){if(!o||a){(S.isDirty||R.isDirty)&&(s=n.isDirty,n.isDirty=u.isDirty=Q(),l=s!==u.isDirty);let r=F(g(c,e),t);s=!!g(n.dirtyFields,e),r?q(n.dirtyFields,e):x(n.dirtyFields,e,!0),u.dirtyFields=n.dirtyFields,l=l||(S.dirtyFields||R.dirtyFields)&&!r!==s}if(o){let t=g(n.touchedFields,e);t||(x(n.touchedFields,e,o),u.touchedFields=n.touchedFields,l=l||(S.touchedFields||R.touchedFields)&&t!==o)}l&&i&&A.state.next(u)}return l?u:{}},Y=(e,o,a,i)=>{let l=g(n.errors,e),s=(S.isValid||R.isValid)&&y(o)&&n.isValid!==o;if(r.delayError&&a?(t=D(()=>L(e,a)))(r.delayError):(clearTimeout(C),t=null,a?x(n.errors,e,a):q(n.errors,e)),(a?!F(l,a):l)||!V(i)||s){let t={...i,...s&&y(o)?{isValid:o}:{},errors:n.errors,name:e};n={...n,...t},A.state.next(t)}},X=async e=>{N(e,!0);let t=await r.resolver(f,r.context,eo(e||w.mount,l,r.criteriaMode,r.shouldUseNativeValidation));return N(e),t},Z=async e=>{let{errors:t}=await X(e);if(e)for(let r of e){let e=g(t,r);e?x(n.errors,r,e):q(n.errors,r)}else n.errors=t;return t},J=async(e,t,o={valid:!0})=>{for(let a in e){let i=e[a];if(i){let{_f:e,...l}=i;if(e){let l=w.array.has(e.name),s=i._f&&eu(i._f);s&&S.validatingFields&&N([a],!0);let u=await eE(i,w.disabled,f,T,r.shouldUseNativeValidation&&!t,l);if(s&&S.validatingFields&&N([a]),u[e.name]&&(o.valid=!1,t))break;t||(g(u,e.name)?l?ey(n.errors,u,e.name):x(n.errors,e.name,u[e.name]):q(n.errors,e.name))}V(l)||await J(l,t,o)}}return o.valid},Q=(e,t)=>!r.disabled&&(e&&t&&x(f,e,t),!F(eC(),c)),et=(e,t,r)=>P(e,w,{...b.mount?f:v(t)?c:M(e)?{[e]:t}:t},r,t),er=(e,t,r={})=>{let n=g(l,e),a=t;if(n){let r=n._f;r&&(r.disabled||x(f,e,ee(t,r)),a=H(r.ref)&&i(t)?"":t,W(r.ref)?[...r.ref.options].forEach(e=>e.selected=a.includes(e.value)):r.refs?o(r.ref)?r.refs.forEach(e=>{e.defaultChecked&&e.disabled||(Array.isArray(a)?e.checked=!!a.find(t=>t===e.value):e.checked=a===e.value||!!a)}):r.refs.forEach(e=>e.checked=e.value===a):B(r.ref)?r.ref.value="":(r.ref.value=a,r.ref.type||A.state.next({name:e,values:m(f)})))}(r.shouldDirty||r.shouldTouch)&&U(e,a,r.shouldTouch,r.shouldDirty,!0),r.shouldValidate&&ex(e)},ea=(e,t,r)=>{for(let n in t){if(!t.hasOwnProperty(n))return;let o=t[n],i=e+"."+n,u=g(l,i);(w.array.has(e)||s(o)||u&&!u._f)&&!a(o)?ea(i,o,r):er(i,o,r)}},es=(e,t,r={})=>{let o=g(l,e),a=w.array.has(e),s=m(t);x(f,e,s),a?(A.array.next({name:e,values:m(f)}),(S.isDirty||S.dirtyFields||R.isDirty||R.dirtyFields)&&r.shouldDirty&&A.state.next({name:e,dirtyFields:$(c,f),isDirty:Q(e,s)})):!o||o._f||i(s)?er(e,s,r):ea(e,s,r),ed(e,w)&&A.state.next({...n}),A.state.next({name:b.mount?e:void 0,values:m(f)})},eb=async e=>{b.mount=!0;let o=e.target,i=o.name,s=!0,c=g(l,i),d=e=>{s=Number.isNaN(e)||a(e)&&isNaN(e.getTime())||F(e,g(f,i,e))},p=el(r.mode),h=el(r.reValidateMode);if(c){let a,v,y=o.type?en(c._f):u(e),b=e.type===E.BLUR||e.type===E.FOCUS_OUT,k=!ec(c._f)&&!r.resolver&&!g(n.errors,i)&&!c._f.deps||ev(b,g(n.touchedFields,i),n.isSubmitted,h,p),C=ed(i,w,b);x(f,i,y),b?(c._f.onBlur&&c._f.onBlur(e),t&&t(0)):c._f.onChange&&c._f.onChange(e);let D=U(i,y,b),M=!V(D)||C;if(b||A.state.next({name:i,type:e.type,values:m(f)}),k)return(S.isValid||R.isValid)&&("onBlur"===r.mode?b&&j():b||j()),M&&A.state.next({name:i,...C?{}:D});if(!b&&C&&A.state.next({...n}),r.resolver){let{errors:e}=await X([i]);if(d(y),s){let t=ep(n.errors,l,i),r=ep(e,l,t.name||i);a=r.error,i=r.name,v=V(e)}}else N([i],!0),a=(await eE(c,w.disabled,f,T,r.shouldUseNativeValidation))[i],N([i]),d(y),s&&(a?v=!1:(S.isValid||R.isValid)&&(v=await J(l,!0)));s&&(c._f.deps&&ex(c._f.deps),Y(i,v,a,D))}},ew=(e,t)=>{if(g(n.errors,t)&&e.focus)return e.focus(),1},ex=async(e,t={})=>{let o,a,i=O(e);if(r.resolver){let t=await Z(v(e)?e:i);o=V(t),a=e?!i.some(e=>g(t,e)):o}else e?((a=(await Promise.all(i.map(async e=>{let t=g(l,e);return await J(t&&t._f?{[e]:t}:t)}))).every(Boolean))||n.isValid)&&j():a=o=await J(l);return A.state.next({...!M(e)||(S.isValid||R.isValid)&&o!==n.isValid?{}:{name:e},...r.resolver||!e?{isValid:o}:{},errors:n.errors}),t.shouldFocus&&!a&&ef(l,ew,e?i:w.mount),a},eC=e=>{let t={...b.mount?f:c};return v(e)?t:M(e)?g(t,e):e.map(e=>g(t,e))},eS=(e,t)=>({invalid:!!g((t||n).errors,e),isDirty:!!g((t||n).dirtyFields,e),error:g((t||n).errors,e),isValidating:!!g(n.validatingFields,e),isTouched:!!g((t||n).touchedFields,e)}),eR=(e,t,r)=>{let o=(g(l,e,{_f:{}})._f||{}).ref,{ref:a,message:i,type:s,...u}=g(n.errors,e)||{};x(n.errors,e,{...u,...t,ref:o}),A.state.next({name:e,errors:n.errors,isValid:!1}),r&&r.shouldFocus&&o&&o.focus&&o.focus()},eA=e=>A.state.subscribe({next:t=>{eh(e.name,t.name,e.exact)&&em(t,e.formState||S,eO,e.reRenderRoot)&&e.callback({values:{...f},...n,...t})}}).unsubscribe,eT=(e,t={})=>{for(let o of e?O(e):w.mount)w.mount.delete(o),w.array.delete(o),t.keepValue||(q(l,o),q(f,o)),t.keepError||q(n.errors,o),t.keepDirty||q(n.dirtyFields,o),t.keepTouched||q(n.touchedFields,o),t.keepIsValidating||q(n.validatingFields,o),r.shouldUnregister||t.keepDefaultValue||q(c,o);A.state.next({values:m(f)}),A.state.next({...n,...t.keepDirty?{isDirty:Q()}:{}}),t.keepIsValid||j()},eD=({disabled:e,name:t})=>{(y(e)&&b.mount||e||w.disabled.has(t))&&(e?w.disabled.add(t):w.disabled.delete(t))},ej=(e,t={})=>{let n=g(l,e),o=y(t.disabled)||y(r.disabled);return x(l,e,{...n||{},_f:{...n&&n._f?n._f:{ref:{name:e}},name:e,mount:!0,...t}}),w.mount.add(e),n?eD({disabled:y(t.disabled)?t.disabled:r.disabled,name:e}):I(e,!0,t.value),{...o?{disabled:t.disabled||r.disabled}:{},...r.progressive?{required:!!t.required,min:ei(t.min),max:ei(t.max),minLength:ei(t.minLength),maxLength:ei(t.maxLength),pattern:ei(t.pattern)}:{},name:e,onChange:eb,onBlur:eb,ref:o=>{if(o){ej(e,t),n=g(l,e);let r=v(o.value)&&o.querySelectorAll&&o.querySelectorAll("input,select,textarea")[0]||o,a=G(r),i=n._f.refs||[];(a?i.find(e=>e===r):r===n._f.ref)||(x(l,e,{_f:{...n._f,...a?{refs:[...i.filter(K),r,...Array.isArray(g(c,e))?[{}]:[]],ref:{type:r.type,name:e}}:{ref:r}}}),I(e,!1,void 0,r))}else(n=g(l,e,{}))._f&&(n._f.mount=!1),(r.shouldUnregister||t.shouldUnregister)&&!(d(w.array,e)&&b.action)&&w.unMount.add(e)}}},eM=()=>r.shouldFocusError&&ef(l,ew,w.mount),eP=(e,t)=>async o=>{let a;o&&(o.preventDefault&&o.preventDefault(),o.persist&&o.persist());let i=m(f);if(A.state.next({isSubmitting:!0}),r.resolver){let{errors:e,values:t}=await X();n.errors=e,i=t}else await J(l);if(w.disabled.size)for(let e of w.disabled)x(i,e,void 0);if(q(n.errors,"root"),V(n.errors)){A.state.next({errors:{}});try{await e(i,o)}catch(e){a=e}}else t&&await t({...n.errors},o),eM(),setTimeout(eM);if(A.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:V(n.errors)&&!a,submitCount:n.submitCount+1,errors:n.errors}),a)throw a},eN=(e,t={})=>{let o=e?m(e):c,a=m(o),i=V(e),s=i?c:a;if(t.keepDefaultValues||(c=o),!t.keepValues){if(t.keepDirtyValues)for(let e of Array.from(new Set([...w.mount,...Object.keys($(c,f))])))g(n.dirtyFields,e)?x(s,e,g(f,e)):es(e,g(s,e));else{if(p&&v(e))for(let e of w.mount){let t=g(l,e);if(t&&t._f){let e=Array.isArray(t._f.refs)?t._f.refs[0]:t._f.ref;if(H(e)){let t=e.closest("form");if(t){t.reset();break}}}}for(let e of w.mount)es(e,g(s,e))}f=m(s),A.array.next({values:{...s}}),A.state.next({values:{...s}})}w={mount:t.keepDirtyValues?w.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},b.mount=!S.isValid||!!t.keepIsValid||!!t.keepDirtyValues,b.watch=!!r.shouldUnregister,A.state.next({submitCount:t.keepSubmitCount?n.submitCount:0,isDirty:!i&&(t.keepDirty?n.isDirty:!!(t.keepDefaultValues&&!F(e,c))),isSubmitted:!!t.keepIsSubmitted&&n.isSubmitted,dirtyFields:i?{}:t.keepDirtyValues?t.keepDefaultValues&&f?$(c,f):n.dirtyFields:t.keepDefaultValues&&e?$(c,e):t.keepDirty?n.dirtyFields:{},touchedFields:t.keepTouched?n.touchedFields:{},errors:t.keepErrors?n.errors:{},isSubmitSuccessful:!!t.keepIsSubmitSuccessful&&n.isSubmitSuccessful,isSubmitting:!1})},eL=(e,t)=>eN(z(e)?e(f):e,t),eO=e=>{n={...n,...e}},e_={control:{register:ej,unregister:eT,getFieldState:eS,handleSubmit:eP,setError:eR,_subscribe:eA,_runSchema:X,_focusError:eM,_getWatch:et,_getDirty:Q,_setValid:j,_setFieldArray:(e,t=[],o,a,i=!0,s=!0)=>{if(a&&o&&!r.disabled){if(b.action=!0,s&&Array.isArray(g(l,e))){let t=o(g(l,e),a.argA,a.argB);i&&x(l,e,t)}if(s&&Array.isArray(g(n.errors,e))){let t=o(g(n.errors,e),a.argA,a.argB);i&&x(n.errors,e,t),eg(n.errors,e)}if((S.touchedFields||R.touchedFields)&&s&&Array.isArray(g(n.touchedFields,e))){let t=o(g(n.touchedFields,e),a.argA,a.argB);i&&x(n.touchedFields,e,t)}(S.dirtyFields||R.dirtyFields)&&(n.dirtyFields=$(c,f)),A.state.next({name:e,isDirty:Q(e,t),dirtyFields:n.dirtyFields,errors:n.errors,isValid:n.isValid})}else x(f,e,t)},_setDisabledField:eD,_setErrors:e=>{n.errors=e,A.state.next({errors:n.errors,isValid:!1})},_getFieldArray:e=>h(g(b.mount?f:c,e,r.shouldUnregister?g(c,e,[]):[])),_reset:eN,_resetDefaultValues:()=>z(r.defaultValues)&&r.defaultValues().then(e=>{eL(e,r.resetOptions),A.state.next({isLoading:!1})}),_removeUnmounted:()=>{for(let e of w.unMount){let t=g(l,e);t&&(t._f.refs?t._f.refs.every(e=>!K(e)):!K(t._f.ref))&&eT(e)}w.unMount=new Set},_disableForm:e=>{y(e)&&(A.state.next({disabled:e}),ef(l,(t,r)=>{let n=g(l,r);n&&(t.disabled=n._f.disabled||e,Array.isArray(n._f.refs)&&n._f.refs.forEach(t=>{t.disabled=n._f.disabled||e}))},0,!1))},_subjects:A,_proxyFormState:S,get _fields(){return l},get _formValues(){return f},get _state(){return b},set _state(value){b=value},get _defaultValues(){return c},get _names(){return w},set _names(value){w=value},get _formState(){return n},get _options(){return r},set _options(value){r={...r,...value}}},subscribe:e=>(b.mount=!0,R={...R,...e.formState},eA({...e,formState:R})),trigger:ex,register:ej,handleSubmit:eP,watch:(e,t)=>z(e)?A.state.subscribe({next:r=>e(et(void 0,t),r)}):et(e,t,!0),setValue:es,getValues:eC,reset:eL,resetField:(e,t={})=>{g(l,e)&&(v(t.defaultValue)?es(e,m(g(c,e))):(es(e,t.defaultValue),x(c,e,m(t.defaultValue))),t.keepTouched||q(n.touchedFields,e),t.keepDirty||(q(n.dirtyFields,e),n.isDirty=t.defaultValue?Q(e,m(g(c,e))):Q()),!t.keepError&&(q(n.errors,e),S.isValid&&j()),A.state.next({...n}))},clearErrors:e=>{e&&O(e).forEach(e=>q(n.errors,e)),A.state.next({errors:e?n.errors:{}})},unregister:eT,setError:eR,setFocus:(e,t={})=>{let r=g(l,e),n=r&&r._f;if(n){let e=n.refs?n.refs[0]:n.ref;e.focus&&(e.focus(),t.shouldSelect&&z(e.select)&&e.select())}},getFieldState:eS};return{...e_,formControl:e_}}(e),formState:l},e.formControl&&e.defaultValues&&!z(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions));let f=t.current.control;return f._options=e,D(()=>{let e=f._subscribe({formState:f._proxyFormState,callback:()=>c({...f._formState}),reRenderRoot:!0});return c(e=>({...e,isReady:!0})),f._formState.isReady=!0,e},[f]),n.useEffect(()=>f._disableForm(e.disabled),[f,e.disabled]),n.useEffect(()=>{e.mode&&(f._options.mode=e.mode),e.reValidateMode&&(f._options.reValidateMode=e.reValidateMode)},[f,e.mode,e.reValidateMode]),n.useEffect(()=>{e.errors&&(f._setErrors(e.errors),f._focusError())},[f,e.errors]),n.useEffect(()=>{e.shouldUnregister&&f._subjects.state.next({values:f._getWatch()})},[f,e.shouldUnregister]),n.useEffect(()=>{if(f._proxyFormState.isDirty){let e=f._getDirty();e!==l.isDirty&&f._subjects.state.next({isDirty:e})}},[f,l.isDirty]),n.useEffect(()=>{e.values&&!F(e.values,r.current)?(f._reset(e.values,f._options.resetOptions),r.current=e.values,c(e=>({...e}))):f._resetDefaultValues()},[f,e.values]),n.useEffect(()=>{f._state.mount||(f._setValid(),f._state.mount=!0),f._state.watch&&(f._state.watch=!1,f._subjects.state.next({...f._formState})),f._removeUnmounted()}),t.current.formState=T(l,f),t.current}},4157:(e,t,r)=>{r.d(t,{m:()=>n});function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}},4489:(e,t,r)=>{r.d(t,{s:()=>i,t:()=>a});var n=r(4545);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function a(...e){return t=>{let r=!1,n=e.map(e=>{let n=o(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():o(e[t],null)}}}}function i(...e){return n.useCallback(a(...e),e)}},4617:(e,t,r)=>{r.d(t,{sG:()=>i});var n=r(4545);r(1076);var o=r(6255),a=r(7093),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,o.TL)(`Primitive.${t}`),i=n.forwardRef((e,n)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(o?r:t,{...i,ref:n})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{})},5196:(e,t,r)=>{r.d(t,{c:()=>o});var n=r(4545);function o(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},5274:(e,t,r)=>{r.d(t,{H_:()=>ts,UC:()=>to,YJ:()=>ta,q7:()=>tl,VF:()=>td,JU:()=>ti,ZL:()=>tn,z6:()=>tu,hN:()=>tc,bL:()=>tt,wv:()=>tf,l9:()=>tr});var n=r(4545),o=r(4157),a=r(4489),i=r(563),l=r(3028),s=r(6856),u=r(2448),c=r(6084),d=r(9817),f=r(6929),p=r(5290),m=r(3328),h=r(536),v=r(2071),g=r(3182),y=r(5196),b=r(7093),w="rovingFocusGroup.onEntryFocus",x={bubbles:!1,cancelable:!0},E="RovingFocusGroup",[k,C,S]=(0,u.N)(E),[R,A]=(0,i.A)(E,[S]),[T,D]=R(E),j=n.forwardRef((e,t)=>(0,b.jsx)(k.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,b.jsx)(k.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,b.jsx)(M,{...e,ref:t})})}));j.displayName=E;var M=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:i,loop:u=!1,dir:d,currentTabStopId:f,defaultCurrentTabStopId:p,onCurrentTabStopIdChange:m,onEntryFocus:h,preventScrollOnEntryFocus:v=!1,...g}=e,k=n.useRef(null),S=(0,a.s)(t,k),R=(0,c.jH)(d),[A,D]=(0,l.i)({prop:f,defaultProp:null!=p?p:null,onChange:m,caller:E}),[j,M]=n.useState(!1),P=(0,y.c)(h),N=C(r),L=n.useRef(!1),[_,I]=n.useState(0);return n.useEffect(()=>{let e=k.current;if(e)return e.addEventListener(w,P),()=>e.removeEventListener(w,P)},[P]),(0,b.jsx)(T,{scope:r,orientation:i,dir:R,loop:u,currentTabStopId:A,onItemFocus:n.useCallback(e=>D(e),[D]),onItemShiftTab:n.useCallback(()=>M(!0),[]),onFocusableItemAdd:n.useCallback(()=>I(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>I(e=>e-1),[]),children:(0,b.jsx)(s.sG.div,{tabIndex:j||0===_?-1:0,"data-orientation":i,...g,ref:S,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{L.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!L.current;if(e.target===e.currentTarget&&t&&!j){let t=new CustomEvent(w,x);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=N().filter(e=>e.focusable);O([e.find(e=>e.active),e.find(e=>e.id===A),...e].filter(Boolean).map(e=>e.ref.current),v)}}L.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>M(!1))})})}),P="RovingFocusGroupItem",N=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:a=!0,active:i=!1,tabStopId:l,children:u,...c}=e,d=(0,m.B)(),f=l||d,p=D(P,r),h=p.currentTabStopId===f,v=C(r),{onFocusableItemAdd:g,onFocusableItemRemove:y,currentTabStopId:w}=p;return n.useEffect(()=>{if(a)return g(),()=>y()},[a,g,y]),(0,b.jsx)(k.ItemSlot,{scope:r,id:f,focusable:a,active:i,children:(0,b.jsx)(s.sG.span,{tabIndex:h?0:-1,"data-orientation":p.orientation,...c,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a?p.onItemFocus(f):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>p.onItemFocus(f)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){p.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return L[o]}(e,p.orientation,p.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=v().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=p.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>O(r))}}),children:"function"==typeof u?u({isCurrentTabStop:h,hasTabStop:null!=w}):u})})});N.displayName=P;var L={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function O(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var _=r(8020),I=r(8126),F=r(9803),V=["Enter"," "],B=["ArrowUp","PageDown","End"],z=["ArrowDown","PageUp","Home",...B],H={ltr:[...V,"ArrowRight"],rtl:[...V,"ArrowLeft"]},W={ltr:["ArrowLeft"],rtl:["ArrowRight"]},U="Menu",[G,K,q]=(0,u.N)(U),[Y,X]=(0,i.A)(U,[q,h.Bk,A]),$=(0,h.Bk)(),Z=A(),[J,Q]=Y(U),[ee,et]=Y(U),er=e=>{let{__scopeMenu:t,open:r=!1,children:o,dir:a,onOpenChange:i,modal:l=!0}=e,s=$(t),[u,d]=n.useState(null),f=n.useRef(!1),p=(0,y.c)(i),m=(0,c.jH)(a);return n.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,b.jsx)(h.bL,{...s,children:(0,b.jsx)(J,{scope:t,open:r,onOpenChange:p,content:u,onContentChange:d,children:(0,b.jsx)(ee,{scope:t,onClose:n.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:m,modal:l,children:o})})})};er.displayName=U;var en=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=$(r);return(0,b.jsx)(h.Mz,{...o,...n,ref:t})});en.displayName="MenuAnchor";var eo="MenuPortal",[ea,ei]=Y(eo,{forceMount:void 0}),el=e=>{let{__scopeMenu:t,forceMount:r,children:n,container:o}=e,a=Q(eo,t);return(0,b.jsx)(ea,{scope:t,forceMount:r,children:(0,b.jsx)(g.C,{present:r||a.open,children:(0,b.jsx)(v.Z,{asChild:!0,container:o,children:n})})})};el.displayName=eo;var es="MenuContent",[eu,ec]=Y(es),ed=n.forwardRef((e,t)=>{let r=ei(es,e.__scopeMenu),{forceMount:n=r.forceMount,...o}=e,a=Q(es,e.__scopeMenu),i=et(es,e.__scopeMenu);return(0,b.jsx)(G.Provider,{scope:e.__scopeMenu,children:(0,b.jsx)(g.C,{present:n||a.open,children:(0,b.jsx)(G.Slot,{scope:e.__scopeMenu,children:i.modal?(0,b.jsx)(ef,{...o,ref:t}):(0,b.jsx)(ep,{...o,ref:t})})})})}),ef=n.forwardRef((e,t)=>{let r=Q(es,e.__scopeMenu),i=n.useRef(null),l=(0,a.s)(t,i);return n.useEffect(()=>{let e=i.current;if(e)return(0,I.Eq)(e)},[]),(0,b.jsx)(eh,{...e,ref:l,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),ep=n.forwardRef((e,t)=>{let r=Q(es,e.__scopeMenu);return(0,b.jsx)(eh,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),em=(0,_.createSlot)("MenuContent.ScrollLock"),eh=n.forwardRef((e,t)=>{let{__scopeMenu:r,loop:i=!1,trapFocus:l,onOpenAutoFocus:s,onCloseAutoFocus:u,disableOutsidePointerEvents:c,onEntryFocus:m,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:w,onDismiss:x,disableOutsideScroll:E,...k}=e,C=Q(es,r),S=et(es,r),R=$(r),A=Z(r),T=K(r),[D,M]=n.useState(null),P=n.useRef(null),N=(0,a.s)(t,P,C.onContentChange),L=n.useRef(0),O=n.useRef(""),_=n.useRef(0),I=n.useRef(null),V=n.useRef("right"),H=n.useRef(0),W=E?F.A:n.Fragment,U=e=>{var t,r;let n=O.current+e,o=T().filter(e=>!e.disabled),a=document.activeElement,i=null===(t=o.find(e=>e.ref.current===a))||void 0===t?void 0:t.textValue,l=function(e,t,r){var n;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=(n=Math.max(r?e.indexOf(r):-1,0),e.map((t,r)=>e[(n+r)%e.length]));1===o.length&&(a=a.filter(e=>e!==r));let i=a.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return i!==r?i:void 0}(o.map(e=>e.textValue),n,i),s=null===(r=o.find(e=>e.textValue===l))||void 0===r?void 0:r.ref.current;!function e(t){O.current=t,window.clearTimeout(L.current),""!==t&&(L.current=window.setTimeout(()=>e(""),1e3))}(n),s&&setTimeout(()=>s.focus())};n.useEffect(()=>()=>window.clearTimeout(L.current),[]),(0,f.Oh)();let G=n.useCallback(e=>{var t,r;return V.current===(null===(t=I.current)||void 0===t?void 0:t.side)&&function(e,t){return!!t&&function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,a=t.length-1;e<t.length;a=e++){let i=t[e],l=t[a],s=i.x,u=i.y,c=l.x,d=l.y;u>n!=d>n&&r<(c-s)*(n-u)/(d-u)+s&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,null===(r=I.current)||void 0===r?void 0:r.area)},[]);return(0,b.jsx)(eu,{scope:r,searchRef:O,onItemEnter:n.useCallback(e=>{G(e)&&e.preventDefault()},[G]),onItemLeave:n.useCallback(e=>{var t;G(e)||(null===(t=P.current)||void 0===t||t.focus(),M(null))},[G]),onTriggerLeave:n.useCallback(e=>{G(e)&&e.preventDefault()},[G]),pointerGraceTimerRef:_,onPointerGraceIntentChange:n.useCallback(e=>{I.current=e},[]),children:(0,b.jsx)(W,{...E?{as:em,allowPinchZoom:!0}:void 0,children:(0,b.jsx)(p.n,{asChild:!0,trapped:l,onMountAutoFocus:(0,o.m)(s,e=>{var t;e.preventDefault(),null===(t=P.current)||void 0===t||t.focus({preventScroll:!0})}),onUnmountAutoFocus:u,children:(0,b.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:w,onDismiss:x,children:(0,b.jsx)(j,{asChild:!0,...A,dir:S.dir,orientation:"vertical",loop:i,currentTabStopId:D,onCurrentTabStopIdChange:M,onEntryFocus:(0,o.m)(m,e=>{S.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,b.jsx)(h.UC,{role:"menu","aria-orientation":"vertical","data-state":ez(C.open),"data-radix-menu-content":"",dir:S.dir,...R,...k,ref:N,style:{outline:"none",...k.style},onKeyDown:(0,o.m)(k.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,n=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!r&&n&&U(e.key));let o=P.current;if(e.target!==o||!z.includes(e.key))return;e.preventDefault();let a=T().filter(e=>!e.disabled).map(e=>e.ref.current);B.includes(e.key)&&a.reverse(),function(e){let t=document.activeElement;for(let r of e)if(r===t||(r.focus(),document.activeElement!==t))return}(a)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(L.current),O.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eU(e=>{let t=e.target,r=H.current!==e.clientX;e.currentTarget.contains(t)&&r&&(V.current=e.clientX>H.current?"right":"left",H.current=e.clientX)}))})})})})})})});ed.displayName=es;var ev=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,b.jsx)(s.sG.div,{role:"group",...n,ref:t})});ev.displayName="MenuGroup";var eg=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,b.jsx)(s.sG.div,{...n,ref:t})});eg.displayName="MenuLabel";var ey="MenuItem",eb="menu.itemSelect",ew=n.forwardRef((e,t)=>{let{disabled:r=!1,onSelect:i,...l}=e,u=n.useRef(null),c=et(ey,e.__scopeMenu),d=ec(ey,e.__scopeMenu),f=(0,a.s)(t,u),p=n.useRef(!1);return(0,b.jsx)(ex,{...l,ref:f,disabled:r,onClick:(0,o.m)(e.onClick,()=>{let e=u.current;if(!r&&e){let t=new CustomEvent(eb,{bubbles:!0,cancelable:!0});e.addEventListener(eb,e=>null==i?void 0:i(e),{once:!0}),(0,s.hO)(e,t),t.defaultPrevented?p.current=!1:c.onClose()}}),onPointerDown:t=>{var r;null===(r=e.onPointerDown)||void 0===r||r.call(e,t),p.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{var t;p.current||null===(t=e.currentTarget)||void 0===t||t.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=""!==d.searchRef.current;!r&&(!t||" "!==e.key)&&V.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ew.displayName=ey;var ex=n.forwardRef((e,t)=>{let{__scopeMenu:r,disabled:i=!1,textValue:l,...u}=e,c=ec(ey,r),d=Z(r),f=n.useRef(null),p=(0,a.s)(t,f),[m,h]=n.useState(!1),[v,g]=n.useState("");return n.useEffect(()=>{let e=f.current;if(e){var t;g((null!==(t=e.textContent)&&void 0!==t?t:"").trim())}},[u.children]),(0,b.jsx)(G.ItemSlot,{scope:r,disabled:i,textValue:null!=l?l:v,children:(0,b.jsx)(N,{asChild:!0,...d,focusable:!i,children:(0,b.jsx)(s.sG.div,{role:"menuitem","data-highlighted":m?"":void 0,"aria-disabled":i||void 0,"data-disabled":i?"":void 0,...u,ref:p,onPointerMove:(0,o.m)(e.onPointerMove,eU(e=>{i?c.onItemLeave(e):(c.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eU(e=>c.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>h(!0)),onBlur:(0,o.m)(e.onBlur,()=>h(!1))})})})}),eE=n.forwardRef((e,t)=>{let{checked:r=!1,onCheckedChange:n,...a}=e;return(0,b.jsx)(ej,{scope:e.__scopeMenu,checked:r,children:(0,b.jsx)(ew,{role:"menuitemcheckbox","aria-checked":eH(r)?"mixed":r,...a,ref:t,"data-state":eW(r),onSelect:(0,o.m)(a.onSelect,()=>null==n?void 0:n(!!eH(r)||!r),{checkForDefaultPrevented:!1})})})});eE.displayName="MenuCheckboxItem";var ek="MenuRadioGroup",[eC,eS]=Y(ek,{value:void 0,onValueChange:()=>{}}),eR=n.forwardRef((e,t)=>{let{value:r,onValueChange:n,...o}=e,a=(0,y.c)(n);return(0,b.jsx)(eC,{scope:e.__scopeMenu,value:r,onValueChange:a,children:(0,b.jsx)(ev,{...o,ref:t})})});eR.displayName=ek;var eA="MenuRadioItem",eT=n.forwardRef((e,t)=>{let{value:r,...n}=e,a=eS(eA,e.__scopeMenu),i=r===a.value;return(0,b.jsx)(ej,{scope:e.__scopeMenu,checked:i,children:(0,b.jsx)(ew,{role:"menuitemradio","aria-checked":i,...n,ref:t,"data-state":eW(i),onSelect:(0,o.m)(n.onSelect,()=>{var e;return null===(e=a.onValueChange)||void 0===e?void 0:e.call(a,r)},{checkForDefaultPrevented:!1})})})});eT.displayName=eA;var eD="MenuItemIndicator",[ej,eM]=Y(eD,{checked:!1}),eP=n.forwardRef((e,t)=>{let{__scopeMenu:r,forceMount:n,...o}=e,a=eM(eD,r);return(0,b.jsx)(g.C,{present:n||eH(a.checked)||!0===a.checked,children:(0,b.jsx)(s.sG.span,{...o,ref:t,"data-state":eW(a.checked)})})});eP.displayName=eD;var eN=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,b.jsx)(s.sG.div,{role:"separator","aria-orientation":"horizontal",...n,ref:t})});eN.displayName="MenuSeparator";var eL=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=$(r);return(0,b.jsx)(h.i3,{...o,...n,ref:t})});eL.displayName="MenuArrow";var[eO,e_]=Y("MenuSub"),eI="MenuSubTrigger",eF=n.forwardRef((e,t)=>{let r=Q(eI,e.__scopeMenu),i=et(eI,e.__scopeMenu),l=e_(eI,e.__scopeMenu),s=ec(eI,e.__scopeMenu),u=n.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:d}=s,f={__scopeMenu:e.__scopeMenu},p=n.useCallback(()=>{u.current&&window.clearTimeout(u.current),u.current=null},[]);return n.useEffect(()=>p,[p]),n.useEffect(()=>{let e=c.current;return()=>{window.clearTimeout(e),d(null)}},[c,d]),(0,b.jsx)(en,{asChild:!0,...f,children:(0,b.jsx)(ex,{id:l.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":l.contentId,"data-state":ez(r.open),...e,ref:(0,a.t)(t,l.onTriggerChange),onClick:t=>{var n;null===(n=e.onClick)||void 0===n||n.call(e,t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eU(t=>{s.onItemEnter(t),t.defaultPrevented||e.disabled||r.open||u.current||(s.onPointerGraceIntentChange(null),u.current=window.setTimeout(()=>{r.onOpenChange(!0),p()},100))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eU(e=>{var t,n;p();let o=null===(t=r.content)||void 0===t?void 0:t.getBoundingClientRect();if(o){let t=null===(n=r.content)||void 0===n?void 0:n.dataset.side,a="right"===t,i=o[a?"left":"right"],l=o[a?"right":"left"];s.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:i,y:o.top},{x:l,y:o.top},{x:l,y:o.bottom},{x:i,y:o.bottom}],side:t}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>s.onPointerGraceIntentChange(null),300)}else{if(s.onTriggerLeave(e),e.defaultPrevented)return;s.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,t=>{let n=""!==s.searchRef.current;if(!e.disabled&&(!n||" "!==t.key)&&H[i.dir].includes(t.key)){var o;r.onOpenChange(!0),null===(o=r.content)||void 0===o||o.focus(),t.preventDefault()}})})})});eF.displayName=eI;var eV="MenuSubContent",eB=n.forwardRef((e,t)=>{let r=ei(es,e.__scopeMenu),{forceMount:i=r.forceMount,...l}=e,s=Q(es,e.__scopeMenu),u=et(es,e.__scopeMenu),c=e_(eV,e.__scopeMenu),d=n.useRef(null),f=(0,a.s)(t,d);return(0,b.jsx)(G.Provider,{scope:e.__scopeMenu,children:(0,b.jsx)(g.C,{present:i||s.open,children:(0,b.jsx)(G.Slot,{scope:e.__scopeMenu,children:(0,b.jsx)(eh,{id:c.contentId,"aria-labelledby":c.triggerId,...l,ref:f,align:"start",side:"rtl"===u.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var t;u.isUsingKeyboardRef.current&&(null===(t=d.current)||void 0===t||t.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==c.trigger&&s.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{u.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),r=W[u.dir].includes(e.key);if(t&&r){var n;s.onOpenChange(!1),null===(n=c.trigger)||void 0===n||n.focus(),e.preventDefault()}})})})})})});function ez(e){return e?"open":"closed"}function eH(e){return"indeterminate"===e}function eW(e){return eH(e)?"indeterminate":e?"checked":"unchecked"}function eU(e){return t=>"mouse"===t.pointerType?e(t):void 0}eB.displayName=eV;var eG="DropdownMenu",[eK,eq]=(0,i.A)(eG,[X]),eY=X(),[eX,e$]=eK(eG),eZ=e=>{let{__scopeDropdownMenu:t,children:r,dir:o,open:a,defaultOpen:i,onOpenChange:s,modal:u=!0}=e,c=eY(t),d=n.useRef(null),[f,p]=(0,l.i)({prop:a,defaultProp:null!=i&&i,onChange:s,caller:eG});return(0,b.jsx)(eX,{scope:t,triggerId:(0,m.B)(),triggerRef:d,contentId:(0,m.B)(),open:f,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:u,children:(0,b.jsx)(er,{...c,open:f,onOpenChange:p,dir:o,modal:u,children:r})})};eZ.displayName=eG;var eJ="DropdownMenuTrigger",eQ=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,disabled:n=!1,...i}=e,l=e$(eJ,r),u=eY(r);return(0,b.jsx)(en,{asChild:!0,...u,children:(0,b.jsx)(s.sG.button,{type:"button",id:l.triggerId,"aria-haspopup":"menu","aria-expanded":l.open,"aria-controls":l.open?l.contentId:void 0,"data-state":l.open?"open":"closed","data-disabled":n?"":void 0,disabled:n,...i,ref:(0,a.t)(t,l.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{n||0!==e.button||!1!==e.ctrlKey||(l.onOpenToggle(),l.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!n&&(["Enter"," "].includes(e.key)&&l.onOpenToggle(),"ArrowDown"===e.key&&l.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eQ.displayName=eJ;var e0=e=>{let{__scopeDropdownMenu:t,...r}=e,n=eY(t);return(0,b.jsx)(el,{...n,...r})};e0.displayName="DropdownMenuPortal";var e1="DropdownMenuContent",e5=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...a}=e,i=e$(e1,r),l=eY(r),s=n.useRef(!1);return(0,b.jsx)(ed,{id:i.contentId,"aria-labelledby":i.triggerId,...l,...a,ref:t,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;s.current||null===(t=i.triggerRef.current)||void 0===t||t.focus(),s.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,n=2===t.button||r;(!i.modal||n)&&(s.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});e5.displayName=e1;var e2=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eY(r);return(0,b.jsx)(ev,{...o,...n,ref:t})});e2.displayName="DropdownMenuGroup";var e4=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eY(r);return(0,b.jsx)(eg,{...o,...n,ref:t})});e4.displayName="DropdownMenuLabel";var e3=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eY(r);return(0,b.jsx)(ew,{...o,...n,ref:t})});e3.displayName="DropdownMenuItem";var e6=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eY(r);return(0,b.jsx)(eE,{...o,...n,ref:t})});e6.displayName="DropdownMenuCheckboxItem";var e9=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eY(r);return(0,b.jsx)(eR,{...o,...n,ref:t})});e9.displayName="DropdownMenuRadioGroup";var e8=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eY(r);return(0,b.jsx)(eT,{...o,...n,ref:t})});e8.displayName="DropdownMenuRadioItem";var e7=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eY(r);return(0,b.jsx)(eP,{...o,...n,ref:t})});e7.displayName="DropdownMenuItemIndicator";var te=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eY(r);return(0,b.jsx)(eN,{...o,...n,ref:t})});te.displayName="DropdownMenuSeparator",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eY(r);return(0,b.jsx)(eL,{...o,...n,ref:t})}).displayName="DropdownMenuArrow",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eY(r);return(0,b.jsx)(eF,{...o,...n,ref:t})}).displayName="DropdownMenuSubTrigger",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eY(r);return(0,b.jsx)(eB,{...o,...n,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var tt=eZ,tr=eQ,tn=e0,to=e5,ta=e2,ti=e4,tl=e3,ts=e6,tu=e9,tc=e8,td=e7,tf=te},5290:(e,t,r)=>{r.d(t,{n:()=>d});var n=r(4545),o=r(4489),a=r(6856),i=r(5196),l=r(7093),s="focusScope.autoFocusOnMount",u="focusScope.autoFocusOnUnmount",c={bubbles:!1,cancelable:!0},d=n.forwardRef((e,t)=>{let{loop:r=!1,trapped:d=!1,onMountAutoFocus:v,onUnmountAutoFocus:g,...y}=e,[b,w]=n.useState(null),x=(0,i.c)(v),E=(0,i.c)(g),k=n.useRef(null),C=(0,o.s)(t,e=>w(e)),S=n.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;n.useEffect(()=>{if(d){let e=function(e){if(S.paused||!b)return;let t=e.target;b.contains(t)?k.current=t:m(k.current,{select:!0})},t=function(e){if(S.paused||!b)return;let t=e.relatedTarget;null===t||b.contains(t)||m(k.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&m(b)});return b&&r.observe(b,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[d,b,S.paused]),n.useEffect(()=>{if(b){h.add(S);let e=document.activeElement;if(!b.contains(e)){let t=new CustomEvent(s,c);b.addEventListener(s,x),b.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=document.activeElement;for(let n of e)if(m(n,{select:t}),document.activeElement!==r)return}(f(b).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&m(b))}return()=>{b.removeEventListener(s,x),setTimeout(()=>{let t=new CustomEvent(u,c);b.addEventListener(u,E),b.dispatchEvent(t),t.defaultPrevented||m(null!=e?e:document.body,{select:!0}),b.removeEventListener(u,E),h.remove(S)},0)}}},[b,x,E,S]);let R=n.useCallback(e=>{if(!r&&!d||S.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,n=document.activeElement;if(t&&n){let t=e.currentTarget,[o,a]=function(e){let t=f(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&a?e.shiftKey||n!==a?e.shiftKey&&n===o&&(e.preventDefault(),r&&m(a,{select:!0})):(e.preventDefault(),r&&m(o,{select:!0})):n===t&&e.preventDefault()}},[r,d,S.paused]);return(0,l.jsx)(a.sG.div,{tabIndex:-1,...y,ref:C,onKeyDown:R})});function f(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function p(e,t){for(let r of e)if(!function(e,t){let{upTo:r}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===r||e!==r);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function m(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}d.displayName="FocusScope";var h=function(){let e=[];return{add(t){let r=e[0];t!==r&&(null==r||r.pause()),(e=v(e,t)).unshift(t)},remove(t){var r;null===(r=(e=v(e,t))[0])||void 0===r||r.resume()}}}();function v(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}},5366:(e,t,r)=>{r.d(t,{N:()=>o});var n=r(4545),o=globalThis?.document?n.useLayoutEffect:()=>{}},5471:(e,t,r)=>{r.d(t,{G$:()=>Y,Hs:()=>x,UC:()=>er,VY:()=>eo,ZL:()=>ee,bL:()=>J,bm:()=>ea,hE:()=>en,hJ:()=>et,l9:()=>Q});var n=r(4545),o=r(4157),a=r(4489),i=r(563),l=r(3328),s=r(3028),u=r(9817),c=r(5290),d=r(2071),f=r(3182),p=r(6856),m=r(6929),h=r(9803),v=r(8126),g=r(8020),y=r(7093),b="Dialog",[w,x]=(0,i.A)(b),[E,k]=w(b),C=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:a,onOpenChange:i,modal:u=!0}=e,c=n.useRef(null),d=n.useRef(null),[f,p]=(0,s.i)({prop:o,defaultProp:null!=a&&a,onChange:i,caller:b});return(0,y.jsx)(E,{scope:t,triggerRef:c,contentRef:d,contentId:(0,l.B)(),titleId:(0,l.B)(),descriptionId:(0,l.B)(),open:f,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:u,children:r})};C.displayName=b;var S="DialogTrigger",R=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=k(S,r),l=(0,a.s)(t,i.triggerRef);return(0,y.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":K(i.open),...n,ref:l,onClick:(0,o.m)(e.onClick,i.onOpenToggle)})});R.displayName=S;var A="DialogPortal",[T,D]=w(A,{forceMount:void 0}),j=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:a}=e,i=k(A,t);return(0,y.jsx)(T,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,y.jsx)(f.C,{present:r||i.open,children:(0,y.jsx)(d.Z,{asChild:!0,container:a,children:e})}))})};j.displayName=A;var M="DialogOverlay",P=n.forwardRef((e,t)=>{let r=D(M,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=k(M,e.__scopeDialog);return a.modal?(0,y.jsx)(f.C,{present:n||a.open,children:(0,y.jsx)(L,{...o,ref:t})}):null});P.displayName=M;var N=(0,g.createSlot)("DialogOverlay.RemoveScroll"),L=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=k(M,r);return(0,y.jsx)(h.A,{as:N,allowPinchZoom:!0,shards:[o.contentRef],children:(0,y.jsx)(p.sG.div,{"data-state":K(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),O="DialogContent",_=n.forwardRef((e,t)=>{let r=D(O,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=k(O,e.__scopeDialog);return(0,y.jsx)(f.C,{present:n||a.open,children:a.modal?(0,y.jsx)(I,{...o,ref:t}):(0,y.jsx)(F,{...o,ref:t})})});_.displayName=O;var I=n.forwardRef((e,t)=>{let r=k(O,e.__scopeDialog),i=n.useRef(null),l=(0,a.s)(t,r.contentRef,i);return n.useEffect(()=>{let e=i.current;if(e)return(0,v.Eq)(e)},[]),(0,y.jsx)(V,{...e,ref:l,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=r.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),F=n.forwardRef((e,t)=>{let r=k(O,e.__scopeDialog),o=n.useRef(!1),a=n.useRef(!1);return(0,y.jsx)(V,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,i;null===(n=e.onCloseAutoFocus)||void 0===n||n.call(e,t),t.defaultPrevented||(o.current||null===(i=r.triggerRef.current)||void 0===i||i.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{var n,i;null===(n=e.onInteractOutside)||void 0===n||n.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(a.current=!0));let l=t.target;(null===(i=r.triggerRef.current)||void 0===i?void 0:i.contains(l))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),V=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:l,...s}=e,d=k(O,r),f=n.useRef(null),p=(0,a.s)(t,f);return(0,m.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(c.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:i,onUnmountAutoFocus:l,children:(0,y.jsx)(u.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":K(d.open),...s,ref:p,onDismiss:()=>d.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)($,{titleId:d.titleId}),(0,y.jsx)(Z,{contentRef:f,descriptionId:d.descriptionId})]})]})}),B="DialogTitle",z=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=k(B,r);return(0,y.jsx)(p.sG.h2,{id:o.titleId,...n,ref:t})});z.displayName=B;var H="DialogDescription",W=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=k(H,r);return(0,y.jsx)(p.sG.p,{id:o.descriptionId,...n,ref:t})});W.displayName=H;var U="DialogClose",G=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=k(U,r);return(0,y.jsx)(p.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function K(e){return e?"open":"closed"}G.displayName=U;var q="DialogTitleWarning",[Y,X]=(0,i.q)(q,{contentName:O,titleName:B,docsSlug:"dialog"}),$=e=>{let{titleId:t}=e,r=X(q),o="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return n.useEffect(()=>{t&&!document.getElementById(t)&&console.error(o)},[o,t]),null},Z=e=>{let{contentRef:t,descriptionId:r}=e,o=X("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return n.useEffect(()=>{var e;let n=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");r&&n&&!document.getElementById(r)&&console.warn(a)},[a,t,r]),null},J=C,Q=R,ee=j,et=P,er=_,en=z,eo=W,ea=G},5993:(e,t,r)=>{r.d(t,{_:()=>n});function n(e,t,r){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object");t.set(e,r)}},6084:(e,t,r)=>{r.d(t,{jH:()=>a});var n=r(4545);r(7093);var o=n.createContext(void 0);function a(e){let t=n.useContext(o);return e||t||"ltr"}},6132:(e,t,r)=>{r.d(t,{Qg:()=>i,bL:()=>s});var n=r(4545),o=r(6856),a=r(7093),i=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),l=n.forwardRef((e,t)=>(0,a.jsx)(o.sG.span,{...e,ref:t,style:{...i,...e.style}}));l.displayName="VisuallyHidden";var s=l},6255:(e,t,r)=>{r.d(t,{TL:()=>i});var n=r(4545),o=r(4489),a=r(7093);function i(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...a}=e;if(n.isValidElement(r)){var i;let e,l,s=(i=r,(l=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(l=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),u=function(e,t){let r={...t};for(let n in t){let o=e[n],a=t[n];/^on[A-Z]/.test(n)?o&&a?r[n]=(...e)=>{let t=a(...e);return o(...e),t}:o&&(r[n]=o):"style"===n?r[n]={...o,...a}:"className"===n&&(r[n]=[o,a].filter(Boolean).join(" "))}return{...e,...r}}(a,r.props);return r.type!==n.Fragment&&(u.ref=t?(0,o.t)(t,s):s),n.cloneElement(r,u)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:o,...i}=e,l=n.Children.toArray(o),u=l.find(s);if(u){let e=u.props.children,o=l.map(t=>t!==u?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...i,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,o):null})}return(0,a.jsx)(t,{...i,ref:r,children:o})});return r.displayName=`${e}.Slot`,r}var l=Symbol("radix.slottable");function s(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}},6733:(e,t,r)=>{r.d(t,{T:()=>n});function n(){let e,t,r=new Promise((r,n)=>{e=r,t=n});function n(e){Object.assign(r,e),delete r.resolve,delete r.reject}return r.status="pending",r.catch(()=>{}),r.resolve=t=>{n({status:"fulfilled",value:t}),e(t)},r.reject=e=>{n({status:"rejected",reason:e}),t(e)},r}},6856:(e,t,r)=>{r.d(t,{hO:()=>s,sG:()=>l});var n=r(4545),o=r(1076),a=r(8020),i=r(7093),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,a.createSlot)(`Primitive.${t}`),o=n.forwardRef((e,n)=>{let{asChild:o,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(o?r:t,{...a,ref:n})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function s(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},6890:(e,t,r)=>{r.d(t,{Ht:()=>l,jE:()=>i});var n=r(4545),o=r(7093),a=n.createContext(void 0),i=e=>{let t=n.useContext(a);if(e)return e;if(!t)throw Error("No QueryClient set, use QueryClientProvider to set one");return t},l=e=>{let{client:t,children:r}=e;return n.useEffect(()=>(t.mount(),()=>{t.unmount()}),[t]),(0,o.jsx)(a.Provider,{value:t,children:r})}},6920:(e,t,r)=>{r.d(t,{t:()=>a});var n=r(667),o=r(8691),a=new class extends n.Q{#e=!0;#t;#r;constructor(){super(),this.#r=e=>{if(!o.S$&&window.addEventListener){let t=()=>e(!0),r=()=>e(!1);return window.addEventListener("online",t,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",t),window.removeEventListener("offline",r)}}}}onSubscribe(){this.#t||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#r=e,this.#t?.(),this.#t=e(this.setOnline.bind(this))}setOnline(e){this.#e!==e&&(this.#e=e,this.listeners.forEach(t=>{t(e)}))}isOnline(){return this.#e}}},6929:(e,t,r)=>{r.d(t,{Oh:()=>a});var n=r(4545),o=0;function a(){n.useEffect(()=>{var e,t;let r=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=r[0])&&void 0!==e?e:i()),document.body.insertAdjacentElement("beforeend",null!==(t=r[1])&&void 0!==t?t:i()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function i(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},7090:(e,t,r)=>{r.d(t,{b:()=>u});var n=r(4545),o=r(6856),a=r(7093),i="horizontal",l=["horizontal","vertical"],s=n.forwardRef((e,t)=>{var r;let{decorative:n,orientation:s=i,...u}=e,c=(r=s,l.includes(r))?s:i;return(0,a.jsx)(o.sG.div,{"data-orientation":c,...n?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...u,ref:t})});s.displayName="Separator";var u=s},7111:(e,t,r)=>{r.d(t,{$:()=>n});function n(){for(var e,t,r=0,n="",o=arguments.length;r<o;r++)(e=arguments[r])&&(t=function e(t){var r,n,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t)if(Array.isArray(t)){var a=t.length;for(r=0;r<a;r++)t[r]&&(n=e(t[r]))&&(o&&(o+=" "),o+=n)}else for(n in t)t[n]&&(o&&(o+=" "),o+=n);return o}(e))&&(n&&(n+=" "),n+=t);return n}},7449:(e,t,r)=>{r.d(t,{b:()=>l});var n=r(4545),o=r(4617),a=r(7093),i=n.forwardRef((e,t)=>(0,a.jsx)(o.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null===(r=e.onMouseDown)||void 0===r||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var l=i},7554:(e,t,r)=>{r.d(t,{jG:()=>o});var n=e=>setTimeout(e,0),o=function(){let e=[],t=0,r=e=>{e()},o=e=>{e()},a=n,i=n=>{t?e.push(n):a(()=>{r(n)})},l=()=>{let t=e;e=[],t.length&&a(()=>{o(()=>{t.forEach(e=>{r(e)})})})};return{batch:e=>{let r;t++;try{r=e()}finally{--t||l()}return r},batchCalls:e=>(...t)=>{i(()=>{e(...t)})},schedule:i,setNotifyFunction:e=>{r=e},setBatchNotifyFunction:e=>{o=e},setScheduler:e=>{a=e}}}()},7569:(e,t,r)=>{r.d(t,{m:()=>a});var n=r(667),o=r(8691),a=new class extends n.Q{#n;#t;#r;constructor(){super(),this.#r=e=>{if(!o.S$&&window.addEventListener){let t=()=>e();return window.addEventListener("visibilitychange",t,!1),()=>{window.removeEventListener("visibilitychange",t)}}}}onSubscribe(){this.#t||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#r=e,this.#t?.(),this.#t=e(e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()})}setFocused(e){this.#n!==e&&(this.#n=e,this.onFocus())}onFocus(){let e=this.isFocused();this.listeners.forEach(t=>{t(e)})}isFocused(){return"boolean"==typeof this.#n?this.#n:globalThis.document?.visibilityState!=="hidden"}}},7634:(e,t,r)=>{r.d(t,{In:()=>eN,JU:()=>eF,LM:()=>e_,PP:()=>eH,UC:()=>eO,VF:()=>ez,WT:()=>eP,YJ:()=>eI,ZL:()=>eL,bL:()=>ej,l9:()=>eM,p4:()=>eB,q7:()=>eV,wn:()=>eW,wv:()=>eU});var n=r(4545),o=r(1076),a=r(8910),i=r(4157),l=r(2448),s=r(4489),u=r(563),c=r(6084),d=r(9817),f=r(6929),p=r(5290),m=r(3328),h=r(536),v=r(2071),g=r(6856),y=r(8020),b=r(5196),w=r(3028),x=r(5366),E=r(56),k=r(6132),C=r(8126),S=r(9803),R=r(7093),A=[" ","Enter","ArrowUp","ArrowDown"],T=[" ","Enter"],D="Select",[j,M,P]=(0,l.N)(D),[N,L]=(0,u.A)(D,[P,h.Bk]),O=(0,h.Bk)(),[_,I]=N(D),[F,V]=N(D),B=e=>{let{__scopeSelect:t,children:r,open:o,defaultOpen:a,onOpenChange:i,value:l,defaultValue:s,onValueChange:u,dir:d,name:f,autoComplete:p,disabled:v,required:g,form:y}=e,b=O(t),[x,E]=n.useState(null),[k,C]=n.useState(null),[S,A]=n.useState(!1),T=(0,c.jH)(d),[M,P]=(0,w.i)({prop:o,defaultProp:null!=a&&a,onChange:i,caller:D}),[N,L]=(0,w.i)({prop:l,defaultProp:s,onChange:u,caller:D}),I=n.useRef(null),V=!x||y||!!x.closest("form"),[B,z]=n.useState(new Set),H=Array.from(B).map(e=>e.props.value).join(";");return(0,R.jsx)(h.bL,{...b,children:(0,R.jsxs)(_,{required:g,scope:t,trigger:x,onTriggerChange:E,valueNode:k,onValueNodeChange:C,valueNodeHasChildren:S,onValueNodeHasChildrenChange:A,contentId:(0,m.B)(),value:N,onValueChange:L,open:M,onOpenChange:P,dir:T,triggerPointerDownPosRef:I,disabled:v,children:[(0,R.jsx)(j.Provider,{scope:t,children:(0,R.jsx)(F,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{z(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{z(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),V?(0,R.jsxs)(eR,{"aria-hidden":!0,required:g,tabIndex:-1,name:f,autoComplete:p,value:N,onChange:e=>L(e.target.value),disabled:v,form:y,children:[void 0===N?(0,R.jsx)("option",{value:""}):null,Array.from(B)]},H):null]})})};B.displayName=D;var z="SelectTrigger",H=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:o=!1,...a}=e,l=O(r),u=I(z,r),c=u.disabled||o,d=(0,s.s)(t,u.onTriggerChange),f=M(r),p=n.useRef("touch"),[m,v,y]=eT(e=>{let t=f().filter(e=>!e.disabled),r=t.find(e=>e.value===u.value),n=eD(t,e,r);void 0!==n&&u.onValueChange(n.value)}),b=e=>{c||(u.onOpenChange(!0),y()),e&&(u.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,R.jsx)(h.Mz,{asChild:!0,...l,children:(0,R.jsx)(g.sG.button,{type:"button",role:"combobox","aria-controls":u.contentId,"aria-expanded":u.open,"aria-required":u.required,"aria-autocomplete":"none",dir:u.dir,"data-state":u.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":eA(u.value)?"":void 0,...a,ref:d,onClick:(0,i.m)(a.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&b(e)}),onPointerDown:(0,i.m)(a.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(b(e),e.preventDefault())}),onKeyDown:(0,i.m)(a.onKeyDown,e=>{let t=""!==m.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||v(e.key),(!t||" "!==e.key)&&A.includes(e.key)&&(b(),e.preventDefault())})})})});H.displayName=z;var W="SelectValue",U=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:o,children:a,placeholder:i="",...l}=e,u=I(W,r),{onValueNodeHasChildrenChange:c}=u,d=void 0!==a,f=(0,s.s)(t,u.onValueNodeChange);return(0,x.N)(()=>{c(d)},[c,d]),(0,R.jsx)(g.sG.span,{...l,ref:f,style:{pointerEvents:"none"},children:eA(u.value)?(0,R.jsx)(R.Fragment,{children:i}):a})});U.displayName=W;var G=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...o}=e;return(0,R.jsx)(g.sG.span,{"aria-hidden":!0,...o,ref:t,children:n||"▼"})});G.displayName="SelectIcon";var K=e=>(0,R.jsx)(v.Z,{asChild:!0,...e});K.displayName="SelectPortal";var q="SelectContent",Y=n.forwardRef((e,t)=>{let r=I(q,e.__scopeSelect),[a,i]=n.useState();return((0,x.N)(()=>{i(new DocumentFragment)},[]),r.open)?(0,R.jsx)(J,{...e,ref:t}):a?o.createPortal((0,R.jsx)(X,{scope:e.__scopeSelect,children:(0,R.jsx)(j.Slot,{scope:e.__scopeSelect,children:(0,R.jsx)("div",{children:e.children})})}),a):null});Y.displayName=q;var[X,$]=N(q),Z=(0,y.createSlot)("SelectContent.RemoveScroll"),J=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:o="item-aligned",onCloseAutoFocus:a,onEscapeKeyDown:l,onPointerDownOutside:u,side:c,sideOffset:m,align:h,alignOffset:v,arrowPadding:g,collisionBoundary:y,collisionPadding:b,sticky:w,hideWhenDetached:x,avoidCollisions:E,...k}=e,A=I(q,r),[T,D]=n.useState(null),[j,P]=n.useState(null),N=(0,s.s)(t,e=>D(e)),[L,O]=n.useState(null),[_,F]=n.useState(null),V=M(r),[B,z]=n.useState(!1),H=n.useRef(!1);n.useEffect(()=>{if(T)return(0,C.Eq)(T)},[T]),(0,f.Oh)();let W=n.useCallback(e=>{let[t,...r]=V().map(e=>e.ref.current),[n]=r.slice(-1),o=document.activeElement;for(let r of e)if(r===o||(null==r||r.scrollIntoView({block:"nearest"}),r===t&&j&&(j.scrollTop=0),r===n&&j&&(j.scrollTop=j.scrollHeight),null==r||r.focus(),document.activeElement!==o))return},[V,j]),U=n.useCallback(()=>W([L,T]),[W,L,T]);n.useEffect(()=>{B&&U()},[B,U]);let{onOpenChange:G,triggerPointerDownPosRef:K}=A;n.useEffect(()=>{if(T){let e={x:0,y:0},t=t=>{var r,n,o,a;e={x:Math.abs(Math.round(t.pageX)-(null!==(o=null===(r=K.current)||void 0===r?void 0:r.x)&&void 0!==o?o:0)),y:Math.abs(Math.round(t.pageY)-(null!==(a=null===(n=K.current)||void 0===n?void 0:n.y)&&void 0!==a?a:0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():T.contains(r.target)||G(!1),document.removeEventListener("pointermove",t),K.current=null};return null!==K.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[T,G,K]),n.useEffect(()=>{let e=()=>G(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[G]);let[Y,$]=eT(e=>{let t=V().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=eD(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),J=n.useCallback((e,t,r)=>{let n=!H.current&&!r;(void 0!==A.value&&A.value===t||n)&&(O(e),n&&(H.current=!0))},[A.value]),et=n.useCallback(()=>null==T?void 0:T.focus(),[T]),er=n.useCallback((e,t,r)=>{let n=!H.current&&!r;(void 0!==A.value&&A.value===t||n)&&F(e)},[A.value]),en="popper"===o?ee:Q,eo=en===ee?{side:c,sideOffset:m,align:h,alignOffset:v,arrowPadding:g,collisionBoundary:y,collisionPadding:b,sticky:w,hideWhenDetached:x,avoidCollisions:E}:{};return(0,R.jsx)(X,{scope:r,content:T,viewport:j,onViewportChange:P,itemRefCallback:J,selectedItem:L,onItemLeave:et,itemTextRefCallback:er,focusSelectedItem:U,selectedItemText:_,position:o,isPositioned:B,searchRef:Y,children:(0,R.jsx)(S.A,{as:Z,allowPinchZoom:!0,children:(0,R.jsx)(p.n,{asChild:!0,trapped:A.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,i.m)(a,e=>{var t;null===(t=A.trigger)||void 0===t||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,R.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:l,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>A.onOpenChange(!1),children:(0,R.jsx)(en,{role:"listbox",id:A.contentId,"data-state":A.open?"open":"closed",dir:A.dir,onContextMenu:e=>e.preventDefault(),...k,...eo,onPlaced:()=>z(!0),ref:N,style:{display:"flex",flexDirection:"column",outline:"none",...k.style},onKeyDown:(0,i.m)(k.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||$(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=V().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>W(t)),e.preventDefault()}})})})})})})});J.displayName="SelectContentImpl";var Q=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:o,...i}=e,l=I(q,r),u=$(q,r),[c,d]=n.useState(null),[f,p]=n.useState(null),m=(0,s.s)(t,e=>p(e)),h=M(r),v=n.useRef(!1),y=n.useRef(!0),{viewport:b,selectedItem:w,selectedItemText:E,focusSelectedItem:k}=u,C=n.useCallback(()=>{if(l.trigger&&l.valueNode&&c&&f&&b&&w&&E){let e=l.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),r=l.valueNode.getBoundingClientRect(),n=E.getBoundingClientRect();if("rtl"!==l.dir){let o=n.left-t.left,i=r.left-o,l=e.left-i,s=e.width+l,u=Math.max(s,t.width),d=window.innerWidth-10,f=(0,a.q)(i,[10,Math.max(10,d-u)]);c.style.minWidth=s+"px",c.style.left=f+"px"}else{let o=t.right-n.right,i=window.innerWidth-r.right-o,l=window.innerWidth-e.right-i,s=e.width+l,u=Math.max(s,t.width),d=window.innerWidth-10,f=(0,a.q)(i,[10,Math.max(10,d-u)]);c.style.minWidth=s+"px",c.style.right=f+"px"}let i=h(),s=window.innerHeight-20,u=b.scrollHeight,d=window.getComputedStyle(f),p=parseInt(d.borderTopWidth,10),m=parseInt(d.paddingTop,10),g=parseInt(d.borderBottomWidth,10),y=p+m+u+parseInt(d.paddingBottom,10)+g,x=Math.min(5*w.offsetHeight,y),k=window.getComputedStyle(b),C=parseInt(k.paddingTop,10),S=parseInt(k.paddingBottom,10),R=e.top+e.height/2-10,A=w.offsetHeight/2,T=p+m+(w.offsetTop+A);if(T<=R){let e=i.length>0&&w===i[i.length-1].ref.current;c.style.bottom="0px";let t=Math.max(s-R,A+(e?S:0)+(f.clientHeight-b.offsetTop-b.offsetHeight)+g);c.style.height=T+t+"px"}else{let e=i.length>0&&w===i[0].ref.current;c.style.top="0px";let t=Math.max(R,p+b.offsetTop+(e?C:0)+A);c.style.height=t+(y-T)+"px",b.scrollTop=T-R+b.offsetTop}c.style.margin="".concat(10,"px 0"),c.style.minHeight=x+"px",c.style.maxHeight=s+"px",null==o||o(),requestAnimationFrame(()=>v.current=!0)}},[h,l.trigger,l.valueNode,c,f,b,w,E,l.dir,o]);(0,x.N)(()=>C(),[C]);let[S,A]=n.useState();(0,x.N)(()=>{f&&A(window.getComputedStyle(f).zIndex)},[f]);let T=n.useCallback(e=>{e&&!0===y.current&&(C(),null==k||k(),y.current=!1)},[C,k]);return(0,R.jsx)(et,{scope:r,contentWrapper:c,shouldExpandOnScrollRef:v,onScrollButtonChange:T,children:(0,R.jsx)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:S},children:(0,R.jsx)(g.sG.div,{...i,ref:m,style:{boxSizing:"border-box",maxHeight:"100%",...i.style}})})})});Q.displayName="SelectItemAlignedPosition";var ee=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:o=10,...a}=e,i=O(r);return(0,R.jsx)(h.UC,{...i,...a,ref:t,align:n,collisionPadding:o,style:{boxSizing:"border-box",...a.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ee.displayName="SelectPopperPosition";var[et,er]=N(q,{}),en="SelectViewport",eo=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:o,...a}=e,l=$(en,r),u=er(en,r),c=(0,s.s)(t,l.onViewportChange),d=n.useRef(0);return(0,R.jsxs)(R.Fragment,{children:[(0,R.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,R.jsx)(j.Slot,{scope:r,children:(0,R.jsx)(g.sG.div,{"data-radix-select-viewport":"",role:"presentation",...a,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...a.style},onScroll:(0,i.m)(a.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=u;if((null==n?void 0:n.current)&&r){let e=Math.abs(d.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,o=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(o<n){let a=o+e,i=Math.min(n,a),l=a-i;r.style.height=i+"px","0px"===r.style.bottom&&(t.scrollTop=l>0?l:0,r.style.justifyContent="flex-end")}}}d.current=t.scrollTop})})})]})});eo.displayName=en;var ea="SelectGroup",[ei,el]=N(ea),es=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=(0,m.B)();return(0,R.jsx)(ei,{scope:r,id:o,children:(0,R.jsx)(g.sG.div,{role:"group","aria-labelledby":o,...n,ref:t})})});es.displayName=ea;var eu="SelectLabel",ec=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=el(eu,r);return(0,R.jsx)(g.sG.div,{id:o.id,...n,ref:t})});ec.displayName=eu;var ed="SelectItem",[ef,ep]=N(ed),em=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:o,disabled:a=!1,textValue:l,...u}=e,c=I(ed,r),d=$(ed,r),f=c.value===o,[p,h]=n.useState(null!=l?l:""),[v,y]=n.useState(!1),b=(0,s.s)(t,e=>{var t;return null===(t=d.itemRefCallback)||void 0===t?void 0:t.call(d,e,o,a)}),w=(0,m.B)(),x=n.useRef("touch"),E=()=>{a||(c.onValueChange(o),c.onOpenChange(!1))};if(""===o)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,R.jsx)(ef,{scope:r,value:o,disabled:a,textId:w,isSelected:f,onItemTextChange:n.useCallback(e=>{h(t=>{var r;return t||(null!==(r=null==e?void 0:e.textContent)&&void 0!==r?r:"").trim()})},[]),children:(0,R.jsx)(j.ItemSlot,{scope:r,value:o,disabled:a,textValue:p,children:(0,R.jsx)(g.sG.div,{role:"option","aria-labelledby":w,"data-highlighted":v?"":void 0,"aria-selected":f&&v,"data-state":f?"checked":"unchecked","aria-disabled":a||void 0,"data-disabled":a?"":void 0,tabIndex:a?void 0:-1,...u,ref:b,onFocus:(0,i.m)(u.onFocus,()=>y(!0)),onBlur:(0,i.m)(u.onBlur,()=>y(!1)),onClick:(0,i.m)(u.onClick,()=>{"mouse"!==x.current&&E()}),onPointerUp:(0,i.m)(u.onPointerUp,()=>{"mouse"===x.current&&E()}),onPointerDown:(0,i.m)(u.onPointerDown,e=>{x.current=e.pointerType}),onPointerMove:(0,i.m)(u.onPointerMove,e=>{if(x.current=e.pointerType,a){var t;null===(t=d.onItemLeave)||void 0===t||t.call(d)}else"mouse"===x.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,i.m)(u.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null===(t=d.onItemLeave)||void 0===t||t.call(d)}}),onKeyDown:(0,i.m)(u.onKeyDown,e=>{var t;((null===(t=d.searchRef)||void 0===t?void 0:t.current)===""||" "!==e.key)&&(T.includes(e.key)&&E()," "===e.key&&e.preventDefault())})})})})});em.displayName=ed;var eh="SelectItemText",ev=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:a,style:i,...l}=e,u=I(eh,r),c=$(eh,r),d=ep(eh,r),f=V(eh,r),[p,m]=n.useState(null),h=(0,s.s)(t,e=>m(e),d.onItemTextChange,e=>{var t;return null===(t=c.itemTextRefCallback)||void 0===t?void 0:t.call(c,e,d.value,d.disabled)}),v=null==p?void 0:p.textContent,y=n.useMemo(()=>(0,R.jsx)("option",{value:d.value,disabled:d.disabled,children:v},d.value),[d.disabled,d.value,v]),{onNativeOptionAdd:b,onNativeOptionRemove:w}=f;return(0,x.N)(()=>(b(y),()=>w(y)),[b,w,y]),(0,R.jsxs)(R.Fragment,{children:[(0,R.jsx)(g.sG.span,{id:d.textId,...l,ref:h}),d.isSelected&&u.valueNode&&!u.valueNodeHasChildren?o.createPortal(l.children,u.valueNode):null]})});ev.displayName=eh;var eg="SelectItemIndicator",ey=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return ep(eg,r).isSelected?(0,R.jsx)(g.sG.span,{"aria-hidden":!0,...n,ref:t}):null});ey.displayName=eg;var eb="SelectScrollUpButton",ew=n.forwardRef((e,t)=>{let r=$(eb,e.__scopeSelect),o=er(eb,e.__scopeSelect),[a,i]=n.useState(!1),l=(0,s.s)(t,o.onScrollButtonChange);return(0,x.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){i(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),a?(0,R.jsx)(ek,{...e,ref:l,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ew.displayName=eb;var ex="SelectScrollDownButton",eE=n.forwardRef((e,t)=>{let r=$(ex,e.__scopeSelect),o=er(ex,e.__scopeSelect),[a,i]=n.useState(!1),l=(0,s.s)(t,o.onScrollButtonChange);return(0,x.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;i(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),a?(0,R.jsx)(ek,{...e,ref:l,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eE.displayName=ex;var ek=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:o,...a}=e,l=$("SelectScrollButton",r),s=n.useRef(null),u=M(r),c=n.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return n.useEffect(()=>()=>c(),[c]),(0,x.N)(()=>{var e;let t=u().find(e=>e.ref.current===document.activeElement);null==t||null===(e=t.ref.current)||void 0===e||e.scrollIntoView({block:"nearest"})},[u]),(0,R.jsx)(g.sG.div,{"aria-hidden":!0,...a,ref:t,style:{flexShrink:0,...a.style},onPointerDown:(0,i.m)(a.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(o,50))}),onPointerMove:(0,i.m)(a.onPointerMove,()=>{var e;null===(e=l.onItemLeave)||void 0===e||e.call(l),null===s.current&&(s.current=window.setInterval(o,50))}),onPointerLeave:(0,i.m)(a.onPointerLeave,()=>{c()})})}),eC=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,R.jsx)(g.sG.div,{"aria-hidden":!0,...n,ref:t})});eC.displayName="SelectSeparator";var eS="SelectArrow";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=O(r),a=I(eS,r),i=$(eS,r);return a.open&&"popper"===i.position?(0,R.jsx)(h.i3,{...o,...n,ref:t}):null}).displayName=eS;var eR=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:o,...a}=e,i=n.useRef(null),l=(0,s.s)(t,i),u=(0,E.Z)(o);return n.useEffect(()=>{let e=i.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(u!==o&&t){let r=new Event("change",{bubbles:!0});t.call(e,o),e.dispatchEvent(r)}},[u,o]),(0,R.jsx)(g.sG.select,{...a,style:{...k.Qg,...a.style},ref:l,defaultValue:o})});function eA(e){return""===e||void 0===e}function eT(e){let t=(0,b.c)(e),r=n.useRef(""),o=n.useRef(0),a=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),i=n.useCallback(()=>{r.current="",window.clearTimeout(o.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),[r,a,i]}function eD(e,t,r){var n,o;let a=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=r?e.indexOf(r):-1,l=(n=e,o=Math.max(i,0),n.map((e,t)=>n[(o+t)%n.length]));1===a.length&&(l=l.filter(e=>e!==r));let s=l.find(e=>e.textValue.toLowerCase().startsWith(a.toLowerCase()));return s!==r?s:void 0}eR.displayName="SelectBubbleInput";var ej=B,eM=H,eP=U,eN=G,eL=K,eO=Y,e_=eo,eI=es,eF=ec,eV=em,eB=ev,ez=ey,eH=ew,eW=eE,eU=eC},7676:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(436).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},7778:(e,t,r)=>{r.d(t,{_:()=>o});var n=r(9730);function o(e,t){var r=(0,n._)(e,t,"get");return r.get?r.get.call(e):r.value}},7857:(e,t,r)=>{r.d(t,{UC:()=>O,VY:()=>V,ZD:()=>I,ZL:()=>N,bL:()=>M,hE:()=>F,hJ:()=>L,l9:()=>P,rc:()=>_});var n=r(4545),o=r(563),a=r(4489),i=r(5471),l=r(4157),s=r(8020),u=r(7093),c="AlertDialog",[d,f]=(0,o.A)(c,[i.Hs]),p=(0,i.Hs)(),m=e=>{let{__scopeAlertDialog:t,...r}=e,n=p(t);return(0,u.jsx)(i.bL,{...n,...r,modal:!0})};m.displayName=c;var h=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,o=p(r);return(0,u.jsx)(i.l9,{...o,...n,ref:t})});h.displayName="AlertDialogTrigger";var v=e=>{let{__scopeAlertDialog:t,...r}=e,n=p(t);return(0,u.jsx)(i.ZL,{...n,...r})};v.displayName="AlertDialogPortal";var g=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,o=p(r);return(0,u.jsx)(i.hJ,{...o,...n,ref:t})});g.displayName="AlertDialogOverlay";var y="AlertDialogContent",[b,w]=d(y),x=(0,s.createSlottable)("AlertDialogContent"),E=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,children:o,...s}=e,c=p(r),d=n.useRef(null),f=(0,a.s)(t,d),m=n.useRef(null);return(0,u.jsx)(i.G$,{contentName:y,titleName:k,docsSlug:"alert-dialog",children:(0,u.jsx)(b,{scope:r,cancelRef:m,children:(0,u.jsxs)(i.UC,{role:"alertdialog",...c,...s,ref:f,onOpenAutoFocus:(0,l.m)(s.onOpenAutoFocus,e=>{var t;e.preventDefault(),null===(t=m.current)||void 0===t||t.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,u.jsx)(x,{children:o}),(0,u.jsx)(j,{contentRef:d})]})})})});E.displayName=y;var k="AlertDialogTitle",C=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,o=p(r);return(0,u.jsx)(i.hE,{...o,...n,ref:t})});C.displayName=k;var S="AlertDialogDescription",R=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,o=p(r);return(0,u.jsx)(i.VY,{...o,...n,ref:t})});R.displayName=S;var A=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,o=p(r);return(0,u.jsx)(i.bm,{...o,...n,ref:t})});A.displayName="AlertDialogAction";var T="AlertDialogCancel",D=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,{cancelRef:o}=w(T,r),l=p(r),s=(0,a.s)(t,o);return(0,u.jsx)(i.bm,{...l,...n,ref:s})});D.displayName=T;var j=e=>{let{contentRef:t}=e,r="`".concat(y,"` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the `").concat(y,"` by passing a `").concat(S,"` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an `id` and passing the same value to the `aria-describedby` prop in `").concat(y,"`. If the description is confusing or duplicative for sighted users, you can use the `@radix-ui/react-visually-hidden` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog");return n.useEffect(()=>{var e;document.getElementById(null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby"))||console.warn(r)},[r,t]),null},M=m,P=h,N=v,L=g,O=E,_=A,I=D,F=C,V=R},8020:(e,t,r)=>{r.r(t),r.d(t,{Root:()=>l,Slot:()=>l,Slottable:()=>c,createSlot:()=>i,createSlottable:()=>u});var n=r(4545),o=r(4489),a=r(7093);function i(e){let t=function(e){let t=n.forwardRef((e,t)=>{var r,a,i;let l,s,{children:u,...c}=e,d=n.isValidElement(u)?(s=(l=null===(a=Object.getOwnPropertyDescriptor((r=u).props,"ref"))||void 0===a?void 0:a.get)&&"isReactWarning"in l&&l.isReactWarning)?r.ref:(s=(l=null===(i=Object.getOwnPropertyDescriptor(r,"ref"))||void 0===i?void 0:i.get)&&"isReactWarning"in l&&l.isReactWarning)?r.props.ref:r.props.ref||r.ref:void 0,f=(0,o.s)(d,t);if(n.isValidElement(u)){let e=function(e,t){let r={...t};for(let n in t){let o=e[n],a=t[n];/^on[A-Z]/.test(n)?o&&a?r[n]=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let n=a(...t);return o(...t),n}:o&&(r[n]=o):"style"===n?r[n]={...o,...a}:"className"===n&&(r[n]=[o,a].filter(Boolean).join(" "))}return{...e,...r}}(c,u.props);return u.type!==n.Fragment&&(e.ref=f),n.cloneElement(u,e)}return n.Children.count(u)>1?n.Children.only(null):null});return t.displayName="".concat(e,".SlotClone"),t}(e),r=n.forwardRef((e,r)=>{let{children:o,...i}=e,l=n.Children.toArray(o),s=l.find(d);if(s){let e=s.props.children,o=l.map(t=>t!==s?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...i,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,o):null})}return(0,a.jsx)(t,{...i,ref:r,children:o})});return r.displayName="".concat(e,".Slot"),r}var l=i("Slot"),s=Symbol("radix.slottable");function u(e){let t=e=>{let{children:t}=e;return(0,a.jsx)(a.Fragment,{children:t})};return t.displayName="".concat(e,".Slottable"),t.__radixId=s,t}var c=u("Slottable");function d(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===s}},8024:(e,t,r)=>{r.d(t,{LM:()=>Y,OK:()=>X,VM:()=>k,bL:()=>q,lr:()=>L});var n=r(4545),o=r(4617),a=r(3182),i=r(563),l=r(4489),s=r(5196),u=r(6084),c=r(5366),d=r(8910),f=r(4157),p=r(7093),m="ScrollArea",[h,v]=(0,i.A)(m),[g,y]=h(m),b=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,type:a="hover",dir:i,scrollHideDelay:s=600,...c}=e,[d,f]=n.useState(null),[m,h]=n.useState(null),[v,y]=n.useState(null),[b,w]=n.useState(null),[x,E]=n.useState(null),[k,C]=n.useState(0),[S,R]=n.useState(0),[A,T]=n.useState(!1),[D,j]=n.useState(!1),M=(0,l.s)(t,e=>f(e)),P=(0,u.jH)(i);return(0,p.jsx)(g,{scope:r,type:a,dir:P,scrollHideDelay:s,scrollArea:d,viewport:m,onViewportChange:h,content:v,onContentChange:y,scrollbarX:b,onScrollbarXChange:w,scrollbarXEnabled:A,onScrollbarXEnabledChange:T,scrollbarY:x,onScrollbarYChange:E,scrollbarYEnabled:D,onScrollbarYEnabledChange:j,onCornerWidthChange:C,onCornerHeightChange:R,children:(0,p.jsx)(o.sG.div,{dir:P,...c,ref:M,style:{position:"relative","--radix-scroll-area-corner-width":k+"px","--radix-scroll-area-corner-height":S+"px",...e.style}})})});b.displayName=m;var w="ScrollAreaViewport",x=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,children:a,nonce:i,...s}=e,u=y(w,r),c=n.useRef(null),d=(0,l.s)(t,c,u.onViewportChange);return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:i}),(0,p.jsx)(o.sG.div,{"data-radix-scroll-area-viewport":"",...s,ref:d,style:{overflowX:u.scrollbarXEnabled?"scroll":"hidden",overflowY:u.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,p.jsx)("div",{ref:u.onContentChange,style:{minWidth:"100%",display:"table"},children:a})})]})});x.displayName=w;var E="ScrollAreaScrollbar",k=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,a=y(E,e.__scopeScrollArea),{onScrollbarXEnabledChange:i,onScrollbarYEnabledChange:l}=a,s="horizontal"===e.orientation;return n.useEffect(()=>(s?i(!0):l(!0),()=>{s?i(!1):l(!1)}),[s,i,l]),"hover"===a.type?(0,p.jsx)(C,{...o,ref:t,forceMount:r}):"scroll"===a.type?(0,p.jsx)(S,{...o,ref:t,forceMount:r}):"auto"===a.type?(0,p.jsx)(R,{...o,ref:t,forceMount:r}):"always"===a.type?(0,p.jsx)(A,{...o,ref:t}):null});k.displayName=E;var C=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,i=y(E,e.__scopeScrollArea),[l,s]=n.useState(!1);return n.useEffect(()=>{let e=i.scrollArea,t=0;if(e){let r=()=>{window.clearTimeout(t),s(!0)},n=()=>{t=window.setTimeout(()=>s(!1),i.scrollHideDelay)};return e.addEventListener("pointerenter",r),e.addEventListener("pointerleave",n),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",r),e.removeEventListener("pointerleave",n)}}},[i.scrollArea,i.scrollHideDelay]),(0,p.jsx)(a.C,{present:r||l,children:(0,p.jsx)(R,{"data-state":l?"visible":"hidden",...o,ref:t})})}),S=n.forwardRef((e,t)=>{var r;let{forceMount:o,...i}=e,l=y(E,e.__scopeScrollArea),s="horizontal"===e.orientation,u=G(()=>d("SCROLL_END"),100),[c,d]=(r={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},n.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},"hidden"));return n.useEffect(()=>{if("idle"===c){let e=window.setTimeout(()=>d("HIDE"),l.scrollHideDelay);return()=>window.clearTimeout(e)}},[c,l.scrollHideDelay,d]),n.useEffect(()=>{let e=l.viewport,t=s?"scrollLeft":"scrollTop";if(e){let r=e[t],n=()=>{let n=e[t];r!==n&&(d("SCROLL"),u()),r=n};return e.addEventListener("scroll",n),()=>e.removeEventListener("scroll",n)}},[l.viewport,s,d,u]),(0,p.jsx)(a.C,{present:o||"hidden"!==c,children:(0,p.jsx)(A,{"data-state":"hidden"===c?"hidden":"visible",...i,ref:t,onPointerEnter:(0,f.m)(e.onPointerEnter,()=>d("POINTER_ENTER")),onPointerLeave:(0,f.m)(e.onPointerLeave,()=>d("POINTER_LEAVE"))})})}),R=n.forwardRef((e,t)=>{let r=y(E,e.__scopeScrollArea),{forceMount:o,...i}=e,[l,s]=n.useState(!1),u="horizontal"===e.orientation,c=G(()=>{if(r.viewport){let e=r.viewport.offsetWidth<r.viewport.scrollWidth,t=r.viewport.offsetHeight<r.viewport.scrollHeight;s(u?e:t)}},10);return K(r.viewport,c),K(r.content,c),(0,p.jsx)(a.C,{present:o||l,children:(0,p.jsx)(A,{"data-state":l?"visible":"hidden",...i,ref:t})})}),A=n.forwardRef((e,t)=>{let{orientation:r="vertical",...o}=e,a=y(E,e.__scopeScrollArea),i=n.useRef(null),l=n.useRef(0),[s,u]=n.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),c=B(s.viewport,s.content),d={...o,sizes:s,onSizesChange:u,hasThumb:!!(c>0&&c<1),onThumbChange:e=>i.current=e,onThumbPointerUp:()=>l.current=0,onThumbPointerDown:e=>l.current=e};function f(e,t){return function(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"ltr",o=z(r),a=t||o/2,i=r.scrollbar.paddingStart+a,l=r.scrollbar.size-r.scrollbar.paddingEnd-(o-a),s=r.content-r.viewport;return W([i,l],"ltr"===n?[0,s]:[-+s,0])(e)}(e,l.current,s,t)}return"horizontal"===r?(0,p.jsx)(T,{...d,ref:t,onThumbPositionChange:()=>{if(a.viewport&&i.current){let e=H(a.viewport.scrollLeft,s,a.dir);i.current.style.transform="translate3d(".concat(e,"px, 0, 0)")}},onWheelScroll:e=>{a.viewport&&(a.viewport.scrollLeft=e)},onDragScroll:e=>{a.viewport&&(a.viewport.scrollLeft=f(e,a.dir))}}):"vertical"===r?(0,p.jsx)(D,{...d,ref:t,onThumbPositionChange:()=>{if(a.viewport&&i.current){let e=H(a.viewport.scrollTop,s);i.current.style.transform="translate3d(0, ".concat(e,"px, 0)")}},onWheelScroll:e=>{a.viewport&&(a.viewport.scrollTop=e)},onDragScroll:e=>{a.viewport&&(a.viewport.scrollTop=f(e))}}):null}),T=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...a}=e,i=y(E,e.__scopeScrollArea),[s,u]=n.useState(),c=n.useRef(null),d=(0,l.s)(t,c,i.onScrollbarXChange);return n.useEffect(()=>{c.current&&u(getComputedStyle(c.current))},[c]),(0,p.jsx)(P,{"data-orientation":"horizontal",...a,ref:d,sizes:r,style:{bottom:0,left:"rtl"===i.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===i.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":z(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,r)=>{if(i.viewport){let n=i.viewport.scrollLeft+t.deltaX;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{c.current&&i.viewport&&s&&o({content:i.viewport.scrollWidth,viewport:i.viewport.offsetWidth,scrollbar:{size:c.current.clientWidth,paddingStart:V(s.paddingLeft),paddingEnd:V(s.paddingRight)}})}})}),D=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...a}=e,i=y(E,e.__scopeScrollArea),[s,u]=n.useState(),c=n.useRef(null),d=(0,l.s)(t,c,i.onScrollbarYChange);return n.useEffect(()=>{c.current&&u(getComputedStyle(c.current))},[c]),(0,p.jsx)(P,{"data-orientation":"vertical",...a,ref:d,sizes:r,style:{top:0,right:"ltr"===i.dir?0:void 0,left:"rtl"===i.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":z(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,r)=>{if(i.viewport){let n=i.viewport.scrollTop+t.deltaY;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{c.current&&i.viewport&&s&&o({content:i.viewport.scrollHeight,viewport:i.viewport.offsetHeight,scrollbar:{size:c.current.clientHeight,paddingStart:V(s.paddingTop),paddingEnd:V(s.paddingBottom)}})}})}),[j,M]=h(E),P=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,sizes:a,hasThumb:i,onThumbChange:u,onThumbPointerUp:c,onThumbPointerDown:d,onThumbPositionChange:m,onDragScroll:h,onWheelScroll:v,onResize:g,...b}=e,w=y(E,r),[x,k]=n.useState(null),C=(0,l.s)(t,e=>k(e)),S=n.useRef(null),R=n.useRef(""),A=w.viewport,T=a.content-a.viewport,D=(0,s.c)(v),M=(0,s.c)(m),P=G(g,10);function N(e){S.current&&h({x:e.clientX-S.current.left,y:e.clientY-S.current.top})}return n.useEffect(()=>{let e=e=>{let t=e.target;(null==x?void 0:x.contains(t))&&D(e,T)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[A,x,T,D]),n.useEffect(M,[a,M]),K(x,P),K(w.content,P),(0,p.jsx)(j,{scope:r,scrollbar:x,hasThumb:i,onThumbChange:(0,s.c)(u),onThumbPointerUp:(0,s.c)(c),onThumbPositionChange:M,onThumbPointerDown:(0,s.c)(d),children:(0,p.jsx)(o.sG.div,{...b,ref:C,style:{position:"absolute",...b.style},onPointerDown:(0,f.m)(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),S.current=x.getBoundingClientRect(),R.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",w.viewport&&(w.viewport.style.scrollBehavior="auto"),N(e))}),onPointerMove:(0,f.m)(e.onPointerMove,N),onPointerUp:(0,f.m)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=R.current,w.viewport&&(w.viewport.style.scrollBehavior=""),S.current=null})})})}),N="ScrollAreaThumb",L=n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,o=M(N,e.__scopeScrollArea);return(0,p.jsx)(a.C,{present:r||o.hasThumb,children:(0,p.jsx)(O,{ref:t,...n})})}),O=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,style:a,...i}=e,s=y(N,r),u=M(N,r),{onThumbPositionChange:c}=u,d=(0,l.s)(t,e=>u.onThumbChange(e)),m=n.useRef(void 0),h=G(()=>{m.current&&(m.current(),m.current=void 0)},100);return n.useEffect(()=>{let e=s.viewport;if(e){let t=()=>{h(),m.current||(m.current=U(e,c),c())};return c(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[s.viewport,h,c]),(0,p.jsx)(o.sG.div,{"data-state":u.hasThumb?"visible":"hidden",...i,ref:d,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...a},onPointerDownCapture:(0,f.m)(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),r=e.clientX-t.left,n=e.clientY-t.top;u.onThumbPointerDown({x:r,y:n})}),onPointerUp:(0,f.m)(e.onPointerUp,u.onThumbPointerUp)})});L.displayName=N;var _="ScrollAreaCorner",I=n.forwardRef((e,t)=>{let r=y(_,e.__scopeScrollArea),n=!!(r.scrollbarX&&r.scrollbarY);return"scroll"!==r.type&&n?(0,p.jsx)(F,{...e,ref:t}):null});I.displayName=_;var F=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,...a}=e,i=y(_,r),[l,s]=n.useState(0),[u,c]=n.useState(0),d=!!(l&&u);return K(i.scrollbarX,()=>{var e;let t=(null===(e=i.scrollbarX)||void 0===e?void 0:e.offsetHeight)||0;i.onCornerHeightChange(t),c(t)}),K(i.scrollbarY,()=>{var e;let t=(null===(e=i.scrollbarY)||void 0===e?void 0:e.offsetWidth)||0;i.onCornerWidthChange(t),s(t)}),d?(0,p.jsx)(o.sG.div,{...a,ref:t,style:{width:l,height:u,position:"absolute",right:"ltr"===i.dir?0:void 0,left:"rtl"===i.dir?0:void 0,bottom:0,...e.style}}):null});function V(e){return e?parseInt(e,10):0}function B(e,t){let r=e/t;return isNaN(r)?0:r}function z(e){let t=B(e.viewport,e.content),r=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-r)*t,18)}function H(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ltr",n=z(t),o=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,a=t.scrollbar.size-o,i=t.content-t.viewport,l=(0,d.q)(e,"ltr"===r?[0,i]:[-+i,0]);return W([0,i],[0,a-n])(l)}function W(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}var U=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:()=>{},r={left:e.scrollLeft,top:e.scrollTop},n=0;return!function o(){let a={left:e.scrollLeft,top:e.scrollTop},i=r.left!==a.left,l=r.top!==a.top;(i||l)&&t(),r=a,n=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(n)};function G(e,t){let r=(0,s.c)(e),o=n.useRef(0);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),n.useCallback(()=>{window.clearTimeout(o.current),o.current=window.setTimeout(r,t)},[r,t])}function K(e,t){let r=(0,s.c)(t);(0,c.N)(()=>{let t=0;if(e){let n=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(r)});return n.observe(e),()=>{window.cancelAnimationFrame(t),n.unobserve(e)}}},[e,r])}var q=b,Y=x,X=I},8126:(e,t,r)=>{r.d(t,{Eq:()=>c});var n=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,a=new WeakMap,i={},l=0,s=function(e){return e&&(e.host||s(e.parentNode))},u=function(e,t,r,n){var u=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var r=s(e);return r&&t.contains(r)?r:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});i[r]||(i[r]=new WeakMap);var c=i[r],d=[],f=new Set,p=new Set(u),m=function(e){!e||f.has(e)||(f.add(e),m(e.parentNode))};u.forEach(m);var h=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))h(e);else try{var t=e.getAttribute(n),i=null!==t&&"false"!==t,l=(o.get(e)||0)+1,s=(c.get(e)||0)+1;o.set(e,l),c.set(e,s),d.push(e),1===l&&i&&a.set(e,!0),1===s&&e.setAttribute(r,"true"),i||e.setAttribute(n,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return h(t),f.clear(),l++,function(){d.forEach(function(e){var t=o.get(e)-1,i=c.get(e)-1;o.set(e,t),c.set(e,i),t||(a.has(e)||e.removeAttribute(n),a.delete(e)),i||e.removeAttribute(r)}),--l||(o=new WeakMap,o=new WeakMap,a=new WeakMap,i={})}},c=function(e,t,r){void 0===r&&(r="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),a=t||n(e);return a?(o.push.apply(o,Array.from(a.querySelectorAll("[aria-live]"))),u(o,a,r,"aria-hidden")):function(){return null}}},8587:(e,t,r)=>{r.d(t,{II:()=>d,v_:()=>s,wm:()=>c});var n=r(7569),o=r(6920),a=r(6733),i=r(8691);function l(e){return Math.min(1e3*2**e,3e4)}function s(e){return(e??"online")!=="online"||o.t.isOnline()}var u=class extends Error{constructor(e){super("CancelledError"),this.revert=e?.revert,this.silent=e?.silent}};function c(e){return e instanceof u}function d(e){let t,r=!1,c=0,d=!1,f=(0,a.T)(),p=()=>n.m.isFocused()&&("always"===e.networkMode||o.t.isOnline())&&e.canRun(),m=()=>s(e.networkMode)&&e.canRun(),h=r=>{d||(d=!0,e.onSuccess?.(r),t?.(),f.resolve(r))},v=r=>{d||(d=!0,e.onError?.(r),t?.(),f.reject(r))},g=()=>new Promise(r=>{t=e=>{(d||p())&&r(e)},e.onPause?.()}).then(()=>{t=void 0,d||e.onContinue?.()}),y=()=>{let t;if(d)return;let n=0===c?e.initialPromise:void 0;try{t=n??e.fn()}catch(e){t=Promise.reject(e)}Promise.resolve(t).then(h).catch(t=>{if(d)return;let n=e.retry??3*!i.S$,o=e.retryDelay??l,a="function"==typeof o?o(c,t):o,s=!0===n||"number"==typeof n&&c<n||"function"==typeof n&&n(c,t);if(r||!s){v(t);return}c++,e.onFail?.(c,t),(0,i.yy)(a).then(()=>p()?void 0:g()).then(()=>{r?v(t):y()})})};return{promise:f,cancel:t=>{d||(v(new u(t)),e.abort?.())},continue:()=>(t?.(),f),cancelRetry:()=>{r=!0},continueRetry:()=>{r=!1},canStart:m,start:()=>(m()?y():g().then(y),f)}}},8691:(e,t,r)=>{r.d(t,{Cp:()=>m,EN:()=>p,Eh:()=>u,F$:()=>f,GU:()=>R,MK:()=>c,S$:()=>n,ZM:()=>S,ZZ:()=>k,Zw:()=>a,d2:()=>s,f8:()=>h,gn:()=>i,hT:()=>C,j3:()=>l,lQ:()=>o,nJ:()=>d,pl:()=>w,rX:()=>x,y9:()=>E,yy:()=>b});var n="undefined"==typeof window||"Deno"in globalThis;function o(){}function a(e,t){return"function"==typeof e?e(t):e}function i(e){return"number"==typeof e&&e>=0&&e!==1/0}function l(e,t){return Math.max(e+(t||0)-Date.now(),0)}function s(e,t){return"function"==typeof e?e(t):e}function u(e,t){return"function"==typeof e?e(t):e}function c(e,t){let{type:r="all",exact:n,fetchStatus:o,predicate:a,queryKey:i,stale:l}=e;if(i){if(n){if(t.queryHash!==f(i,t.options))return!1}else if(!m(t.queryKey,i))return!1}if("all"!==r){let e=t.isActive();if("active"===r&&!e||"inactive"===r&&e)return!1}return("boolean"!=typeof l||t.isStale()===l)&&(!o||o===t.state.fetchStatus)&&(!a||!!a(t))}function d(e,t){let{exact:r,status:n,predicate:o,mutationKey:a}=e;if(a){if(!t.options.mutationKey)return!1;if(r){if(p(t.options.mutationKey)!==p(a))return!1}else if(!m(t.options.mutationKey,a))return!1}return(!n||t.state.status===n)&&(!o||!!o(t))}function f(e,t){return(t?.queryKeyHashFn||p)(e)}function p(e){return JSON.stringify(e,(e,t)=>g(t)?Object.keys(t).sort().reduce((e,r)=>(e[r]=t[r],e),{}):t)}function m(e,t){return e===t||typeof e==typeof t&&!!e&&!!t&&"object"==typeof e&&"object"==typeof t&&Object.keys(t).every(r=>m(e[r],t[r]))}function h(e,t){if(!t||Object.keys(e).length!==Object.keys(t).length)return!1;for(let r in e)if(e[r]!==t[r])return!1;return!0}function v(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function g(e){if(!y(e))return!1;let t=e.constructor;if(void 0===t)return!0;let r=t.prototype;return!!(y(r)&&r.hasOwnProperty("isPrototypeOf"))&&Object.getPrototypeOf(e)===Object.prototype}function y(e){return"[object Object]"===Object.prototype.toString.call(e)}function b(e){return new Promise(t=>{setTimeout(t,e)})}function w(e,t,r){return"function"==typeof r.structuralSharing?r.structuralSharing(e,t):!1!==r.structuralSharing?function e(t,r){if(t===r)return t;let n=v(t)&&v(r);if(n||g(t)&&g(r)){let o=n?t:Object.keys(t),a=o.length,i=n?r:Object.keys(r),l=i.length,s=n?[]:{},u=0;for(let a=0;a<l;a++){let l=n?a:i[a];(!n&&o.includes(l)||n)&&void 0===t[l]&&void 0===r[l]?(s[l]=void 0,u++):(s[l]=e(t[l],r[l]),s[l]===t[l]&&void 0!==t[l]&&u++)}return a===l&&u===a?t:s}return r}(e,t):t}function x(e){return e}function E(e,t,r=0){let n=[...e,t];return r&&n.length>r?n.slice(1):n}function k(e,t,r=0){let n=[t,...e];return r&&n.length>r?n.slice(0,-1):n}var C=Symbol();function S(e,t){return!e.queryFn&&t?.initialPromise?()=>t.initialPromise:e.queryFn&&e.queryFn!==C?e.queryFn:()=>Promise.reject(Error(`Missing queryFn: '${e.queryHash}'`))}function R(e,t){return"function"==typeof e?e(...t):!!e}},8910:(e,t,r)=>{r.d(t,{q:()=>n});function n(e,[t,r]){return Math.min(r,Math.max(t,e))}},8939:(e,t,r)=>{r.d(t,{k:()=>o});var n=r(8691),o=class{#o;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),(0,n.gn)(this.gcTime)&&(this.#o=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(n.S$?1/0:3e5))}clearGcTimeout(){this.#o&&(clearTimeout(this.#o),this.#o=void 0)}}},9379:(e,t,r)=>{r.d(t,{CC:()=>W,Q6:()=>U,bL:()=>H,zi:()=>G});var n=r(4545),o=r(8910),a=r(4157),i=r(4489),l=r(563),s=r(3028),u=r(6084),c=r(56),d=r(2080),f=r(6856),p=r(2448),m=r(7093),h=["PageUp","PageDown"],v=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],g={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},y="Slider",[b,w,x]=(0,p.N)(y),[E,k]=(0,l.A)(y,[x]),[C,S]=E(y),R=n.forwardRef((e,t)=>{let{name:r,min:i=0,max:l=100,step:u=1,orientation:c="horizontal",disabled:d=!1,minStepsBetweenThumbs:f=0,defaultValue:p=[i],value:g,onValueChange:y=()=>{},onValueCommit:w=()=>{},inverted:x=!1,form:E,...k}=e,S=n.useRef(new Set),R=n.useRef(0),A="horizontal"===c,[T=[],M]=(0,s.i)({prop:g,defaultProp:p,onChange:e=>{var t;null===(t=[...S.current][R.current])||void 0===t||t.focus(),y(e)}}),P=n.useRef(T);function N(e,t){let{commit:r}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{commit:!1};let n=(String(u).split(".")[1]||"").length,a=function(e,t){let r=Math.pow(10,t);return Math.round(e*r)/r}(Math.round((e-i)/u)*u+i,n),s=(0,o.q)(a,[i,l]);M(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0,r=arguments.length>2?arguments[2]:void 0,n=[...e];return n[r]=t,n.sort((e,t)=>e-t)}(e,s,t);if(!function(e,t){if(t>0)return Math.min(...e.slice(0,-1).map((t,r)=>e[r+1]-t))>=t;return!0}(n,f*u))return e;{R.current=n.indexOf(s);let t=String(n)!==String(e);return t&&r&&w(n),t?n:e}})}return(0,m.jsx)(C,{scope:e.__scopeSlider,name:r,disabled:d,min:i,max:l,valueIndexToChangeRef:R,thumbs:S.current,values:T,orientation:c,form:E,children:(0,m.jsx)(b.Provider,{scope:e.__scopeSlider,children:(0,m.jsx)(b.Slot,{scope:e.__scopeSlider,children:(0,m.jsx)(A?D:j,{"aria-disabled":d,"data-disabled":d?"":void 0,...k,ref:t,onPointerDown:(0,a.m)(k.onPointerDown,()=>{d||(P.current=T)}),min:i,max:l,inverted:x,onSlideStart:d?void 0:function(e){let t=function(e,t){if(1===e.length)return 0;let r=e.map(e=>Math.abs(e-t)),n=Math.min(...r);return r.indexOf(n)}(T,e);N(e,t)},onSlideMove:d?void 0:function(e){N(e,R.current)},onSlideEnd:d?void 0:function(){let e=P.current[R.current];T[R.current]!==e&&w(T)},onHomeKeyDown:()=>!d&&N(i,0,{commit:!0}),onEndKeyDown:()=>!d&&N(l,T.length-1,{commit:!0}),onStepKeyDown:e=>{let{event:t,direction:r}=e;if(!d){let e=h.includes(t.key)||t.shiftKey&&v.includes(t.key),n=R.current;N(T[n]+u*(e?10:1)*r,n,{commit:!0})}}})})})})});R.displayName=y;var[A,T]=E(y,{startEdge:"left",endEdge:"right",size:"width",direction:1}),D=n.forwardRef((e,t)=>{let{min:r,max:o,dir:a,inverted:l,onSlideStart:s,onSlideMove:c,onSlideEnd:d,onStepKeyDown:f,...p}=e,[h,v]=n.useState(null),y=(0,i.s)(t,e=>v(e)),b=n.useRef(void 0),w=(0,u.jH)(a),x="ltr"===w,E=x&&!l||!x&&l;function k(e){let t=b.current||h.getBoundingClientRect(),n=z([0,t.width],E?[r,o]:[o,r]);return b.current=t,n(e-t.left)}return(0,m.jsx)(A,{scope:e.__scopeSlider,startEdge:E?"left":"right",endEdge:E?"right":"left",direction:E?1:-1,size:"width",children:(0,m.jsx)(M,{dir:w,"data-orientation":"horizontal",...p,ref:y,style:{...p.style,"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:e=>{let t=k(e.clientX);null==s||s(t)},onSlideMove:e=>{let t=k(e.clientX);null==c||c(t)},onSlideEnd:()=>{b.current=void 0,null==d||d()},onStepKeyDown:e=>{let t=g[E?"from-left":"from-right"].includes(e.key);null==f||f({event:e,direction:t?-1:1})}})})}),j=n.forwardRef((e,t)=>{let{min:r,max:o,inverted:a,onSlideStart:l,onSlideMove:s,onSlideEnd:u,onStepKeyDown:c,...d}=e,f=n.useRef(null),p=(0,i.s)(t,f),h=n.useRef(void 0),v=!a;function y(e){let t=h.current||f.current.getBoundingClientRect(),n=z([0,t.height],v?[o,r]:[r,o]);return h.current=t,n(e-t.top)}return(0,m.jsx)(A,{scope:e.__scopeSlider,startEdge:v?"bottom":"top",endEdge:v?"top":"bottom",size:"height",direction:v?1:-1,children:(0,m.jsx)(M,{"data-orientation":"vertical",...d,ref:p,style:{...d.style,"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:e=>{let t=y(e.clientY);null==l||l(t)},onSlideMove:e=>{let t=y(e.clientY);null==s||s(t)},onSlideEnd:()=>{h.current=void 0,null==u||u()},onStepKeyDown:e=>{let t=g[v?"from-bottom":"from-top"].includes(e.key);null==c||c({event:e,direction:t?-1:1})}})})}),M=n.forwardRef((e,t)=>{let{__scopeSlider:r,onSlideStart:n,onSlideMove:o,onSlideEnd:i,onHomeKeyDown:l,onEndKeyDown:s,onStepKeyDown:u,...c}=e,d=S(y,r);return(0,m.jsx)(f.sG.span,{...c,ref:t,onKeyDown:(0,a.m)(e.onKeyDown,e=>{"Home"===e.key?(l(e),e.preventDefault()):"End"===e.key?(s(e),e.preventDefault()):h.concat(v).includes(e.key)&&(u(e),e.preventDefault())}),onPointerDown:(0,a.m)(e.onPointerDown,e=>{let t=e.target;t.setPointerCapture(e.pointerId),e.preventDefault(),d.thumbs.has(t)?t.focus():n(e)}),onPointerMove:(0,a.m)(e.onPointerMove,e=>{e.target.hasPointerCapture(e.pointerId)&&o(e)}),onPointerUp:(0,a.m)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&(t.releasePointerCapture(e.pointerId),i(e))})})}),P="SliderTrack",N=n.forwardRef((e,t)=>{let{__scopeSlider:r,...n}=e,o=S(P,r);return(0,m.jsx)(f.sG.span,{"data-disabled":o.disabled?"":void 0,"data-orientation":o.orientation,...n,ref:t})});N.displayName=P;var L="SliderRange",O=n.forwardRef((e,t)=>{let{__scopeSlider:r,...o}=e,a=S(L,r),l=T(L,r),s=n.useRef(null),u=(0,i.s)(t,s),c=a.values.length,d=a.values.map(e=>B(e,a.min,a.max)),p=c>1?Math.min(...d):0,h=100-Math.max(...d);return(0,m.jsx)(f.sG.span,{"data-orientation":a.orientation,"data-disabled":a.disabled?"":void 0,...o,ref:u,style:{...e.style,[l.startEdge]:p+"%",[l.endEdge]:h+"%"}})});O.displayName=L;var _="SliderThumb",I=n.forwardRef((e,t)=>{let r=w(e.__scopeSlider),[o,a]=n.useState(null),l=(0,i.s)(t,e=>a(e)),s=n.useMemo(()=>o?r().findIndex(e=>e.ref.current===o):-1,[r,o]);return(0,m.jsx)(F,{...e,ref:l,index:s})}),F=n.forwardRef((e,t)=>{let{__scopeSlider:r,index:o,name:l,...s}=e,u=S(_,r),c=T(_,r),[p,h]=n.useState(null),v=(0,i.s)(t,e=>h(e)),g=!p||u.form||!!p.closest("form"),y=(0,d.X)(p),w=u.values[o],x=void 0===w?0:B(w,u.min,u.max),E=function(e,t){return t>2?"Value ".concat(e+1," of ").concat(t):2===t?["Minimum","Maximum"][e]:void 0}(o,u.values.length),k=null==y?void 0:y[c.size],C=k?function(e,t,r){let n=e/2,o=z([0,50],[0,n]);return(n-o(t)*r)*r}(k,x,c.direction):0;return n.useEffect(()=>{if(p)return u.thumbs.add(p),()=>{u.thumbs.delete(p)}},[p,u.thumbs]),(0,m.jsxs)("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[c.startEdge]:"calc(".concat(x,"% + ").concat(C,"px)")},children:[(0,m.jsx)(b.ItemSlot,{scope:e.__scopeSlider,children:(0,m.jsx)(f.sG.span,{role:"slider","aria-label":e["aria-label"]||E,"aria-valuemin":u.min,"aria-valuenow":w,"aria-valuemax":u.max,"aria-orientation":u.orientation,"data-orientation":u.orientation,"data-disabled":u.disabled?"":void 0,tabIndex:u.disabled?void 0:0,...s,ref:v,style:void 0===w?{display:"none"}:e.style,onFocus:(0,a.m)(e.onFocus,()=>{u.valueIndexToChangeRef.current=o})})}),g&&(0,m.jsx)(V,{name:null!=l?l:u.name?u.name+(u.values.length>1?"[]":""):void 0,form:u.form,value:w},o)]})});I.displayName=_;var V=n.forwardRef((e,t)=>{let{__scopeSlider:r,value:o,...a}=e,l=n.useRef(null),s=(0,i.s)(l,t),u=(0,c.Z)(o);return n.useEffect(()=>{let e=l.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"value").set;if(u!==o&&t){let r=new Event("input",{bubbles:!0});t.call(e,o),e.dispatchEvent(r)}},[u,o]),(0,m.jsx)(f.sG.input,{style:{display:"none"},...a,ref:s,defaultValue:o})});function B(e,t,r){return(0,o.q)(100/(r-t)*(e-t),[0,100])}function z(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}V.displayName="RadioBubbleInput";var H=R,W=N,U=O,G=I},9410:(e,t,r)=>{r.d(t,{uB:()=>P});var n=/[\\\/_+.#"@\[\(\{&]/,o=/[\\\/_+.#"@\[\(\{&]/g,a=/[\s-]/,i=/[\s-]/g;function l(e){return e.toLowerCase().replace(i," ")}var s=r(5471),u=r(4545),c=r(6856),d=r(3328),f=r(4489),p='[cmdk-group=""]',m='[cmdk-group-items=""]',h='[cmdk-item=""]',v="".concat(h,':not([aria-disabled="true"])'),g="cmdk-item-select",y="data-value",b=(e,t,r)=>(function(e,t,r){return function e(t,r,l,s,u,c,d){if(c===r.length)return u===t.length?1:.99;var f=`${u},${c}`;if(void 0!==d[f])return d[f];for(var p,m,h,v,g=s.charAt(c),y=l.indexOf(g,u),b=0;y>=0;)(p=e(t,r,l,s,y+1,c+1,d))>b&&(y===u?p*=1:n.test(t.charAt(y-1))?(p*=.8,(h=t.slice(u,y-1).match(o))&&u>0&&(p*=Math.pow(.999,h.length))):a.test(t.charAt(y-1))?(p*=.9,(v=t.slice(u,y-1).match(i))&&u>0&&(p*=Math.pow(.999,v.length))):(p*=.17,u>0&&(p*=Math.pow(.999,y-u))),t.charAt(y)!==r.charAt(c)&&(p*=.9999)),(p<.1&&l.charAt(y-1)===s.charAt(c+1)||s.charAt(c+1)===s.charAt(c)&&l.charAt(y-1)!==s.charAt(c))&&.1*(m=e(t,r,l,s,y+1,c+2,d))>p&&(p=.1*m),p>b&&(b=p),y=l.indexOf(g,y+1);return d[f]=b,b}(e=r&&r.length>0?`${e+" "+r.join(" ")}`:e,t,l(e),l(t),0,0,{})})(e,t,r),w=u.createContext(void 0),x=()=>u.useContext(w),E=u.createContext(void 0),k=()=>u.useContext(E),C=u.createContext(void 0),S=u.forwardRef((e,t)=>{let r=O(()=>{var t,r;return{search:"",value:null!=(r=null!=(t=e.value)?t:e.defaultValue)?r:"",selectedItemId:void 0,filtered:{count:0,items:new Map,groups:new Set}}}),n=O(()=>new Set),o=O(()=>new Map),a=O(()=>new Map),i=O(()=>new Set),l=N(e),{label:s,children:f,value:x,onValueChange:k,filter:C,shouldFilter:S,loop:R,disablePointerSelection:A=!1,vimBindings:T=!0,...D}=e,j=(0,d.B)(),M=(0,d.B)(),P=(0,d.B)(),_=u.useRef(null),I=F();L(()=>{if(void 0!==x){let e=x.trim();r.current.value=e,z.emit()}},[x]),L(()=>{I(6,q)},[]);let z=u.useMemo(()=>({subscribe:e=>(i.current.add(e),()=>i.current.delete(e)),snapshot:()=>r.current,setState:(e,t,n)=>{var o,a,i,s;if(!Object.is(r.current[e],t)){if(r.current[e]=t,"search"===e)K(),U(),I(1,G);else if("value"===e){if(document.activeElement.hasAttribute("cmdk-input")||document.activeElement.hasAttribute("cmdk-root")){let e=document.getElementById(P);e?e.focus():null==(o=document.getElementById(j))||o.focus()}if(I(7,()=>{var e;r.current.selectedItemId=null==(e=Y())?void 0:e.id,z.emit()}),n||I(5,q),(null==(a=l.current)?void 0:a.value)!==void 0){null==(s=(i=l.current).onValueChange)||s.call(i,null!=t?t:"");return}}z.emit()}},emit:()=>{i.current.forEach(e=>e())}}),[]),H=u.useMemo(()=>({value:(e,t,n)=>{var o;t!==(null==(o=a.current.get(e))?void 0:o.value)&&(a.current.set(e,{value:t,keywords:n}),r.current.filtered.items.set(e,W(t,n)),I(2,()=>{U(),z.emit()}))},item:(e,t)=>(n.current.add(e),t&&(o.current.has(t)?o.current.get(t).add(e):o.current.set(t,new Set([e]))),I(3,()=>{K(),U(),r.current.value||G(),z.emit()}),()=>{a.current.delete(e),n.current.delete(e),r.current.filtered.items.delete(e);let t=Y();I(4,()=>{K(),(null==t?void 0:t.getAttribute("id"))===e&&G(),z.emit()})}),group:e=>(o.current.has(e)||o.current.set(e,new Set),()=>{a.current.delete(e),o.current.delete(e)}),filter:()=>l.current.shouldFilter,label:s||e["aria-label"],getDisablePointerSelection:()=>l.current.disablePointerSelection,listId:j,inputId:P,labelId:M,listInnerRef:_}),[]);function W(e,t){var n,o;let a=null!=(o=null==(n=l.current)?void 0:n.filter)?o:b;return e?a(e,r.current.search,t):0}function U(){if(!r.current.search||!1===l.current.shouldFilter)return;let e=r.current.filtered.items,t=[];r.current.filtered.groups.forEach(r=>{let n=o.current.get(r),a=0;n.forEach(t=>{a=Math.max(e.get(t),a)}),t.push([r,a])});let n=_.current;X().sort((t,r)=>{var n,o;let a=t.getAttribute("id"),i=r.getAttribute("id");return(null!=(n=e.get(i))?n:0)-(null!=(o=e.get(a))?o:0)}).forEach(e=>{let t=e.closest(m);t?t.appendChild(e.parentElement===t?e:e.closest("".concat(m," > *"))):n.appendChild(e.parentElement===n?e:e.closest("".concat(m," > *")))}),t.sort((e,t)=>t[1]-e[1]).forEach(e=>{var t;let r=null==(t=_.current)?void 0:t.querySelector("".concat(p,"[").concat(y,'="').concat(encodeURIComponent(e[0]),'"]'));null==r||r.parentElement.appendChild(r)})}function G(){let e=X().find(e=>"true"!==e.getAttribute("aria-disabled")),t=null==e?void 0:e.getAttribute(y);z.setState("value",t||void 0)}function K(){var e,t,i,s;if(!r.current.search||!1===l.current.shouldFilter){r.current.filtered.count=n.current.size;return}r.current.filtered.groups=new Set;let u=0;for(let o of n.current){let n=W(null!=(t=null==(e=a.current.get(o))?void 0:e.value)?t:"",null!=(s=null==(i=a.current.get(o))?void 0:i.keywords)?s:[]);r.current.filtered.items.set(o,n),n>0&&u++}for(let[e,t]of o.current)for(let n of t)if(r.current.filtered.items.get(n)>0){r.current.filtered.groups.add(e);break}r.current.filtered.count=u}function q(){var e,t,r;let n=Y();n&&((null==(e=n.parentElement)?void 0:e.firstChild)===n&&(null==(r=null==(t=n.closest(p))?void 0:t.querySelector('[cmdk-group-heading=""]'))||r.scrollIntoView({block:"nearest"})),n.scrollIntoView({block:"nearest"}))}function Y(){var e;return null==(e=_.current)?void 0:e.querySelector("".concat(h,'[aria-selected="true"]'))}function X(){var e;return Array.from((null==(e=_.current)?void 0:e.querySelectorAll(v))||[])}function $(e){let t=X()[e];t&&z.setState("value",t.getAttribute(y))}function Z(e){var t;let r=Y(),n=X(),o=n.findIndex(e=>e===r),a=n[o+e];null!=(t=l.current)&&t.loop&&(a=o+e<0?n[n.length-1]:o+e===n.length?n[0]:n[o+e]),a&&z.setState("value",a.getAttribute(y))}function J(e){let t=Y(),r=null==t?void 0:t.closest(p),n;for(;r&&!n;)n=null==(r=e>0?function(e,t){let r=e.nextElementSibling;for(;r;){if(r.matches(t))return r;r=r.nextElementSibling}}(r,p):function(e,t){let r=e.previousElementSibling;for(;r;){if(r.matches(t))return r;r=r.previousElementSibling}}(r,p))?void 0:r.querySelector(v);n?z.setState("value",n.getAttribute(y)):Z(e)}let Q=()=>$(X().length-1),ee=e=>{e.preventDefault(),e.metaKey?Q():e.altKey?J(1):Z(1)},et=e=>{e.preventDefault(),e.metaKey?$(0):e.altKey?J(-1):Z(-1)};return u.createElement(c.sG.div,{ref:t,tabIndex:-1,...D,"cmdk-root":"",onKeyDown:e=>{var t;null==(t=D.onKeyDown)||t.call(D,e);let r=e.nativeEvent.isComposing||229===e.keyCode;if(!(e.defaultPrevented||r))switch(e.key){case"n":case"j":T&&e.ctrlKey&&ee(e);break;case"ArrowDown":ee(e);break;case"p":case"k":T&&e.ctrlKey&&et(e);break;case"ArrowUp":et(e);break;case"Home":e.preventDefault(),$(0);break;case"End":e.preventDefault(),Q();break;case"Enter":{e.preventDefault();let t=Y();if(t){let e=new Event(g);t.dispatchEvent(e)}}}}},u.createElement("label",{"cmdk-label":"",htmlFor:H.inputId,id:H.labelId,style:B},s),V(e,e=>u.createElement(E.Provider,{value:z},u.createElement(w.Provider,{value:H},e))))}),R=u.forwardRef((e,t)=>{var r,n;let o=(0,d.B)(),a=u.useRef(null),i=u.useContext(C),l=x(),s=N(e),p=null!=(n=null==(r=s.current)?void 0:r.forceMount)?n:null==i?void 0:i.forceMount;L(()=>{if(!p)return l.item(o,null==i?void 0:i.id)},[p]);let m=I(o,a,[e.value,e.children,a],e.keywords),h=k(),v=_(e=>e.value&&e.value===m.current),y=_(e=>!!p||!1===l.filter()||!e.search||e.filtered.items.get(o)>0);function b(){var e,t;w(),null==(t=(e=s.current).onSelect)||t.call(e,m.current)}function w(){h.setState("value",m.current,!0)}if(u.useEffect(()=>{let t=a.current;if(!(!t||e.disabled))return t.addEventListener(g,b),()=>t.removeEventListener(g,b)},[y,e.onSelect,e.disabled]),!y)return null;let{disabled:E,value:S,onSelect:R,forceMount:A,keywords:T,...D}=e;return u.createElement(c.sG.div,{ref:(0,f.t)(a,t),...D,id:o,"cmdk-item":"",role:"option","aria-disabled":!!E,"aria-selected":!!v,"data-disabled":!!E,"data-selected":!!v,onPointerMove:E||l.getDisablePointerSelection()?void 0:w,onClick:E?void 0:b},e.children)}),A=u.forwardRef((e,t)=>{let{heading:r,children:n,forceMount:o,...a}=e,i=(0,d.B)(),l=u.useRef(null),s=u.useRef(null),p=(0,d.B)(),m=x(),h=_(e=>!!o||!1===m.filter()||!e.search||e.filtered.groups.has(i));L(()=>m.group(i),[]),I(i,l,[e.value,e.heading,s]);let v=u.useMemo(()=>({id:i,forceMount:o}),[o]);return u.createElement(c.sG.div,{ref:(0,f.t)(l,t),...a,"cmdk-group":"",role:"presentation",hidden:!h||void 0},r&&u.createElement("div",{ref:s,"cmdk-group-heading":"","aria-hidden":!0,id:p},r),V(e,e=>u.createElement("div",{"cmdk-group-items":"",role:"group","aria-labelledby":r?p:void 0},u.createElement(C.Provider,{value:v},e))))}),T=u.forwardRef((e,t)=>{let{alwaysRender:r,...n}=e,o=u.useRef(null),a=_(e=>!e.search);return r||a?u.createElement(c.sG.div,{ref:(0,f.t)(o,t),...n,"cmdk-separator":"",role:"separator"}):null}),D=u.forwardRef((e,t)=>{let{onValueChange:r,...n}=e,o=null!=e.value,a=k(),i=_(e=>e.search),l=_(e=>e.selectedItemId),s=x();return u.useEffect(()=>{null!=e.value&&a.setState("search",e.value)},[e.value]),u.createElement(c.sG.input,{ref:t,...n,"cmdk-input":"",autoComplete:"off",autoCorrect:"off",spellCheck:!1,"aria-autocomplete":"list",role:"combobox","aria-expanded":!0,"aria-controls":s.listId,"aria-labelledby":s.labelId,"aria-activedescendant":l,id:s.inputId,type:"text",value:o?e.value:i,onChange:e=>{o||a.setState("search",e.target.value),null==r||r(e.target.value)}})}),j=u.forwardRef((e,t)=>{let{children:r,label:n="Suggestions",...o}=e,a=u.useRef(null),i=u.useRef(null),l=_(e=>e.selectedItemId),s=x();return u.useEffect(()=>{if(i.current&&a.current){let e=i.current,t=a.current,r,n=new ResizeObserver(()=>{r=requestAnimationFrame(()=>{let r=e.offsetHeight;t.style.setProperty("--cmdk-list-height",r.toFixed(1)+"px")})});return n.observe(e),()=>{cancelAnimationFrame(r),n.unobserve(e)}}},[]),u.createElement(c.sG.div,{ref:(0,f.t)(a,t),...o,"cmdk-list":"",role:"listbox",tabIndex:-1,"aria-activedescendant":l,"aria-label":n,id:s.listId},V(e,e=>u.createElement("div",{ref:(0,f.t)(i,s.listInnerRef),"cmdk-list-sizer":""},e)))}),M=u.forwardRef((e,t)=>{let{open:r,onOpenChange:n,overlayClassName:o,contentClassName:a,container:i,...l}=e;return u.createElement(s.bL,{open:r,onOpenChange:n},u.createElement(s.ZL,{container:i},u.createElement(s.hJ,{"cmdk-overlay":"",className:o}),u.createElement(s.UC,{"aria-label":e.label,"cmdk-dialog":"",className:a},u.createElement(S,{ref:t,...l}))))}),P=Object.assign(S,{List:j,Item:R,Input:D,Group:A,Separator:T,Dialog:M,Empty:u.forwardRef((e,t)=>_(e=>0===e.filtered.count)?u.createElement(c.sG.div,{ref:t,...e,"cmdk-empty":"",role:"presentation"}):null),Loading:u.forwardRef((e,t)=>{let{progress:r,children:n,label:o="Loading...",...a}=e;return u.createElement(c.sG.div,{ref:t,...a,"cmdk-loading":"",role:"progressbar","aria-valuenow":r,"aria-valuemin":0,"aria-valuemax":100,"aria-label":o},V(e,e=>u.createElement("div",{"aria-hidden":!0},e)))})});function N(e){let t=u.useRef(e);return L(()=>{t.current=e}),t}var L="undefined"==typeof window?u.useEffect:u.useLayoutEffect;function O(e){let t=u.useRef();return void 0===t.current&&(t.current=e()),t}function _(e){let t=k(),r=()=>e(t.snapshot());return u.useSyncExternalStore(t.subscribe,r,r)}function I(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[],o=u.useRef(),a=x();return L(()=>{var i;let l=(()=>{var e;for(let t of r){if("string"==typeof t)return t.trim();if("object"==typeof t&&"current"in t)return t.current?null==(e=t.current.textContent)?void 0:e.trim():o.current}})(),s=n.map(e=>e.trim());a.value(e,l,s),null==(i=t.current)||i.setAttribute(y,l),o.current=l}),o}var F=()=>{let[e,t]=u.useState(),r=O(()=>new Map);return L(()=>{r.current.forEach(e=>e()),r.current=new Map},[e]),(e,n)=>{r.current.set(e,n),t({})}};function V(e,t){let r,{asChild:n,children:o}=e;return n&&u.isValidElement(o)?u.cloneElement("function"==typeof(r=o.type)?r(o.props):"render"in r?r.render(o.props):o,{ref:o.ref},t(o.props.children)):t(o)}var B={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"}},9730:(e,t,r)=>{r.d(t,{_:()=>n});function n(e,t,r){if(!t.has(e))throw TypeError("attempted to "+r+" private field on non-instance");return t.get(e)}},9803:(e,t,r)=>{r.d(t,{A:()=>G});var n,o=function(){return(o=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function a(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}Object.create;Object.create;var i=("function"==typeof SuppressedError&&SuppressedError,r(4545)),l="right-scroll-bar-position",s="width-before-scroll-bar";function u(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var c="undefined"!=typeof window?i.useLayoutEffect:i.useEffect,d=new WeakMap;function f(e){return e}var p=function(e){void 0===e&&(e={});var t,r,n,a,i=(t=null,void 0===r&&(r=f),n=[],a=!1,{read:function(){if(a)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var t=r(e,a);return n.push(t),function(){n=n.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(a=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){a=!0;var t=[];if(n.length){var r=n;n=[],r.forEach(e),t=n}var o=function(){var r=t;t=[],r.forEach(e)},i=function(){return Promise.resolve().then(o)};i(),n={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),n}}}});return i.options=o({async:!0,ssr:!1},e),i}(),m=function(){},h=i.forwardRef(function(e,t){var r,n,l,s,f=i.useRef(null),h=i.useState({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:m}),v=h[0],g=h[1],y=e.forwardProps,b=e.children,w=e.className,x=e.removeScrollBar,E=e.enabled,k=e.shards,C=e.sideCar,S=e.noIsolation,R=e.inert,A=e.allowPinchZoom,T=e.as,D=e.gapMode,j=a(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),M=(r=[f,t],n=function(e){return r.forEach(function(t){return u(t,e)})},(l=(0,i.useState)(function(){return{value:null,callback:n,facade:{get current(){return l.value},set current(value){var e=l.value;e!==value&&(l.value=value,l.callback(value,e))}}}})[0]).callback=n,s=l.facade,c(function(){var e=d.get(s);if(e){var t=new Set(e),n=new Set(r),o=s.current;t.forEach(function(e){n.has(e)||u(e,null)}),n.forEach(function(e){t.has(e)||u(e,o)})}d.set(s,r)},[r]),s),P=o(o({},j),v);return i.createElement(i.Fragment,null,E&&i.createElement(C,{sideCar:p,removeScrollBar:x,shards:k,noIsolation:S,inert:R,setCallbacks:g,allowPinchZoom:!!A,lockRef:f,gapMode:D}),y?i.cloneElement(i.Children.only(b),o(o({},P),{ref:M})):i.createElement(void 0===T?"div":T,o({},P,{className:w,ref:M}),b))});h.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},h.classNames={fullWidth:s,zeroRight:l};var v=function(e){var t=e.sideCar,r=a(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var n=t.read();if(!n)throw Error("Sidecar medium not found");return i.createElement(n,o({},r))};v.isSideCarExport=!0;var g=function(){var e=0,t=null;return{add:function(o){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=n||r.nc;return t&&e.setAttribute("nonce",t),e}())){var a,i;(a=t).styleSheet?a.styleSheet.cssText=o:a.appendChild(document.createTextNode(o)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},y=function(){var e=g();return function(t,r){i.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&r])}},b=function(){var e=y();return function(t){return e(t.styles,t.dynamic),null}},w={left:0,top:0,right:0,gap:0},x=function(e){return parseInt(e||"",10)||0},E=function(e){var t=window.getComputedStyle(document.body),r=t["padding"===e?"paddingLeft":"marginLeft"],n=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[x(r),x(n),x(o)]},k=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return w;var t=E(e),r=document.documentElement.clientWidth,n=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,n-r+t[2]-t[0])}},C=b(),S="data-scroll-locked",R=function(e,t,r,n){var o=e.left,a=e.top,i=e.right,u=e.gap;return void 0===r&&(r="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(n,";\n   padding-right: ").concat(u,"px ").concat(n,";\n  }\n  body[").concat(S,"] {\n    overflow: hidden ").concat(n,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(n,";"),"margin"===r&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(u,"px ").concat(n,";\n    "),"padding"===r&&"padding-right: ".concat(u,"px ").concat(n,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(l," {\n    right: ").concat(u,"px ").concat(n,";\n  }\n  \n  .").concat(s," {\n    margin-right: ").concat(u,"px ").concat(n,";\n  }\n  \n  .").concat(l," .").concat(l," {\n    right: 0 ").concat(n,";\n  }\n  \n  .").concat(s," .").concat(s," {\n    margin-right: 0 ").concat(n,";\n  }\n  \n  body[").concat(S,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(u,"px;\n  }\n")},A=function(){var e=parseInt(document.body.getAttribute(S)||"0",10);return isFinite(e)?e:0},T=function(){i.useEffect(function(){return document.body.setAttribute(S,(A()+1).toString()),function(){var e=A()-1;e<=0?document.body.removeAttribute(S):document.body.setAttribute(S,e.toString())}},[])},D=function(e){var t=e.noRelative,r=e.noImportant,n=e.gapMode,o=void 0===n?"margin":n;T();var a=i.useMemo(function(){return k(o)},[o]);return i.createElement(C,{styles:R(a,!t,o,r?"":"!important")})},j=!1;if("undefined"!=typeof window)try{var M=Object.defineProperty({},"passive",{get:function(){return j=!0,!0}});window.addEventListener("test",M,M),window.removeEventListener("test",M,M)}catch(e){j=!1}var P=!!j&&{passive:!1},N=function(e,t){if(!(e instanceof Element))return!1;var r=window.getComputedStyle(e);return"hidden"!==r[t]&&(r.overflowY!==r.overflowX||"TEXTAREA"===e.tagName||"visible"!==r[t])},L=function(e,t){var r=t.ownerDocument,n=t;do{if("undefined"!=typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),O(e,n)){var o=_(e,n);if(o[1]>o[2])return!0}n=n.parentNode}while(n&&n!==r.body);return!1},O=function(e,t){return"v"===e?N(t,"overflowY"):N(t,"overflowX")},_=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},I=function(e,t,r,n,o){var a,i=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),l=i*n,s=r.target,u=t.contains(s),c=!1,d=l>0,f=0,p=0;do{var m=_(e,s),h=m[0],v=m[1]-m[2]-i*h;(h||v)&&O(e,s)&&(f+=v,p+=h),s=s instanceof ShadowRoot?s.host:s.parentNode}while(!u&&s!==document.body||u&&(t.contains(s)||t===s));return d&&(o&&1>Math.abs(f)||!o&&l>f)?c=!0:!d&&(o&&1>Math.abs(p)||!o&&-l>p)&&(c=!0),c},F=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},V=function(e){return[e.deltaX,e.deltaY]},B=function(e){return e&&"current"in e?e.current:e},z=0,H=[];let W=(p.useMedium(function(e){var t=i.useRef([]),r=i.useRef([0,0]),n=i.useRef(),o=i.useState(z++)[0],a=i.useState(b)[0],l=i.useRef(e);i.useEffect(function(){l.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,r){if(r||2==arguments.length)for(var n,o=0,a=t.length;o<a;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(B),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var s=i.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!l.current.allowPinchZoom;var o,a=F(e),i=r.current,s="deltaX"in e?e.deltaX:i[0]-a[0],u="deltaY"in e?e.deltaY:i[1]-a[1],c=e.target,d=Math.abs(s)>Math.abs(u)?"h":"v";if("touches"in e&&"h"===d&&"range"===c.type)return!1;var f=L(d,c);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=L(d,c)),!f)return!1;if(!n.current&&"changedTouches"in e&&(s||u)&&(n.current=o),!o)return!0;var p=n.current||o;return I(p,t,e,"h"===p?s:u,!0)},[]),u=i.useCallback(function(e){if(H.length&&H[H.length-1]===a){var r="deltaY"in e?V(e):F(e),n=t.current.filter(function(t){var n;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(n=t.delta)[0]===r[0]&&n[1]===r[1]})[0];if(n&&n.should){e.cancelable&&e.preventDefault();return}if(!n){var o=(l.current.shards||[]).map(B).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?s(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=i.useCallback(function(e,r,n,o){var a={name:e,delta:r,target:n,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(n)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),d=i.useCallback(function(e){r.current=F(e),n.current=void 0},[]),f=i.useCallback(function(t){c(t.type,V(t),t.target,s(t,e.lockRef.current))},[]),p=i.useCallback(function(t){c(t.type,F(t),t.target,s(t,e.lockRef.current))},[]);i.useEffect(function(){return H.push(a),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",u,P),document.addEventListener("touchmove",u,P),document.addEventListener("touchstart",d,P),function(){H=H.filter(function(e){return e!==a}),document.removeEventListener("wheel",u,P),document.removeEventListener("touchmove",u,P),document.removeEventListener("touchstart",d,P)}},[]);var m=e.removeScrollBar,h=e.inert;return i.createElement(i.Fragment,null,h?i.createElement(a,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,m?i.createElement(D,{gapMode:e.gapMode}):null)}),v);var U=i.forwardRef(function(e,t){return i.createElement(h,o({},e,{ref:t,sideCar:W}))});U.classNames=h.classNames;let G=U},9817:(e,t,r)=>{r.d(t,{qW:()=>f});var n,o=r(4545),a=r(4157),i=r(6856),l=r(4489),s=r(5196),u=r(7093),c="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{var r,f;let{disableOutsidePointerEvents:h=!1,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:b,onDismiss:w,...x}=e,E=o.useContext(d),[k,C]=o.useState(null),S=null!==(f=null==k?void 0:k.ownerDocument)&&void 0!==f?f:null===(r=globalThis)||void 0===r?void 0:r.document,[,R]=o.useState({}),A=(0,l.s)(t,e=>C(e)),T=Array.from(E.layers),[D]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),j=T.indexOf(D),M=k?T.indexOf(k):-1,P=E.layersWithOutsidePointerEventsDisabled.size>0,N=M>=j,L=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,n=(0,s.c)(e),a=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!a.current){let t=function(){m("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(r.removeEventListener("click",i.current),i.current=t,r.addEventListener("click",i.current,{once:!0})):t()}else r.removeEventListener("click",i.current);a.current=!1},t=window.setTimeout(()=>{r.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),r.removeEventListener("pointerdown",e),r.removeEventListener("click",i.current)}},[r,n]),{onPointerDownCapture:()=>a.current=!0}}(e=>{let t=e.target,r=[...E.branches].some(e=>e.contains(t));!N||r||(null==g||g(e),null==b||b(e),e.defaultPrevented||null==w||w())},S),O=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,n=(0,s.c)(e),a=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!a.current&&m("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return r.addEventListener("focusin",e),()=>r.removeEventListener("focusin",e)},[r,n]),{onFocusCapture:()=>a.current=!0,onBlurCapture:()=>a.current=!1}}(e=>{let t=e.target;[...E.branches].some(e=>e.contains(t))||(null==y||y(e),null==b||b(e),e.defaultPrevented||null==w||w())},S);return!function(e,t=globalThis?.document){let r=(0,s.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{M===E.layers.size-1&&(null==v||v(e),!e.defaultPrevented&&w&&(e.preventDefault(),w()))},S),o.useEffect(()=>{if(k)return h&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(n=S.body.style.pointerEvents,S.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(k)),E.layers.add(k),p(),()=>{h&&1===E.layersWithOutsidePointerEventsDisabled.size&&(S.body.style.pointerEvents=n)}},[k,S,h,E]),o.useEffect(()=>()=>{k&&(E.layers.delete(k),E.layersWithOutsidePointerEventsDisabled.delete(k),p())},[k,E]),o.useEffect(()=>{let e=()=>R({});return document.addEventListener(c,e),()=>document.removeEventListener(c,e)},[]),(0,u.jsx)(i.sG.div,{...x,ref:A,style:{pointerEvents:P?N?"auto":"none":void 0,...e.style},onFocusCapture:(0,a.m)(e.onFocusCapture,O.onFocusCapture),onBlurCapture:(0,a.m)(e.onBlurCapture,O.onBlurCapture),onPointerDownCapture:(0,a.m)(e.onPointerDownCapture,L.onPointerDownCapture)})});function p(){let e=new CustomEvent(c);document.dispatchEvent(e)}function m(e,t,r,n){let{discrete:o}=n,a=r.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&a.addEventListener(e,t,{once:!0}),o?(0,i.hO)(a,l):a.dispatchEvent(l)}f.displayName="DismissableLayer",o.forwardRef((e,t)=>{let r=o.useContext(d),n=o.useRef(null),a=(0,l.s)(t,n);return o.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,u.jsx)(i.sG.div,{...e,ref:a})}).displayName="DismissableLayerBranch"},9947:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(436).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])}}]);