"use strict";exports.id=948,exports.ids=[948],exports.modules={2893:(t,e,i)=>{i.d(e,{E:()=>r});var n=i(44508);let r=i(23235).B?n.useLayoutEffect:n.useEffect},7014:(t,e,i)=>{i.d(e,{M:()=>r});var n=i(44508);function r(t){let e=(0,n.useRef)(null);return null===e.current&&(e.current=t()),e.current}},7729:(t,e,i)=>{i.d(e,{A:()=>P});var n=i(44508);function r(t){return"[object Object]"===Object.prototype.toString.call(t)||Array.isArray(t)}function s(t,e){let i=Object.keys(t),n=Object.keys(e);return i.length===n.length&&JSON.stringify(Object.keys(t.breakpoints||{}))===JSON.stringify(Object.keys(e.breakpoints||{}))&&i.every(i=>{let n=t[i],o=e[i];return"function"==typeof n?`${n}`==`${o}`:r(n)&&r(o)?s(n,o):n===o})}function o(t){return t.concat().sort((t,e)=>t.name>e.name?1:-1).map(t=>t.options)}function a(t){return"number"==typeof t}function l(t){return"string"==typeof t}function u(t){return"boolean"==typeof t}function c(t){return"[object Object]"===Object.prototype.toString.call(t)}function h(t){return Math.abs(t)}function d(t){return Math.sign(t)}function p(t){return y(t).map(Number)}function m(t){return t[f(t)]}function f(t){return Math.max(0,t.length-1)}function g(t,e=0){return Array.from(Array(t),(t,i)=>e+i)}function y(t){return Object.keys(t)}function v(t,e){return void 0!==e.MouseEvent&&t instanceof e.MouseEvent}function b(){let t=[],e={add:function(i,n,r,s={passive:!0}){let o;return"addEventListener"in i?(i.addEventListener(n,r,s),o=()=>i.removeEventListener(n,r,s)):(i.addListener(r),o=()=>i.removeListener(r)),t.push(o),e},clear:function(){t=t.filter(t=>t())}};return e}function x(t=0,e=0){let i=h(t-e);function n(i){return i<t||i>e}return{length:i,max:e,min:t,constrain:function(i){return n(i)?i<t?t:e:i},reachedAny:n,reachedMax:function(t){return t>e},reachedMin:function(e){return e<t},removeOffset:function(t){return i?t-i*Math.ceil((t-e)/i):t}}}function w(t){let e=t;function i(t){return a(t)?t:t.get()}return{get:function(){return e},set:function(t){e=i(t)},add:function(t){e+=i(t)},subtract:function(t){e-=i(t)}}}function k(t,e){let i="x"===t.scroll?function(t){return`translate3d(${t}px,0px,0px)`}:function(t){return`translate3d(0px,${t}px,0px)`},n=e.style,r=null,s=!1;return{clear:function(){s||(n.transform="",e.getAttribute("style")||e.removeAttribute("style"))},to:function(e){if(s)return;let o=Math.round(100*t.direction(e))/100;o!==r&&(n.transform=i(o),r=o)},toggleActive:function(t){s=!t}}}let T={align:"center",axis:"x",container:null,slides:null,containScroll:"trimSnaps",direction:"ltr",slidesToScroll:1,inViewThreshold:0,breakpoints:{},dragFree:!1,dragThreshold:10,loop:!1,skipSnaps:!1,duration:25,startIndex:0,active:!0,watchDrag:!0,watchResize:!0,watchSlides:!0,watchFocus:!0};function S(t,e,i){let n,r,s,o,P,A=t.ownerDocument,M=A.defaultView,E=function(t){function e(t,e){return function t(e,i){return[e,i].reduce((e,i)=>(y(i).forEach(n=>{let r=e[n],s=i[n],o=c(r)&&c(s);e[n]=o?t(r,s):s}),e),{})}(t,e||{})}return{mergeOptions:e,optionsAtMedia:function(i){let n=i.breakpoints||{},r=y(n).filter(e=>t.matchMedia(e).matches).map(t=>n[t]).reduce((t,i)=>e(t,i),{});return e(i,r)},optionsMediaQueries:function(e){return e.map(t=>y(t.breakpoints||{})).reduce((t,e)=>t.concat(e),[]).map(t.matchMedia)}}}(M),V=(P=[],{init:function(t,e){return(P=e.filter(({options:t})=>!1!==E.optionsAtMedia(t).active)).forEach(e=>e.init(t,E)),e.reduce((t,e)=>Object.assign(t,{[e.name]:e}),{})},destroy:function(){P=P.filter(t=>t.destroy())}}),D=b(),C=function(){let t,e={},i={init:function(e){t=e},emit:function(n){return(e[n]||[]).forEach(e=>e(t,n)),i},off:function(t,n){return e[t]=(e[t]||[]).filter(t=>t!==n),i},on:function(t,n){return e[t]=(e[t]||[]).concat([n]),i},clear:function(){e={}}};return i}(),{mergeOptions:j,optionsAtMedia:L,optionsMediaQueries:R}=E,{on:O,off:F,emit:B}=C,I=!1,z=j(T,S.globalOptions),N=j(z),U=[];function $(e,i){if(I)return;N=L(z=j(z,e)),U=i||U;let{container:c,slides:T}=N;s=(l(c)?t.querySelector(c):c)||t.children[0];let S=l(T)?s.querySelectorAll(T):T;o=[].slice.call(S||s.children),n=function e(i){let n=function(t,e,i,n,r,s,o){let c,T,{align:S,axis:P,direction:A,startIndex:M,loop:E,duration:V,dragFree:D,dragThreshold:C,inViewThreshold:j,slidesToScroll:L,skipSnaps:R,containScroll:O,watchResize:F,watchSlides:B,watchDrag:I,watchFocus:z}=s,N={measure:function(t){let{offsetTop:e,offsetLeft:i,offsetWidth:n,offsetHeight:r}=t;return{top:e,right:i+n,bottom:e+r,left:i,width:n,height:r}}},U=N.measure(e),$=i.map(N.measure),W=function(t,e){let i="rtl"===e,n="y"===t,r=!n&&i?-1:1;return{scroll:n?"y":"x",cross:n?"x":"y",startEdge:n?"top":i?"right":"left",endEdge:n?"bottom":i?"left":"right",measureSize:function(t){let{height:e,width:i}=t;return n?e:i},direction:function(t){return t*r}}}(P,A),H=W.measureSize(U),X={measure:function(t){return t/100*H}},Y=function(t,e){let i={start:function(){return 0},center:function(t){return(e-t)/2},end:function(t){return e-t}};return{measure:function(n,r){return l(t)?i[t](n):t(e,n,r)}}}(S,H),q=!E&&!!O,{slideSizes:G,slideSizesWithGaps:K,startGap:Z,endGap:_}=function(t,e,i,n,r,s){let{measureSize:o,startEdge:a,endEdge:l}=t,u=i[0]&&r,c=function(){if(!u)return 0;let t=i[0];return h(e[a]-t[a])}(),d=u?parseFloat(s.getComputedStyle(m(n)).getPropertyValue(`margin-${l}`)):0,p=i.map(o),g=i.map((t,e,i)=>{let n=e===f(i);return e?n?p[e]+d:i[e+1][a]-t[a]:p[e]+c}).map(h);return{slideSizes:p,slideSizesWithGaps:g,startGap:c,endGap:d}}(W,U,$,i,E||!!O,r),Q=function(t,e,i,n,r,s,o,l,u){let{startEdge:c,endEdge:d,direction:g}=t,y=a(i);return{groupSlides:function(t){return y?p(t).filter(t=>t%i==0).map(e=>t.slice(e,e+i)):t.length?p(t).reduce((i,a,u)=>{let p=m(i)||0,y=a===f(t),v=r[c]-s[p][c],b=r[c]-s[a][d],x=n||0!==p?0:g(o),w=h(b-(!n&&y?g(l):0)-(v+x));return u&&w>e+2&&i.push(a),y&&i.push(t.length),i},[]).map((e,i,n)=>{let r=Math.max(n[i-1]||0);return t.slice(r,e)}):[]}}}(W,H,L,E,U,$,Z,_,0),{snaps:J,snapsAligned:tt}=function(t,e,i,n,r){let{startEdge:s,endEdge:o}=t,{groupSlides:a}=r,l=a(n).map(t=>m(t)[o]-t[0][s]).map(h).map(e.measure),u=n.map(t=>i[s]-t[s]).map(t=>-h(t)),c=a(u).map(t=>t[0]).map((t,e)=>t+l[e]);return{snaps:u,snapsAligned:c}}(W,Y,U,$,Q),te=-m(J)+m(K),{snapsContained:ti,scrollContainLimit:tn}=function(t,e,i,n,r){let s=x(-e+t,0),o=i.map((t,e)=>{let{min:n,max:r}=s,o=s.constrain(t),a=e===f(i);return e?a||function(t,e){return 1>=h(t-e)}(n,o)?n:function(t,e){return 1>=h(t-e)}(r,o)?r:o:r}).map(t=>parseFloat(t.toFixed(3))),a=function(){let t=o[0],e=m(o);return x(o.lastIndexOf(t),o.indexOf(e)+1)}();function l(t,e){return 1>=h(t-e)}return{snapsContained:function(){if(e<=t+2)return[s.max];if("keepSnaps"===n)return o;let{min:i,max:r}=a;return o.slice(i,r)}(),scrollContainLimit:a}}(H,te,tt,O,0),tr=q?ti:tt,{limit:ts}=function(t,e,i){let n=e[0];return{limit:x(i?n-t:m(e),n)}}(te,tr,E),to=function t(e,i,n){let{constrain:r}=x(0,e),s=e+1,o=a(i);function a(t){return n?h((s+t)%s):r(t)}function l(){return t(e,o,n)}let u={get:function(){return o},set:function(t){return o=a(t),u},add:function(t){return l().set(o+t)},clone:l};return u}(f(tr),M,E),ta=to.clone(),tl=p(i),tu=({dragHandler:t,scrollBody:e,scrollBounds:i,options:{loop:n}})=>{n||i.constrain(t.pointerDown()),e.seek()},tc=({scrollBody:t,translate:e,location:i,offsetLocation:n,previousLocation:r,scrollLooper:s,slideLooper:o,dragHandler:a,animation:l,eventHandler:u,scrollBounds:c,options:{loop:h}},d)=>{let p=t.settled(),m=!c.shouldConstrain(),f=h?p:p&&m,g=f&&!a.pointerDown();g&&l.stop();let y=i.get()*d+r.get()*(1-d);n.set(y),h&&(s.loop(t.direction()),o.loop()),e.to(n.get()),g&&u.emit("settle"),f||u.emit("scroll")},th=function(t,e,i,n){let r=b(),s=1e3/60,o=null,a=0,l=0;function u(t){if(!l)return;o||(o=t,i(),i());let r=t-o;for(o=t,a+=r;a>=s;)i(),a-=s;n(a/s),l&&(l=e.requestAnimationFrame(u))}function c(){e.cancelAnimationFrame(l),o=null,a=0,l=0}return{init:function(){r.add(t,"visibilitychange",()=>{t.hidden&&(o=null,a=0)})},destroy:function(){c(),r.clear()},start:function(){l||(l=e.requestAnimationFrame(u))},stop:c,update:i,render:n}}(n,r,()=>tu(tP),t=>tc(tP,t)),td=tr[to.get()],tp=w(td),tm=w(td),tf=w(td),tg=w(td),ty=function(t,e,i,n,r,s){let o=0,a=0,l=r,u=.68,c=t.get(),p=0;function m(t){return l=t,g}function f(t){return u=t,g}let g={direction:function(){return a},duration:function(){return l},velocity:function(){return o},seek:function(){let e=n.get()-t.get(),r=0;return l?(i.set(t),o+=e/l,o*=u,c+=o,t.add(o),r=c-p):(o=0,i.set(n),t.set(n),r=e),a=d(r),p=c,g},settled:function(){return .001>h(n.get()-e.get())},useBaseFriction:function(){return f(.68)},useBaseDuration:function(){return m(r)},useFriction:f,useDuration:m};return g}(tp,tf,tm,tg,V,.68),tv=function(t,e,i,n,r){let{reachedAny:s,removeOffset:o,constrain:a}=n;function l(t){return t.concat().sort((t,e)=>h(t)-h(e))[0]}function u(e,n){let r=[e,e+i,e-i];if(!t)return e;if(!n)return l(r);let s=r.filter(t=>d(t)===n);return s.length?l(s):m(r)-i}return{byDistance:function(i,n){let l=r.get()+i,{index:c,distance:d}=function(i){let n=t?o(i):a(i),{index:r}=e.map((t,e)=>({diff:u(t-n,0),index:e})).sort((t,e)=>h(t.diff)-h(e.diff))[0];return{index:r,distance:n}}(l),p=!t&&s(l);if(!n||p)return{index:c,distance:i};let m=i+u(e[c]-d,0);return{index:c,distance:m}},byIndex:function(t,i){let n=u(e[t]-r.get(),i);return{index:t,distance:n}},shortcut:u}}(E,tr,te,ts,tg),tb=function(t,e,i,n,r,s,o){function a(r){let a=r.distance,l=r.index!==e.get();s.add(a),a&&(n.duration()?t.start():(t.update(),t.render(1),t.update())),l&&(i.set(e.get()),e.set(r.index),o.emit("select"))}return{distance:function(t,e){a(r.byDistance(t,e))},index:function(t,i){let n=e.clone().set(t);a(r.byIndex(n.get(),i))}}}(th,to,ta,ty,tv,tg,o),tx=function(t){let{max:e,length:i}=t;return{get:function(t){return i?-((t-e)/i):0}}}(ts),tw=b(),tk=function(t,e,i,n){let r,s={},o=null,a=null,l=!1;return{init:function(){r=new IntersectionObserver(t=>{l||(t.forEach(t=>{s[e.indexOf(t.target)]=t}),o=null,a=null,i.emit("slidesInView"))},{root:t.parentElement,threshold:n}),e.forEach(t=>r.observe(t))},destroy:function(){r&&r.disconnect(),l=!0},get:function(t=!0){if(t&&o)return o;if(!t&&a)return a;let e=y(s).reduce((e,i)=>{let n=parseInt(i),{isIntersecting:r}=s[n];return(t&&r||!t&&!r)&&e.push(n),e},[]);return t&&(o=e),t||(a=e),e}}}(e,i,o,j),{slideRegistry:tT}=function(t,e,i,n,r,s){let{groupSlides:o}=r,{min:a,max:l}=n;return{slideRegistry:function(){let n=o(s);return 1===i.length?[s]:t&&"keepSnaps"!==e?n.slice(a,l).map((t,e,i)=>{let n=e===f(i);return e?n?g(f(s)-m(i)[0]+1,m(i)[0]):t:g(m(i[0])+1)}):n}()}}(q,O,tr,tn,Q,tl),tS=function(t,e,i,n,r,s,o,l){let c={passive:!0,capture:!0},h=0;function d(t){"Tab"===t.code&&(h=new Date().getTime())}return{init:function(p){l&&(s.add(document,"keydown",d,!1),e.forEach((e,d)=>{s.add(e,"focus",e=>{(u(l)||l(p,e))&&function(e){if(new Date().getTime()-h>10)return;o.emit("slideFocusStart"),t.scrollLeft=0;let s=i.findIndex(t=>t.includes(e));a(s)&&(r.useDuration(0),n.index(s,0),o.emit("slideFocus"))}(d)},c)}))}}}(t,i,tT,tb,ty,tw,o,z),tP={ownerDocument:n,ownerWindow:r,eventHandler:o,containerRect:U,slideRects:$,animation:th,axis:W,dragHandler:function(t,e,i,n,r,s,o,a,l,c,p,m,f,g,y,w,k,T,S){let{cross:P,direction:A}=t,M=["INPUT","SELECT","TEXTAREA"],E={passive:!1},V=b(),D=b(),C=x(50,225).constrain(g.measure(20)),j={mouse:300,touch:400},L={mouse:500,touch:600},R=y?43:25,O=!1,F=0,B=0,I=!1,z=!1,N=!1,U=!1;function $(t){if(!v(t,n)&&t.touches.length>=2)return W(t);let e=s.readPoint(t),i=s.readPoint(t,P),o=h(e-F),l=h(i-B);if(!z&&!U&&(!t.cancelable||!(z=o>l)))return W(t);let u=s.pointerMove(t);o>w&&(N=!0),c.useFriction(.3).useDuration(.75),a.start(),r.add(A(u)),t.preventDefault()}function W(t){let e=p.byDistance(0,!1).index!==m.get(),i=s.pointerUp(t)*(y?L:j)[U?"mouse":"touch"],n=function(t,e){let i=m.add(-+d(t)),n=p.byDistance(t,!y).distance;return y||h(t)<C?n:k&&e?.5*n:p.byIndex(i.get(),0).distance}(A(i),e),r=function(t,e){var i,n;if(0===t||0===e||h(t)<=h(e))return 0;let r=(i=h(t),n=h(e),h(i-n));return h(r/t)}(i,n);z=!1,I=!1,D.clear(),c.useDuration(R-10*r).useFriction(.68+r/50),l.distance(n,!y),U=!1,f.emit("pointerUp")}function H(t){N&&(t.stopPropagation(),t.preventDefault(),N=!1)}return{init:function(t){S&&V.add(e,"dragstart",t=>t.preventDefault(),E).add(e,"touchmove",()=>void 0,E).add(e,"touchend",()=>void 0).add(e,"touchstart",a).add(e,"mousedown",a).add(e,"touchcancel",W).add(e,"contextmenu",W).add(e,"click",H,!0);function a(a){(u(S)||S(t,a))&&function(t){let a=v(t,n);if(U=a,N=y&&a&&!t.buttons&&O,O=h(r.get()-o.get())>=2,(!a||0===t.button)&&!function(t){let e=t.nodeName||"";return M.includes(e)}(t.target)){I=!0,s.pointerDown(t),c.useFriction(0).useDuration(0),r.set(o);let n=U?i:e;D.add(n,"touchmove",$,E).add(n,"touchend",W).add(n,"mousemove",$,E).add(n,"mouseup",W),F=s.readPoint(t),B=s.readPoint(t,P),f.emit("pointerDown")}}(a)}},destroy:function(){V.clear(),D.clear()},pointerDown:function(){return I}}}(W,t,n,r,tg,function(t,e){let i,n;function r(t){return t.timeStamp}function s(i,n){let r=n||t.scroll,s=`client${"x"===r?"X":"Y"}`;return(v(i,e)?i:i.touches[0])[s]}return{pointerDown:function(t){return i=t,n=t,s(t)},pointerMove:function(t){let e=s(t)-s(n),o=r(t)-r(i)>170;return n=t,o&&(i=t),e},pointerUp:function(t){if(!i||!n)return 0;let e=s(n)-s(i),o=r(t)-r(i),a=r(t)-r(n)>170,l=e/o;return o&&!a&&h(l)>.1?l:0},readPoint:s}}(W,r),tp,th,tb,ty,tv,to,o,X,D,C,R,0,I),eventStore:tw,percentOfView:X,index:to,indexPrevious:ta,limit:ts,location:tp,offsetLocation:tf,previousLocation:tm,options:s,resizeHandler:function(t,e,i,n,r,s,o){let a,l,c=[t].concat(n),d=[],p=!1;function m(t){return r.measureSize(o.measure(t))}return{init:function(r){s&&(l=m(t),d=n.map(m),a=new ResizeObserver(i=>{(u(s)||s(r,i))&&function(i){for(let s of i){if(p)return;let i=s.target===t,o=n.indexOf(s.target),a=i?l:d[o];if(h(m(i?t:n[o])-a)>=.5){r.reInit(),e.emit("resize");break}}}(i)}),i.requestAnimationFrame(()=>{c.forEach(t=>a.observe(t))}))},destroy:function(){p=!0,a&&a.disconnect()}}}(e,o,r,i,W,F,N),scrollBody:ty,scrollBounds:function(t,e,i,n,r){let s=r.measure(10),o=r.measure(50),a=x(.1,.99),l=!1;function u(){return!!(!l&&t.reachedAny(i.get())&&t.reachedAny(e.get()))}return{shouldConstrain:u,constrain:function(r){if(!u())return;let l=t.reachedMin(e.get())?"min":"max",c=h(t[l]-e.get()),d=i.get()-e.get(),p=a.constrain(c/o);i.subtract(d*p),!r&&h(d)<s&&(i.set(t.constrain(i.get())),n.useDuration(25).useBaseFriction())},toggleActive:function(t){l=!t}}}(ts,tf,tg,ty,X),scrollLooper:function(t,e,i,n){let{reachedMin:r,reachedMax:s}=x(e.min+.1,e.max+.1);return{loop:function(e){if(!(1===e?s(i.get()):-1===e&&r(i.get())))return;let o=-(t*+e);n.forEach(t=>t.add(o))}}}(te,ts,tf,[tp,tf,tm,tg]),scrollProgress:tx,scrollSnapList:tr.map(tx.get),scrollSnaps:tr,scrollTarget:tv,scrollTo:tb,slideLooper:function(t,e,i,n,r,s,o,a,l){let u=p(r),c=p(r).reverse(),h=f(m(c,o[0]),i,!1).concat(f(m(u,e-o[0]-1),-i,!0));function d(t,e){return t.reduce((t,e)=>t-r[e],e)}function m(t,e){return t.reduce((t,i)=>d(t,e)>0?t.concat([i]):t,[])}function f(r,o,u){let c=s.map((t,i)=>({start:t-n[i]+.5+o,end:t+e-.5+o}));return r.map(e=>{let n=u?0:-i,r=u?i:0,s=c[e][u?"end":"start"];return{index:e,loopPoint:s,slideLocation:w(-1),translate:k(t,l[e]),target:()=>a.get()>s?n:r}})}return{canLoop:function(){return h.every(({index:t})=>.1>=d(u.filter(e=>e!==t),e))},clear:function(){h.forEach(t=>t.translate.clear())},loop:function(){h.forEach(t=>{let{target:e,translate:i,slideLocation:n}=t,r=e();r!==n.get()&&(i.to(r),n.set(r))})},loopPoints:h}}(W,H,te,G,K,J,tr,tf,i),slideFocus:tS,slidesHandler:(T=!1,{init:function(t){B&&(c=new MutationObserver(e=>{!T&&(u(B)||B(t,e))&&function(e){for(let i of e)if("childList"===i.type){t.reInit(),o.emit("slidesChanged");break}}(e)})).observe(e,{childList:!0})},destroy:function(){c&&c.disconnect(),T=!0}}),slidesInView:tk,slideIndexes:tl,slideRegistry:tT,slidesToScroll:Q,target:tg,translate:k(W,e)};return tP}(t,s,o,A,M,i,C);return i.loop&&!n.slideLooper.canLoop()?e(Object.assign({},i,{loop:!1})):n}(N),R([z,...U.map(({options:t})=>t)]).forEach(t=>D.add(t,"change",W)),N.active&&(n.translate.to(n.location.get()),n.animation.init(),n.slidesInView.init(),n.slideFocus.init(q),n.eventHandler.init(q),n.resizeHandler.init(q),n.slidesHandler.init(q),n.options.loop&&n.slideLooper.loop(),s.offsetParent&&o.length&&n.dragHandler.init(q),r=V.init(q,U))}function W(t,e){let i=Y();H(),$(j({startIndex:i},t),e),C.emit("reInit")}function H(){n.dragHandler.destroy(),n.eventStore.clear(),n.translate.clear(),n.slideLooper.clear(),n.resizeHandler.destroy(),n.slidesHandler.destroy(),n.slidesInView.destroy(),n.animation.destroy(),V.destroy(),D.clear()}function X(t,e,i){N.active&&!I&&(n.scrollBody.useBaseFriction().useDuration(!0===e?0:N.duration),n.scrollTo.index(t,i||0))}function Y(){return n.index.get()}let q={canScrollNext:function(){return n.index.add(1).get()!==Y()},canScrollPrev:function(){return n.index.add(-1).get()!==Y()},containerNode:function(){return s},internalEngine:function(){return n},destroy:function(){I||(I=!0,D.clear(),H(),C.emit("destroy"),C.clear())},off:F,on:O,emit:B,plugins:function(){return r},previousScrollSnap:function(){return n.indexPrevious.get()},reInit:W,rootNode:function(){return t},scrollNext:function(t){X(n.index.add(1).get(),t,-1)},scrollPrev:function(t){X(n.index.add(-1).get(),t,1)},scrollProgress:function(){return n.scrollProgress.get(n.offsetLocation.get())},scrollSnapList:function(){return n.scrollSnapList},scrollTo:X,selectedScrollSnap:Y,slideNodes:function(){return o},slidesInView:function(){return n.slidesInView.get()},slidesNotInView:function(){return n.slidesInView.get(!1)}};return $(e,i),setTimeout(()=>C.emit("init"),0),q}function P(t={},e=[]){let i=(0,n.useRef)(t),r=(0,n.useRef)(e),[a,l]=(0,n.useState)(),[u,c]=(0,n.useState)(),h=(0,n.useCallback)(()=>{a&&a.reInit(i.current,r.current)},[a]);return(0,n.useEffect)(()=>{s(i.current,t)||(i.current=t,h())},[t,h]),(0,n.useEffect)(()=>{!function(t,e){if(t.length!==e.length)return!1;let i=o(t),n=o(e);return i.every((t,e)=>s(t,n[e]))}(r.current,e)&&(r.current=e,h())},[e,h]),(0,n.useEffect)(()=>{if("undefined"!=typeof window&&window.document&&window.document.createElement&&u){S.globalOptions=P.globalOptions;let t=S(u,i.current,r.current);return l(t),()=>t.destroy()}l(void 0)},[u,l]),[c,a]}S.globalOptions=void 0,P.globalOptions=void 0},10905:(t,e,i)=>{let n;function r(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function s(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function o(t,e,i,n){if("function"==typeof e){let[r,o]=s(n);e=e(void 0!==i?i:t.custom,r,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[r,o]=s(n);e=e(void 0!==i?i:t.custom,r,o)}return e}function a(t,e,i){let n=t.getProps();return o(n,e,void 0!==i?i:n.custom,t)}function l(t,e){return t?.[e]??t?.default??t}i.d(e,{P:()=>sM});let u=t=>t,c={},h=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],d={value:null,addProjectionMetrics:null};function p(t,e){let i=!1,n=!0,r={delta:0,timestamp:0,isProcessing:!1},s=()=>i=!0,o=h.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,n=new Set,r=!1,s=!1,o=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){o.has(e)&&(c.schedule(e),t()),l++,e(a)}let c={schedule:(t,e=!1,s=!1)=>{let a=s&&r?i:n;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{n.delete(t),o.delete(t)},process:t=>{if(a=t,r){s=!0;return}r=!0,[i,n]=[n,i],i.forEach(u),e&&d.value&&d.value.frameloop[e].push(l),l=0,i.clear(),r=!1,s&&(s=!1,c.process(t))}};return c}(s,e?i:void 0),t),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:p,update:m,preRender:f,render:g,postRender:y}=o,v=()=>{let s=c.useManualTiming?r.timestamp:performance.now();i=!1,c.useManualTiming||(r.delta=n?1e3/60:Math.max(Math.min(s-r.timestamp,40),1)),r.timestamp=s,r.isProcessing=!0,a.process(r),l.process(r),u.process(r),p.process(r),m.process(r),f.process(r),g.process(r),y.process(r),r.isProcessing=!1,i&&e&&(n=!1,t(v))},b=()=>{i=!0,n=!0,r.isProcessing||t(v)};return{schedule:h.reduce((t,e)=>{let n=o[e];return t[e]=(t,e=!1,r=!1)=>(i||b(),n.schedule(t,e,r)),t},{}),cancel:t=>{for(let e=0;e<h.length;e++)o[h[e]].cancel(t)},state:r,steps:o}}let{schedule:m,cancel:f,state:g,steps:y}=p("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:u,!0),v=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],b=new Set(v),x=new Set(["width","height","top","left","right","bottom",...v]);function w(t,e){-1===t.indexOf(e)&&t.push(e)}function k(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class T{constructor(){this.subscriptions=[]}add(t){return w(this.subscriptions,t),()=>k(this.subscriptions,t)}notify(t,e,i){let n=this.subscriptions.length;if(n)if(1===n)this.subscriptions[0](t,e,i);else for(let r=0;r<n;r++){let n=this.subscriptions[r];n&&n(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function S(){n=void 0}let P={now:()=>(void 0===n&&P.set(g.isProcessing||c.useManualTiming?g.timestamp:performance.now()),n),set:t=>{n=t,queueMicrotask(S)}},A=t=>!isNaN(parseFloat(t)),M={current:void 0};class E{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=P.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=P.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=A(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new T);let i=this.events[t].add(e);return"change"===t?()=>{i(),m.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return M.current&&M.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=P.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function V(t,e){return new E(t,e)}let D=t=>Array.isArray(t),C=t=>!!(t&&t.getVelocity);function j(t,e){let i=t.getValue("willChange");if(C(i)&&i.add)return i.add(e);if(!i&&c.WillChange){let i=new c.WillChange("auto");t.addValue("willChange",i),i.add(e)}}let L=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),R="data-"+L("framerAppearId"),O=(t,e)=>i=>e(t(i)),F=(...t)=>t.reduce(O),B=(t,e,i)=>i>e?e:i<t?t:i,I=t=>1e3*t,z=t=>t/1e3,N={layout:0,mainThread:0,waapi:0},U=()=>{},$=()=>{},W=t=>e=>"string"==typeof e&&e.startsWith(t),H=W("--"),X=W("var(--"),Y=t=>!!X(t)&&q.test(t.split("/*")[0].trim()),q=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,G={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},K={...G,transform:t=>B(0,1,t)},Z={...G,default:1},_=t=>Math.round(1e5*t)/1e5,Q=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,J=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tt=(t,e)=>i=>!!("string"==typeof i&&J.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),te=(t,e,i)=>n=>{if("string"!=typeof n)return n;let[r,s,o,a]=n.match(Q);return{[t]:parseFloat(r),[e]:parseFloat(s),[i]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},ti=t=>B(0,255,t),tn={...G,transform:t=>Math.round(ti(t))},tr={test:tt("rgb","red"),parse:te("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:n=1})=>"rgba("+tn.transform(t)+", "+tn.transform(e)+", "+tn.transform(i)+", "+_(K.transform(n))+")"},ts={test:tt("#"),parse:function(t){let e="",i="",n="",r="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),n=t.substring(5,7),r=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),n=t.substring(3,4),r=t.substring(4,5),e+=e,i+=i,n+=n,r+=r),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(n,16),alpha:r?parseInt(r,16)/255:1}},transform:tr.transform},to=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),ta=to("deg"),tl=to("%"),tu=to("px"),tc=to("vh"),th=to("vw"),td={...tl,parse:t=>tl.parse(t)/100,transform:t=>tl.transform(100*t)},tp={test:tt("hsl","hue"),parse:te("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:n=1})=>"hsla("+Math.round(t)+", "+tl.transform(_(e))+", "+tl.transform(_(i))+", "+_(K.transform(n))+")"},tm={test:t=>tr.test(t)||ts.test(t)||tp.test(t),parse:t=>tr.test(t)?tr.parse(t):tp.test(t)?tp.parse(t):ts.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?tr.transform(t):tp.transform(t)},tf=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tg="number",ty="color",tv=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tb(t){let e=t.toString(),i=[],n={color:[],number:[],var:[]},r=[],s=0,o=e.replace(tv,t=>(tm.test(t)?(n.color.push(s),r.push(ty),i.push(tm.parse(t))):t.startsWith("var(")?(n.var.push(s),r.push("var"),i.push(t)):(n.number.push(s),r.push(tg),i.push(parseFloat(t))),++s,"${}")).split("${}");return{values:i,split:o,indexes:n,types:r}}function tx(t){return tb(t).values}function tw(t){let{split:e,types:i}=tb(t),n=e.length;return t=>{let r="";for(let s=0;s<n;s++)if(r+=e[s],void 0!==t[s]){let e=i[s];e===tg?r+=_(t[s]):e===ty?r+=tm.transform(t[s]):r+=t[s]}return r}}let tk=t=>"number"==typeof t?0:t,tT={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(Q)?.length||0)+(t.match(tf)?.length||0)>0},parse:tx,createTransformer:tw,getAnimatableNone:function(t){let e=tx(t);return tw(t)(e.map(tk))}};function tS(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function tP(t,e){return i=>i>0?e:t}let tA=(t,e,i)=>t+(e-t)*i,tM=(t,e,i)=>{let n=t*t,r=i*(e*e-n)+n;return r<0?0:Math.sqrt(r)},tE=[ts,tr,tp],tV=t=>tE.find(e=>e.test(t));function tD(t){let e=tV(t);if(U(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===tp&&(i=function({hue:t,saturation:e,lightness:i,alpha:n}){t/=360,i/=100;let r=0,s=0,o=0;if(e/=100){let n=i<.5?i*(1+e):i+e-i*e,a=2*i-n;r=tS(a,n,t+1/3),s=tS(a,n,t),o=tS(a,n,t-1/3)}else r=s=o=i;return{red:Math.round(255*r),green:Math.round(255*s),blue:Math.round(255*o),alpha:n}}(i)),i}let tC=(t,e)=>{let i=tD(t),n=tD(e);if(!i||!n)return tP(t,e);let r={...i};return t=>(r.red=tM(i.red,n.red,t),r.green=tM(i.green,n.green,t),r.blue=tM(i.blue,n.blue,t),r.alpha=tA(i.alpha,n.alpha,t),tr.transform(r))},tj=new Set(["none","hidden"]);function tL(t,e){return i=>tA(t,e,i)}function tR(t){return"number"==typeof t?tL:"string"==typeof t?Y(t)?tP:tm.test(t)?tC:tB:Array.isArray(t)?tO:"object"==typeof t?tm.test(t)?tC:tF:tP}function tO(t,e){let i=[...t],n=i.length,r=t.map((t,i)=>tR(t)(t,e[i]));return t=>{for(let e=0;e<n;e++)i[e]=r[e](t);return i}}function tF(t,e){let i={...t,...e},n={};for(let r in i)void 0!==t[r]&&void 0!==e[r]&&(n[r]=tR(t[r])(t[r],e[r]));return t=>{for(let e in n)i[e]=n[e](t);return i}}let tB=(t,e)=>{let i=tT.createTransformer(e),n=tb(t),r=tb(e);return n.indexes.var.length===r.indexes.var.length&&n.indexes.color.length===r.indexes.color.length&&n.indexes.number.length>=r.indexes.number.length?tj.has(t)&&!r.values.length||tj.has(e)&&!n.values.length?function(t,e){return tj.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):F(tO(function(t,e){let i=[],n={color:0,var:0,number:0};for(let r=0;r<e.values.length;r++){let s=e.types[r],o=t.indexes[s][n[s]],a=t.values[o]??0;i[r]=a,n[s]++}return i}(n,r),r.values),i):(U(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tP(t,e))};function tI(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?tA(t,e,i):tR(t)(t,e)}let tz=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>m.update(e,t),stop:()=>f(e),now:()=>g.isProcessing?g.timestamp:P.now()}},tN=(t,e,i=10)=>{let n="",r=Math.max(Math.round(e/i),2);for(let e=0;e<r;e++)n+=t(e/(r-1))+", ";return`linear(${n.substring(0,n.length-2)})`};function tU(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function t$(t,e,i){var n,r;let s=Math.max(e-5,0);return n=i-t(s),(r=e-s)?1e3/r*n:0}let tW={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function tH(t,e){return t*Math.sqrt(1-e*e)}let tX=["duration","bounce"],tY=["stiffness","damping","mass"];function tq(t,e){return e.some(e=>void 0!==t[e])}function tG(t=tW.visualDuration,e=tW.bounce){let i,n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:r,restDelta:s}=n,o=n.keyframes[0],a=n.keyframes[n.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:c,mass:h,duration:d,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:tW.velocity,stiffness:tW.stiffness,damping:tW.damping,mass:tW.mass,isResolvedFromDuration:!1,...t};if(!tq(t,tY)&&tq(t,tX))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),n=i*i,r=2*B(.05,1,1-(t.bounce||0))*Math.sqrt(n);e={...e,mass:tW.mass,stiffness:n,damping:r}}else{let i=function({duration:t=tW.duration,bounce:e=tW.bounce,velocity:i=tW.velocity,mass:n=tW.mass}){let r,s;U(t<=I(tW.maxDuration),"Spring duration must be 10 seconds or less");let o=1-e;o=B(tW.minDamping,tW.maxDamping,o),t=B(tW.minDuration,tW.maxDuration,z(t)),o<1?(r=e=>{let n=e*o,r=n*t;return .001-(n-i)/tH(e,o)*Math.exp(-r)},s=e=>{let n=e*o*t,s=Math.pow(o,2)*Math.pow(e,2)*t,a=Math.exp(-n),l=tH(Math.pow(e,2),o);return(n*i+i-s)*a*(-r(e)+.001>0?-1:1)/l}):(r=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),s=e=>t*t*(i-e)*Math.exp(-e*t));let a=function(t,e,i){let n=i;for(let i=1;i<12;i++)n-=t(n)/e(n);return n}(r,s,5/t);if(t=I(t),isNaN(a))return{stiffness:tW.stiffness,damping:tW.damping,duration:t};{let e=Math.pow(a,2)*n;return{stiffness:e,damping:2*o*Math.sqrt(n*e),duration:t}}}(t);(e={...e,...i,mass:tW.mass}).isResolvedFromDuration=!0}return e}({...n,velocity:-z(n.velocity||0)}),f=p||0,g=c/(2*Math.sqrt(u*h)),y=a-o,v=z(Math.sqrt(u/h)),b=5>Math.abs(y);if(r||(r=b?tW.restSpeed.granular:tW.restSpeed.default),s||(s=b?tW.restDelta.granular:tW.restDelta.default),g<1){let t=tH(v,g);i=e=>a-Math.exp(-g*v*e)*((f+g*v*y)/t*Math.sin(t*e)+y*Math.cos(t*e))}else if(1===g)i=t=>a-Math.exp(-v*t)*(y+(f+v*y)*t);else{let t=v*Math.sqrt(g*g-1);i=e=>{let i=Math.exp(-g*v*e),n=Math.min(t*e,300);return a-i*((f+g*v*y)*Math.sinh(n)+t*y*Math.cosh(n))/t}}let x={calculatedDuration:m&&d||null,next:t=>{let e=i(t);if(m)l.done=t>=d;else{let n=0===t?f:0;g<1&&(n=0===t?I(f):t$(i,t,e));let o=Math.abs(a-e)<=s;l.done=Math.abs(n)<=r&&o}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(tU(x),2e4),e=tN(e=>x.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return x}function tK({keyframes:t,velocity:e=0,power:i=.8,timeConstant:n=325,bounceDamping:r=10,bounceStiffness:s=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:c}){let h,d,p=t[0],m={done:!1,value:p},f=t=>void 0!==a&&t<a||void 0!==l&&t>l,g=t=>void 0===a?l:void 0===l?a:Math.abs(a-t)<Math.abs(l-t)?a:l,y=i*e,v=p+y,b=void 0===o?v:o(v);b!==v&&(y=b-p);let x=t=>-y*Math.exp(-t/n),w=t=>b+x(t),k=t=>{let e=x(t),i=w(t);m.done=Math.abs(e)<=u,m.value=m.done?b:i},T=t=>{f(m.value)&&(h=t,d=tG({keyframes:[m.value,g(m.value)],velocity:t$(w,t,m.value),damping:r,stiffness:s,restDelta:u,restSpeed:c}))};return T(0),{calculatedDuration:null,next:t=>{let e=!1;return(d||void 0!==h||(e=!0,k(t),T(t)),void 0!==h&&t>=h)?d.next(t-h):(e||k(t),m)}}}tG.applyToOptions=t=>{let e=function(t,e=100,i){let n=i({...t,keyframes:[0,e]}),r=Math.min(tU(n),2e4);return{type:"keyframes",ease:t=>n.next(r*t).value/e,duration:z(r)}}(t,100,tG);return t.ease=e.ease,t.duration=I(e.duration),t.type="keyframes",t};let tZ=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function t_(t,e,i,n){if(t===e&&i===n)return u;let r=e=>(function(t,e,i,n,r){let s,o,a=0;do(s=tZ(o=e+(i-e)/2,n,r)-t)>0?i=o:e=o;while(Math.abs(s)>1e-7&&++a<12);return o})(e,0,1,t,i);return t=>0===t||1===t?t:tZ(r(t),e,n)}let tQ=t_(.42,0,1,1),tJ=t_(0,0,.58,1),t0=t_(.42,0,.58,1),t1=t=>Array.isArray(t)&&"number"!=typeof t[0],t2=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,t4=t=>e=>1-t(1-e),t3=t_(.33,1.53,.69,.99),t5=t4(t3),t6=t2(t5),t9=t=>(t*=2)<1?.5*t5(t):.5*(2-Math.pow(2,-10*(t-1))),t7=t=>1-Math.sin(Math.acos(t)),t8=t4(t7),et=t2(t7),ee=t=>Array.isArray(t)&&"number"==typeof t[0],ei={linear:u,easeIn:tQ,easeInOut:t0,easeOut:tJ,circIn:t7,circInOut:et,circOut:t8,backIn:t5,backInOut:t6,backOut:t3,anticipate:t9},en=t=>"string"==typeof t,er=t=>{if(ee(t)){$(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,n,r]=t;return t_(e,i,n,r)}return en(t)?($(void 0!==ei[t],`Invalid easing type '${t}'`),ei[t]):t},es=(t,e,i)=>{let n=e-t;return 0===n?1:(i-t)/n};function eo({duration:t=300,keyframes:e,times:i,ease:n="easeInOut"}){let r=t1(n)?n.map(er):er(n),s={done:!1,value:e[0]},o=function(t,e,{clamp:i=!0,ease:n,mixer:r}={}){let s=t.length;if($(s===e.length,"Both input and output ranges must be the same length"),1===s)return()=>e[0];if(2===s&&e[0]===e[1])return()=>e[1];let o=t[0]===t[1];t[0]>t[s-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=function(t,e,i){let n=[],r=i||c.mix||tI,s=t.length-1;for(let i=0;i<s;i++){let s=r(t[i],t[i+1]);e&&(s=F(Array.isArray(e)?e[i]||u:e,s)),n.push(s)}return n}(e,n,r),l=a.length,h=i=>{if(o&&i<t[0])return e[0];let n=0;if(l>1)for(;n<t.length-2&&!(i<t[n+1]);n++);let r=es(t[n],t[n+1],i);return a[n](r)};return i?e=>h(B(t[0],t[s-1],e)):h}((i&&i.length===e.length?i:function(t){let e=[0];return function(t,e){let i=t[t.length-1];for(let n=1;n<=e;n++){let r=es(0,e,n);t.push(tA(i,1,r))}}(e,t.length-1),e}(e)).map(e=>e*t),e,{ease:Array.isArray(r)?r:e.map(()=>r||t0).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(s.value=o(e),s.done=e>=t,s)}}let ea=t=>null!==t;function el(t,{repeat:e,repeatType:i="loop"},n,r=1){let s=t.filter(ea),o=r<0||e&&"loop"!==i&&e%2==1?0:s.length-1;return o&&void 0!==n?n:s[o]}let eu={decay:tK,inertia:tK,tween:eo,keyframes:eo,spring:tG};function ec(t){"string"==typeof t.type&&(t.type=eu[t.type])}class eh{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let ed=t=>t/100;class ep extends eh{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==P.now()&&this.tick(P.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},N.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;ec(t);let{type:e=eo,repeat:i=0,repeatDelay:n=0,repeatType:r,velocity:s=0}=t,{keyframes:o}=t,a=e||eo;a!==eo&&"number"!=typeof o[0]&&(this.mixKeyframes=F(ed,tI(o[0],o[1])),o=[0,100]);let l=a({...t,keyframes:o});"mirror"===r&&(this.mirroredGenerator=a({...t,keyframes:[...o].reverse(),velocity:-s})),null===l.calculatedDuration&&(l.calculatedDuration=tU(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+n,this.totalDuration=this.resolvedDuration*(i+1)-n,this.generator=l}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:n,mixKeyframes:r,mirroredGenerator:s,resolvedDuration:o,calculatedDuration:a}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:u,repeat:c,repeatType:h,repeatDelay:d,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-n/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>n;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=n);let v=this.currentTime,b=i;if(c){let t=Math.min(this.currentTime,n)/o,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,c+1))%2&&("reverse"===h?(i=1-i,d&&(i-=d/o)):"mirror"===h&&(b=s)),v=B(0,1,i)*o}let x=y?{done:!1,value:u[0]}:b.next(v);r&&(x.value=r(x.value));let{done:w}=x;y||null===a||(w=this.playbackSpeed>=0?this.currentTime>=n:this.currentTime<=0);let k=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return k&&p!==tK&&(x.value=el(u,this.options,f,this.speed)),m&&m(x.value),k&&this.finish(),x}then(t,e){return this.finished.then(t,e)}get duration(){return z(this.calculatedDuration)}get time(){return z(this.currentTime)}set time(t){t=I(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(P.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=z(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=tz,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(P.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,N.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}let em=t=>180*t/Math.PI,ef=t=>ey(em(Math.atan2(t[1],t[0]))),eg={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:ef,rotateZ:ef,skewX:t=>em(Math.atan(t[1])),skewY:t=>em(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},ey=t=>((t%=360)<0&&(t+=360),t),ev=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),eb=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),ex={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:ev,scaleY:eb,scale:t=>(ev(t)+eb(t))/2,rotateX:t=>ey(em(Math.atan2(t[6],t[5]))),rotateY:t=>ey(em(Math.atan2(-t[2],t[0]))),rotateZ:ef,rotate:ef,skewX:t=>em(Math.atan(t[4])),skewY:t=>em(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function ew(t){return+!!t.includes("scale")}function ek(t,e){let i,n;if(!t||"none"===t)return ew(e);let r=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(r)i=ex,n=r;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=eg,n=e}if(!n)return ew(e);let s=i[e],o=n[1].split(",").map(eS);return"function"==typeof s?s(o):o[s]}let eT=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return ek(i,e)};function eS(t){return parseFloat(t.trim())}let eP=t=>t===G||t===tu,eA=new Set(["x","y","z"]),eM=v.filter(t=>!eA.has(t)),eE={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>ek(e,"x"),y:(t,{transform:e})=>ek(e,"y")};eE.translateX=eE.x,eE.translateY=eE.y;let eV=new Set,eD=!1,eC=!1,ej=!1;function eL(){if(eC){let t=Array.from(eV).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return eM.forEach(i=>{let n=t.getValue(i);void 0!==n&&(e.push([i,n.get()]),n.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}eC=!1,eD=!1,eV.forEach(t=>t.complete(ej)),eV.clear()}function eR(){eV.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(eC=!0)})}class eO{constructor(t,e,i,n,r,s=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=n,this.element=r,this.isAsync=s}scheduleResolve(){this.state="scheduled",this.isAsync?(eV.add(this),eD||(eD=!0,m.read(eR),m.resolveKeyframes(eL))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:n}=this;if(null===t[0]){let r=n?.get(),s=t[t.length-1];if(void 0!==r)t[0]=r;else if(i&&e){let n=i.readValue(e,s);null!=n&&(t[0]=n)}void 0===t[0]&&(t[0]=s),n&&void 0===r&&n.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),eV.delete(this)}cancel(){"scheduled"===this.state&&(eV.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let eF=t=>t.startsWith("--");function eB(t){let e;return()=>(void 0===e&&(e=t()),e)}let eI=eB(()=>void 0!==window.ScrollTimeline),ez={},eN=function(t,e){let i=eB(t);return()=>ez[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),eU=([t,e,i,n])=>`cubic-bezier(${t}, ${e}, ${i}, ${n})`,e$={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:eU([0,.65,.55,1]),circOut:eU([.55,0,1,.45]),backIn:eU([.31,.01,.66,-.59]),backOut:eU([.33,1.53,.69,.99])};function eW(t){return"function"==typeof t&&"applyToOptions"in t}class eH extends eh{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:n,pseudoElement:r,allowFlatten:s=!1,finalKeyframe:o,onComplete:a}=t;this.isPseudoElement=!!r,this.allowFlatten=s,this.options=t,$("string"!=typeof t.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:t,...e}){return eW(t)&&eN()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:n=0,duration:r=300,repeat:s=0,repeatType:o="loop",ease:a="easeOut",times:l}={},u){let c={[e]:i};l&&(c.offset=l);let h=function t(e,i){if(e)return"function"==typeof e?eN()?tN(e,i):"ease-out":ee(e)?eU(e):Array.isArray(e)?e.map(e=>t(e,i)||e$.easeOut):e$[e]}(a,r);Array.isArray(h)&&(c.easing=h),d.value&&N.waapi++;let p={delay:n,duration:r,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:s+1,direction:"reverse"===o?"alternate":"normal"};u&&(p.pseudoElement=u);let m=t.animate(c,p);return d.value&&m.finished.finally(()=>{N.waapi--}),m}(e,i,n,l,r),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!r){let t=el(n,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){eF(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"===t||"finished"===t||(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return z(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return z(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=I(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&eI())?(this.animation.timeline=t,u):e(this)}}let eX={anticipate:t9,backInOut:t6,circInOut:et};class eY extends eH{constructor(t){(function(t){"string"==typeof t.ease&&t.ease in eX&&(t.ease=eX[t.ease])})(t),ec(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:n,element:r,...s}=this.options;if(!e)return;if(void 0!==t){e.set(t);return}let o=new ep({...s,autoplay:!1}),a=I(this.finishedTime??this.time);e.setWithVelocity(o.sample(a-10).value,o.sample(a).value,10),o.stop()}}let eq=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tT.test(t)||"0"===t)&&!t.startsWith("url("));var eG,eK,eZ=i(80693);let e_=new Set(["opacity","clipPath","filter","transform"]),eQ=eB(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class eJ extends eh{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:n=0,repeatDelay:r=0,repeatType:s="loop",keyframes:o,name:a,motionValue:l,element:u,...c}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=P.now();let h={autoplay:t,delay:e,type:i,repeat:n,repeatDelay:r,repeatType:s,name:a,motionValue:l,element:u,...c},d=u?.KeyframeResolver||eO;this.keyframeResolver=new d(o,(t,e,i)=>this.onKeyframesResolved(t,e,h,!i),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,n){this.keyframeResolver=void 0;let{name:r,type:s,velocity:o,delay:a,isHandoff:l,onUpdate:h}=i;this.resolvedAt=P.now(),!function(t,e,i,n){let r=t[0];if(null===r)return!1;if("display"===e||"visibility"===e)return!0;let s=t[t.length-1],o=eq(r,e),a=eq(s,e);return U(o===a,`You are trying to animate ${e} from "${r}" to "${s}". ${r} is not an animatable value - to enable this animation set ${r} to a value animatable to ${s} via the \`style\` property.`),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||eW(i))&&n)}(t,r,s,o)&&((c.instantAnimations||!a)&&h?.(el(t,i,e)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);let d={startTime:n?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},p=!l&&function(t){let{motionValue:e,name:i,repeatDelay:n,repeatType:r,damping:s,type:o}=t;if(!(0,eZ.s)(e?.owner?.current))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return eQ()&&i&&e_.has(i)&&("transform"!==i||!l)&&!a&&!n&&"mirror"!==r&&0!==s&&"inertia"!==o}(d)?new eY({...d,element:d.motionValue.owner.current}):new ep(d);p.finished.then(()=>this.notifyFinished()).catch(u),this.pendingTimeline&&(this.stopTimeline=p.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=p}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),ej=!0,eR(),eL(),ej=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let e0=t=>null!==t,e1={type:"spring",stiffness:500,damping:25,restSpeed:10},e2=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),e4={type:"keyframes",duration:.8},e3={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},e5=(t,{keyframes:e})=>e.length>2?e4:b.has(t)?t.startsWith("scale")?e2(e[1]):e1:e3,e6=(t,e,i,n={},r,s)=>o=>{let a=l(n,t)||{},u=a.delay||n.delay||0,{elapsed:h=0}=n;h-=I(u);let d={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-h,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:s?void 0:r};!function({when:t,delay:e,delayChildren:i,staggerChildren:n,staggerDirection:r,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length}(a)&&Object.assign(d,e5(t,d)),d.duration&&(d.duration=I(d.duration)),d.repeatDelay&&(d.repeatDelay=I(d.repeatDelay)),void 0!==d.from&&(d.keyframes[0]=d.from);let p=!1;if(!1!==d.type&&(0!==d.duration||d.repeatDelay)||(d.duration=0,0!==d.delay||(p=!0)),(c.instantAnimations||c.skipAnimations)&&(p=!0,d.duration=0,d.delay=0),d.allowFlatten=!a.type&&!a.ease,p&&!s&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},n){let r=t.filter(e0),s=e&&"loop"!==i&&e%2==1?0:r.length-1;return r[s]}(d.keyframes,a);if(void 0!==t){m.update(()=>{d.onUpdate(t),d.onComplete()});return}}return a.isSync?new ep(d):new eJ(d)};function e9(t,e,{delay:i=0,transitionOverride:n,type:r}={}){let{transition:s=t.getDefaultTransition(),transitionEnd:o,...u}=e;n&&(s=n);let c=[],h=r&&t.animationState&&t.animationState.getState()[r];for(let e in u){let n=t.getValue(e,t.latestValues[e]??null),r=u[e];if(void 0===r||h&&function({protectedKeys:t,needsAnimating:e},i){let n=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,n}(h,e))continue;let o={delay:i,...l(s||{},e)},a=n.get();if(void 0!==a&&!n.isAnimating&&!Array.isArray(r)&&r===a&&!o.velocity)continue;let d=!1;if(window.MotionHandoffAnimation){let i=t.props[R];if(i){let t=window.MotionHandoffAnimation(i,e,m);null!==t&&(o.startTime=t,d=!0)}}j(t,e),n.start(e6(e,n,r,t.shouldReduceMotion&&x.has(e)?{type:!1}:o,t,d));let p=n.animation;p&&c.push(p)}return o&&Promise.all(c).then(()=>{m.update(()=>{o&&function(t,e){let{transitionEnd:i={},transition:n={},...r}=a(t,e)||{};for(let e in r={...r,...i}){var s;let i=D(s=r[e])?s[s.length-1]||0:s;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,V(i))}}(t,o)})}),c}function e7(t,e,i={}){let n=a(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:r=t.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(r=i.transitionOverride);let s=n?()=>Promise.all(e9(t,n,i)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(n=0)=>{let{delayChildren:s=0,staggerChildren:o,staggerDirection:a}=r;return function(t,e,i=0,n=0,r=1,s){let o=[],a=(t.variantChildren.size-1)*n,l=1===r?(t=0)=>t*n:(t=0)=>a-t*n;return Array.from(t.variantChildren).sort(e8).forEach((t,n)=>{t.notify("AnimationStart",e),o.push(e7(t,e,{...s,delay:i+l(n)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,s+n,o,a,i)}:()=>Promise.resolve(),{when:l}=r;if(!l)return Promise.all([s(),o(i.delay)]);{let[t,e]="beforeChildren"===l?[s,o]:[o,s];return t().then(()=>e())}}function e8(t,e){return t.sortNodePosition(e)}function it(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let n=0;n<i;n++)if(e[n]!==t[n])return!1;return!0}function ie(t){return"string"==typeof t||Array.isArray(t)}let ii=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ir=["initial",...ii],is=ir.length,io=[...ii].reverse(),ia=ii.length;function il(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function iu(){return{animate:il(!0),whileInView:il(),whileHover:il(),whileTap:il(),whileDrag:il(),whileFocus:il(),exit:il()}}class ic{constructor(t){this.isMounted=!1,this.node=t}update(){}}class ih extends ic{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let n;if(t.notify("AnimationStart",e),Array.isArray(e))n=Promise.all(e.map(e=>e7(t,e,i)));else if("string"==typeof e)n=e7(t,e,i);else{let r="function"==typeof e?a(t,e,i.custom):e;n=Promise.all(e9(t,r,i))}return n.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=iu(),n=!0,s=e=>(i,n)=>{let r=a(t,n,"exit"===e?t.presenceContext?.custom:void 0);if(r){let{transition:t,transitionEnd:e,...n}=r;i={...i,...n,...e}}return i};function o(o){let{props:l}=t,u=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<is;t++){let n=ir[t],r=e.props[n];(ie(r)||!1===r)&&(i[n]=r)}return i}(t.parent)||{},c=[],h=new Set,d={},p=1/0;for(let e=0;e<ia;e++){var m,f;let a=io[e],g=i[a],y=void 0!==l[a]?l[a]:u[a],v=ie(y),b=a===o?g.isActive:null;!1===b&&(p=e);let x=y===u[a]&&y!==l[a]&&v;if(x&&n&&t.manuallyAnimateOnMount&&(x=!1),g.protectedKeys={...d},!g.isActive&&null===b||!y&&!g.prevProp||r(y)||"boolean"==typeof y)continue;let w=(m=g.prevProp,"string"==typeof(f=y)?f!==m:!!Array.isArray(f)&&!it(f,m)),k=w||a===o&&g.isActive&&!x&&v||e>p&&v,T=!1,S=Array.isArray(y)?y:[y],P=S.reduce(s(a),{});!1===b&&(P={});let{prevResolvedValues:A={}}=g,M={...A,...P},E=e=>{k=!0,h.has(e)&&(T=!0,h.delete(e)),g.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in M){let e=P[t],i=A[t];if(d.hasOwnProperty(t))continue;let n=!1;(D(e)&&D(i)?it(e,i):e===i)?void 0!==e&&h.has(t)?E(t):g.protectedKeys[t]=!0:null!=e?E(t):h.add(t)}g.prevProp=y,g.prevResolvedValues=P,g.isActive&&(d={...d,...P}),n&&t.blockInitialAnimation&&(k=!1);let V=!(x&&w)||T;k&&V&&c.push(...S.map(t=>({animation:t,options:{type:a}})))}if(h.size){let e={};if("boolean"!=typeof l.initial){let i=a(t,Array.isArray(l.initial)?l.initial[0]:l.initial);i&&i.transition&&(e.transition=i.transition)}h.forEach(i=>{let n=t.getBaseTarget(i),r=t.getValue(i);r&&(r.liveStyle=!0),e[i]=n??null}),c.push({animation:e})}let g=!!c.length;return n&&(!1===l.initial||l.initial===l.animate)&&!t.manuallyAnimateOnMount&&(g=!1),n=!1,g?e(c):Promise.resolve()}return{animateChanges:o,setActive:function(e,n){if(i[e].isActive===n)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,n)),i[e].isActive=n;let r=o(e);for(let t in i)i[t].protectedKeys={};return r},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=iu(),n=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();r(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let id=0;class ip extends ic{constructor(){super(...arguments),this.id=id++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let n=this.node.animationState.setActive("exit",!t);e&&!t&&n.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let im={x:!1,y:!1};function ig(t,e,i,n={passive:!0}){return t.addEventListener(e,i,n),()=>t.removeEventListener(e,i)}let iy=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function iv(t){return{point:{x:t.pageX,y:t.pageY}}}let ib=t=>e=>iy(e)&&t(e,iv(e));function ix(t,e,i,n){return ig(t,e,ib(i),n)}function iw({top:t,left:e,right:i,bottom:n}){return{x:{min:e,max:i},y:{min:t,max:n}}}function ik(t){return t.max-t.min}function iT(t,e,i,n=.5){t.origin=n,t.originPoint=tA(e.min,e.max,t.origin),t.scale=ik(i)/ik(e),t.translate=tA(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function iS(t,e,i,n){iT(t.x,e.x,i.x,n?n.originX:void 0),iT(t.y,e.y,i.y,n?n.originY:void 0)}function iP(t,e,i){t.min=i.min+e.min,t.max=t.min+ik(e)}function iA(t,e,i){t.min=e.min-i.min,t.max=t.min+ik(e)}function iM(t,e,i){iA(t.x,e.x,i.x),iA(t.y,e.y,i.y)}let iE=()=>({translate:0,scale:1,origin:0,originPoint:0}),iV=()=>({x:iE(),y:iE()}),iD=()=>({min:0,max:0}),iC=()=>({x:iD(),y:iD()});function ij(t){return[t("x"),t("y")]}function iL(t){return void 0===t||1===t}function iR({scale:t,scaleX:e,scaleY:i}){return!iL(t)||!iL(e)||!iL(i)}function iO(t){return iR(t)||iF(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function iF(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function iB(t,e,i,n,r){return void 0!==r&&(t=n+r*(t-n)),n+i*(t-n)+e}function iI(t,e=0,i=1,n,r){t.min=iB(t.min,e,i,n,r),t.max=iB(t.max,e,i,n,r)}function iz(t,{x:e,y:i}){iI(t.x,e.translate,e.scale,e.originPoint),iI(t.y,i.translate,i.scale,i.originPoint)}function iN(t,e){t.min=t.min+e,t.max=t.max+e}function iU(t,e,i,n,r=.5){let s=tA(t.min,t.max,r);iI(t,e,i,s,n)}function i$(t,e){iU(t.x,e.x,e.scaleX,e.scale,e.originX),iU(t.y,e.y,e.scaleY,e.scale,e.originY)}function iW(t,e){return iw(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:n.y,right:n.x}}(t.getBoundingClientRect(),e))}let iH=({current:t})=>t?t.ownerDocument.defaultView:null;function iX(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let iY=(t,e)=>Math.abs(t-e);class iq{constructor(t,e,{transformPagePoint:i,contextWindow:n,dragSnapToOrigin:r=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=iZ(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(iY(t.x,e.x)**2+iY(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:n}=t,{timestamp:r}=g;this.history.push({...n,timestamp:r});let{onStart:s,onMove:o}=this.handlers;e||(s&&s(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=iG(e,this.transformPagePoint),m.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:n,resumeAnimation:r}=this.handlers;if(this.dragSnapToOrigin&&r&&r(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=iZ("pointercancel"===t.type?this.lastMoveEventInfo:iG(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,s),n&&n(t,s)},!iy(t))return;this.dragSnapToOrigin=r,this.handlers=e,this.transformPagePoint=i,this.contextWindow=n||window;let s=iG(iv(t),this.transformPagePoint),{point:o}=s,{timestamp:a}=g;this.history=[{...o,timestamp:a}];let{onSessionStart:l}=e;l&&l(t,iZ(s,this.history)),this.removeListeners=F(ix(this.contextWindow,"pointermove",this.handlePointerMove),ix(this.contextWindow,"pointerup",this.handlePointerUp),ix(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),f(this.updatePoint)}}function iG(t,e){return e?{point:e(t.point)}:t}function iK(t,e){return{x:t.x-e.x,y:t.y-e.y}}function iZ({point:t},e){return{point:t,delta:iK(t,i_(e)),offset:iK(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,n=null,r=i_(t);for(;i>=0&&(n=t[i],!(r.timestamp-n.timestamp>I(.1)));)i--;if(!n)return{x:0,y:0};let s=z(r.timestamp-n.timestamp);if(0===s)return{x:0,y:0};let o={x:(r.x-n.x)/s,y:(r.y-n.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,.1)}}function i_(t){return t[t.length-1]}function iQ(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function iJ(t,e){let i=e.min-t.min,n=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,n]=[n,i]),{min:i,max:n}}function i0(t,e,i){return{min:i1(t,e),max:i1(t,i)}}function i1(t,e){return"number"==typeof t?t:t[e]||0}let i2=new WeakMap;class i4{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=iC(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:n}=this.getProps();this.panSession=new iq(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(iv(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:n,onDragStart:r}=this.getProps();if(i&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(im[t])return null;else return im[t]=!0,()=>{im[t]=!1};return im.x||im.y?null:(im.x=im.y=!0,()=>{im.x=im.y=!1})}(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),ij(t=>{let e=this.getAxisMotionValue(t).get()||0;if(tl.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let n=i.layout.layoutBox[t];n&&(e=ik(n)*(parseFloat(e)/100))}}this.originPoint[t]=e}),r&&m.postRender(()=>r(t,e)),j(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:n,onDirectionLock:r,onDrag:s}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(n&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&r&&r(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),s&&s(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>ij(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:n,contextWindow:iH(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:n}=e;this.startAnimation(n);let{onDragEnd:r}=this.getProps();r&&m.postRender(()=>r(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:n}=this.getProps();if(!i||!i3(t,n,this.currentDirection))return;let r=this.getAxisMotionValue(t),s=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(s=function(t,{min:e,max:i},n){return void 0!==e&&t<e?t=n?tA(e,t,n.min):Math.max(t,e):void 0!==i&&t>i&&(t=n?tA(i,t,n.max):Math.min(t,i)),t}(s,this.constraints[t],this.elastic[t])),r.set(s)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,n=this.constraints;t&&iX(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:n,right:r}){return{x:iQ(t.x,i,r),y:iQ(t.y,e,n)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:i0(t,"left","right"),y:i0(t,"top","bottom")}}(e),n!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&ij(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!iX(e))return!1;let n=e.current;$(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:r}=this.visualElement;if(!r||!r.layout)return!1;let s=function(t,e,i){let n=iW(t,i),{scroll:r}=e;return r&&(iN(n.x,r.offset.x),iN(n.y,r.offset.y)),n}(n,r.root,this.visualElement.getTransformPagePoint()),o={x:iJ((t=r.layout.layoutBox).x,s.x),y:iJ(t.y,s.y)};if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=iw(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:n,dragTransition:r,dragSnapToOrigin:s,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(ij(o=>{if(!i3(o,e,this.currentDirection))return;let l=a&&a[o]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[o]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...r,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return j(this.visualElement,t),i.start(e6(t,i,0,e,this.visualElement,!1))}stopAnimation(){ij(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){ij(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){ij(e=>{let{drag:i}=this.getProps();if(!i3(e,i,this.currentDirection))return;let{projection:n}=this.visualElement,r=this.getAxisMotionValue(e);if(n&&n.layout){let{min:i,max:s}=n.layout.layoutBox[e];r.set(t[e]-tA(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!iX(e)||!i||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};ij(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();n[t]=function(t,e){let i=.5,n=ik(t),r=ik(e);return r>n?i=es(e.min,e.max-n,t.min):n>r&&(i=es(t.min,t.max-r,e.min)),B(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),ij(e=>{if(!i3(e,t,null))return;let i=this.getAxisMotionValue(e),{min:r,max:s}=this.constraints[e];i.set(tA(r,s,n[e]))})}addListeners(){if(!this.visualElement.current)return;i2.set(this.visualElement,this);let t=ix(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();iX(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,n=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),m.read(e);let r=ig(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(ij(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{r(),t(),n(),s&&s()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:n=!1,dragConstraints:r=!1,dragElastic:s=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:n,dragConstraints:r,dragElastic:s,dragMomentum:o}}}function i3(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class i5 extends ic{constructor(t){super(t),this.removeGroupControls=u,this.removeListeners=u,this.controls=new i4(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||u}unmount(){this.removeGroupControls(),this.removeListeners()}}let i6=t=>(e,i)=>{t&&m.postRender(()=>t(e,i))};class i9 extends ic{constructor(){super(...arguments),this.removePointerDownListener=u}onPointerDown(t){this.session=new iq(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:iH(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:n}=this.node.getProps();return{onSessionStart:i6(t),onStart:i6(e),onMove:i,onEnd:(t,e)=>{delete this.session,n&&m.postRender(()=>n(t,e))}}}mount(){this.removePointerDownListener=ix(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var i7=i(3641);let{schedule:i8}=p(queueMicrotask,!1);var nt=i(44508),ne=i(31933),ni=i(44490);let nn=(0,nt.createContext)({}),nr={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function ns(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let no={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!tu.test(t))return t;else t=parseFloat(t);let i=ns(t,e.target.x),n=ns(t,e.target.y);return`${i}% ${n}%`}},na={};class nl extends nt.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:n}=this.props,{projection:r}=t;for(let t in nc)na[t]=nc[t],H(t)&&(na[t].isCSSVariable=!0);r&&(e.group&&e.group.add(r),i&&i.register&&n&&i.register(r),r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),nr.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:n,isPresent:r}=this.props,{projection:s}=i;return s&&(s.isPresent=r,n||t.layoutDependency!==e||void 0===e||t.isPresent!==r?s.willUpdate():this.safeToRemove(),t.isPresent===r||(r?s.promote():s.relegate()||m.postRender(()=>{let t=s.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),i8.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:n}=t;n&&(n.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(n),i&&i.deregister&&i.deregister(n))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function nu(t){let[e,i]=(0,ne.xQ)(),n=(0,nt.useContext)(ni.L);return(0,i7.jsx)(nl,{...t,layoutGroup:n,switchLayoutGroup:(0,nt.useContext)(nn),isPresent:e,safeToRemove:i})}let nc={borderRadius:{...no,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:no,borderTopRightRadius:no,borderBottomLeftRadius:no,borderBottomRightRadius:no,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let n=tT.parse(t);if(n.length>5)return t;let r=tT.createTransformer(t),s=+("number"!=typeof n[0]),o=i.x.scale*e.x,a=i.y.scale*e.y;n[0+s]/=o,n[1+s]/=a;let l=tA(o,a,.5);return"number"==typeof n[2+s]&&(n[2+s]/=l),"number"==typeof n[3+s]&&(n[3+s]/=l),r(n)}}};var nh=i(63179);function nd(t){return(0,nh.G)(t)&&"ownerSVGElement"in t}let np=(t,e)=>t.depth-e.depth;class nm{constructor(){this.children=[],this.isDirty=!1}add(t){w(this.children,t),this.isDirty=!0}remove(t){k(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(np),this.isDirty=!1,this.children.forEach(t)}}function nf(t){return C(t)?t.get():t}let ng=["TopLeft","TopRight","BottomLeft","BottomRight"],ny=ng.length,nv=t=>"string"==typeof t?parseFloat(t):t,nb=t=>"number"==typeof t||tu.test(t);function nx(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let nw=nT(0,.5,t8),nk=nT(.5,.95,u);function nT(t,e,i){return n=>n<t?0:n>e?1:i(es(t,e,n))}function nS(t,e){t.min=e.min,t.max=e.max}function nP(t,e){nS(t.x,e.x),nS(t.y,e.y)}function nA(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function nM(t,e,i,n,r){return t-=e,t=n+1/i*(t-n),void 0!==r&&(t=n+1/r*(t-n)),t}function nE(t,e,[i,n,r],s,o){!function(t,e=0,i=1,n=.5,r,s=t,o=t){if(tl.test(e)&&(e=parseFloat(e),e=tA(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=tA(s.min,s.max,n);t===s&&(a-=e),t.min=nM(t.min,e,i,a,r),t.max=nM(t.max,e,i,a,r)}(t,e[i],e[n],e[r],e.scale,s,o)}let nV=["x","scaleX","originX"],nD=["y","scaleY","originY"];function nC(t,e,i,n){nE(t.x,e,nV,i?i.x:void 0,n?n.x:void 0),nE(t.y,e,nD,i?i.y:void 0,n?n.y:void 0)}function nj(t){return 0===t.translate&&1===t.scale}function nL(t){return nj(t.x)&&nj(t.y)}function nR(t,e){return t.min===e.min&&t.max===e.max}function nO(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function nF(t,e){return nO(t.x,e.x)&&nO(t.y,e.y)}function nB(t){return ik(t.x)/ik(t.y)}function nI(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class nz{constructor(){this.members=[]}add(t){w(this.members,t),t.scheduleRender()}remove(t){if(k(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:n}=t.options;!1===n&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let nN={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},nU=["","X","Y","Z"],n$={visibility:"hidden"},nW=0;function nH(t,e,i,n){let{latestValues:r}=e;r[t]&&(i[t]=r[t],e.setStaticValue(t,0),n&&(n[t]=0))}function nX({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:n,resetTransform:r}){return class{constructor(t={},i=e?.()){this.id=nW++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,d.value&&(nN.nodes=nN.calculatedTargetDeltas=nN.calculatedProjections=0),this.nodes.forEach(nG),this.nodes.forEach(n1),this.nodes.forEach(n2),this.nodes.forEach(nK),d.addProjectionMetrics&&d.addProjectionMetrics(nN)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new nm)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new T),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=nd(e)&&!(nd(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:i,layout:n,visualElement:r}=this.options;if(r&&!r.current&&r.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(n||i)&&(this.isLayoutDirty=!0),t){let i,n=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=P.now(),n=({timestamp:r})=>{let s=r-i;s>=250&&(f(n),t(s-e))};return m.setup(n,!0),()=>f(n)}(n,250),nr.hasAnimatedSinceResize&&(nr.hasAnimatedSinceResize=!1,this.nodes.forEach(n0))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&r&&(i||n)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let s=this.options.transition||r.getDefaultTransition()||n7,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=r.getProps(),u=!this.targetLayout||!nF(this.targetLayout,n),c=!e&&i;if(this.options.layoutRoot||this.resumeFrom||c||e&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...l(s,"layout"),onPlay:o,onComplete:a};(r.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,c)}else e||n0(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),f(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(n4),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let n=i.props[R];if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",m,!(t||i))}let{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&t(r)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(n_);return}this.isUpdating||this.nodes.forEach(nQ),this.isUpdating=!1,this.nodes.forEach(nJ),this.nodes.forEach(nY),this.nodes.forEach(nq),this.clearAllSnapshots();let t=P.now();g.delta=B(0,1e3/60,t-g.timestamp),g.timestamp=t,g.isProcessing=!0,y.update.process(g),y.preRender.process(g),y.render.process(g),g.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,i8.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(nZ),this.sharedNodes.forEach(n3)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,m.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){m.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),!this.snapshot||ik(this.snapshot.measuredBox.x)||ik(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=iC(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=n(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!r)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!nL(this.projectionDelta),i=this.getTransformTemplate(),n=i?i(this.latestValues,""):void 0,s=n!==this.prevTransformTemplateValue;t&&this.instance&&(e||iO(this.latestValues)||s)&&(r(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),n=this.removeElementScroll(i);return t&&(n=this.removeTransform(n)),re((e=n).x),re(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return iC();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(rn))){let{scroll:t}=this.root;t&&(iN(e.x,t.offset.x),iN(e.y,t.offset.y))}return e}removeElementScroll(t){let e=iC();if(nP(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let n=this.path[i],{scroll:r,options:s}=n;n!==this.root&&r&&s.layoutScroll&&(r.wasRoot&&nP(e,t),iN(e.x,r.offset.x),iN(e.y,r.offset.y))}return e}applyTransform(t,e=!1){let i=iC();nP(i,t);for(let t=0;t<this.path.length;t++){let n=this.path[t];!e&&n.options.layoutScroll&&n.scroll&&n!==n.root&&i$(i,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),iO(n.latestValues)&&i$(i,n.latestValues)}return iO(this.latestValues)&&i$(i,this.latestValues),i}removeTransform(t){let e=iC();nP(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!iO(i.latestValues))continue;iR(i.latestValues)&&i.updateSnapshot();let n=iC();nP(n,i.measurePageBox()),nC(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,n)}return iO(this.latestValues)&&nC(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==g.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:n,layoutId:r}=this.options;if(this.layout&&(n||r)){if(this.resolvedRelativeTargetAt=g.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iC(),this.relativeTargetOrigin=iC(),iM(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),nP(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=iC(),this.targetWithTransforms=iC()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var s,o,a;this.forceRelativeParentToResolveTarget(),s=this.target,o=this.relativeTarget,a=this.relativeParent.target,iP(s.x,o.x,a.x),iP(s.y,o.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):nP(this.target,this.layout.layoutBox),iz(this.target,this.targetDelta)):nP(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iC(),this.relativeTargetOrigin=iC(),iM(this.relativeTargetOrigin,this.target,t.target),nP(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}d.value&&nN.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||iR(this.parent.latestValues)||iF(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===g.timestamp&&(i=!1),i)return;let{layout:n,layoutId:r}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||r))return;nP(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,o=this.treeScale.y;(function(t,e,i,n=!1){let r,s,o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){s=(r=i[a]).projectionDelta;let{visualElement:o}=r.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(n&&r.options.layoutScroll&&r.scroll&&r!==r.root&&i$(t,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),s&&(e.x*=s.x.scale,e.y*=s.y.scale,iz(t,s)),n&&iO(r.latestValues)&&i$(t,r.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}})(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=iC());let{target:a}=t;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(nA(this.prevProjectionDelta.x,this.projectionDelta.x),nA(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),iS(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===s&&this.treeScale.y===o&&nI(this.projectionDelta.x,this.prevProjectionDelta.x)&&nI(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),d.value&&nN.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=iV(),this.projectionDelta=iV(),this.projectionDeltaWithTransform=iV()}setAnimationOrigin(t,e=!1){let i,n=this.snapshot,r=n?n.latestValues:{},s={...this.latestValues},o=iV();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=iC(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),c=!u||u.members.length<=1,h=!!(l&&!c&&!0===this.options.crossfade&&!this.path.some(n9));this.animationProgress=0,this.mixTargetDelta=e=>{let n=e/1e3;if(n5(o.x,t.x,n),n5(o.y,t.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,d,p,m,f,g;if(iM(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=a,g=n,n6(p.x,m.x,f.x,g),n6(p.y,m.y,f.y,g),i&&(u=this.relativeTarget,d=i,nR(u.x,d.x)&&nR(u.y,d.y)))this.isProjectionDirty=!1;i||(i=iC()),nP(i,this.relativeTarget)}l&&(this.animationValues=s,function(t,e,i,n,r,s){r?(t.opacity=tA(0,i.opacity??1,nw(n)),t.opacityExit=tA(e.opacity??1,0,nk(n))):s&&(t.opacity=tA(e.opacity??1,i.opacity??1,n));for(let r=0;r<ny;r++){let s=`border${ng[r]}Radius`,o=nx(e,s),a=nx(i,s);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||nb(o)===nb(a)?(t[s]=Math.max(tA(nv(o),nv(a),n),0),(tl.test(a)||tl.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||i.rotate)&&(t.rotate=tA(e.rotate||0,i.rotate||0,n))}(s,r,this.latestValues,n,h,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(f(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=m.update(()=>{nr.hasAnimatedSinceResize=!0,N.layout++,this.motionValue||(this.motionValue=V(0)),this.currentAnimation=function(t,e,i){let n=C(t)?t:V(t);return n.start(e6("",n,e,i)),n.animation}(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{N.layout--},onComplete:()=>{N.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:n,latestValues:r}=t;if(e&&i&&n){if(this!==t&&this.layout&&n&&ri(this.options.animationType,this.layout.layoutBox,n.layoutBox)){i=this.target||iC();let e=ik(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let n=ik(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+n}nP(e,i),i$(e,r),iS(this.projectionDeltaWithTransform,this.layoutCorrected,e,r)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new nz),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let n=this.getStack();n&&n.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let n={};i.z&&nH("z",t,n,this.animationValues);for(let e=0;e<nU.length;e++)nH(`rotate${nU[e]}`,t,n,this.animationValues),nH(`skew${nU[e]}`,t,n,this.animationValues);for(let e in t.render(),n)t.setStaticValue(e,n[e]),this.animationValues&&(this.animationValues[e]=n[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return n$;let e={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=nf(t?.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none",e;let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=nf(t?.pointerEvents)||""),this.hasProjected&&!iO(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1),e}let r=n.animationValues||n.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,i){let n="",r=t.x.translate/e.x,s=t.y.translate/e.y,o=i?.z||0;if((r||s||o)&&(n=`translate3d(${r}px, ${s}px, ${o}px) `),(1!==e.x||1!==e.y)&&(n+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:r,rotateY:s,skewX:o,skewY:a}=i;t&&(n=`perspective(${t}px) ${n}`),e&&(n+=`rotate(${e}deg) `),r&&(n+=`rotateX(${r}deg) `),s&&(n+=`rotateY(${s}deg) `),o&&(n+=`skewX(${o}deg) `),a&&(n+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(n+=`scale(${a}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,r),i&&(e.transform=i(r,e.transform));let{x:s,y:o}=this.projectionDelta;for(let t in e.transformOrigin=`${100*s.origin}% ${100*o.origin}% 0`,n.animationValues?e.opacity=n===this?r.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:r.opacityExit:e.opacity=n===this?void 0!==r.opacity?r.opacity:"":void 0!==r.opacityExit?r.opacityExit:0,na){if(void 0===r[t])continue;let{correct:i,applyTo:s,isCSSVariable:o}=na[t],a="none"===e.transform?r[t]:i(r[t],n);if(s){let t=s.length;for(let i=0;i<t;i++)e[s[i]]=a}else o?this.options.visualElement.renderState.vars[t]=a:e[t]=a}return this.options.layoutId&&(e.pointerEvents=n===this?nf(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(n_),this.root.sharedNodes.clear()}}}function nY(t){t.updateLayout()}function nq(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:n}=t.layout,{animationType:r}=t.options,s=e.source!==t.layout.source;"size"===r?ij(t=>{let n=s?e.measuredBox[t]:e.layoutBox[t],r=ik(n);n.min=i[t].min,n.max=n.min+r}):ri(r,e.layoutBox,i)&&ij(n=>{let r=s?e.measuredBox[n]:e.layoutBox[n],o=ik(i[n]);r.max=r.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[n].max=t.relativeTarget[n].min+o)});let o=iV();iS(o,i,e.layoutBox);let a=iV();s?iS(a,t.applyTransform(n,!0),e.measuredBox):iS(a,i,e.layoutBox);let l=!nL(o),u=!1;if(!t.resumeFrom){let n=t.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:r,layout:s}=n;if(r&&s){let o=iC();iM(o,e.layoutBox,r.layoutBox);let a=iC();iM(a,i,s.layoutBox),nF(o,a)||(u=!0),n.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=n)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function nG(t){d.value&&nN.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function nK(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function nZ(t){t.clearSnapshot()}function n_(t){t.clearMeasurements()}function nQ(t){t.isLayoutDirty=!1}function nJ(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function n0(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function n1(t){t.resolveTargetDelta()}function n2(t){t.calcProjection()}function n4(t){t.resetSkewAndRotation()}function n3(t){t.removeLeadSnapshot()}function n5(t,e,i){t.translate=tA(e.translate,0,i),t.scale=tA(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function n6(t,e,i,n){t.min=tA(e.min,i.min,n),t.max=tA(e.max,i.max,n)}function n9(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let n7={duration:.45,ease:[.4,0,.1,1]},n8=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),rt=n8("applewebkit/")&&!n8("chrome/")?Math.round:u;function re(t){t.min=rt(t.min),t.max=rt(t.max)}function ri(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(nB(e)-nB(i)))}function rn(t){return t!==t.root&&t.scroll?.wasRoot}let rr=nX({attachResizeListener:(t,e)=>ig(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),rs={current:void 0},ro=nX({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!rs.current){let t=new rr({});t.mount(window),t.setOptions({layoutScroll:!0}),rs.current=t}return rs.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function ra(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,i=(void 0)??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),n=new AbortController;return[i,{passive:!0,...e,signal:n.signal},()=>n.abort()]}function rl(t){return!("touch"===t.pointerType||im.x||im.y)}function ru(t,e,i){let{props:n}=t;t.animationState&&n.whileHover&&t.animationState.setActive("whileHover","Start"===i);let r=n["onHover"+i];r&&m.postRender(()=>r(e,iv(e)))}class rc extends ic{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,r,s]=ra(t,i),o=t=>{if(!rl(t))return;let{target:i}=t,n=e(i,t);if("function"!=typeof n||!i)return;let s=t=>{rl(t)&&(n(t),i.removeEventListener("pointerleave",s))};i.addEventListener("pointerleave",s,r)};return n.forEach(t=>{t.addEventListener("pointerenter",o,r)}),s}(t,(t,e)=>(ru(this.node,e,"Start"),t=>ru(this.node,t,"End"))))}unmount(){}}class rh extends ic{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=F(ig(this.node.current,"focus",()=>this.onFocus()),ig(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let rd=(t,e)=>!!e&&(t===e||rd(t,e.parentElement)),rp=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),rm=new WeakSet;function rf(t){return e=>{"Enter"===e.key&&t(e)}}function rg(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let ry=(t,e)=>{let i=t.currentTarget;if(!i)return;let n=rf(()=>{if(rm.has(i))return;rg(i,"down");let t=rf(()=>{rg(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>rg(i,"cancel"),e)});i.addEventListener("keydown",n,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",n),e)};function rv(t){return iy(t)&&!(im.x||im.y)}function rb(t,e,i){let{props:n}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&n.whileTap&&t.animationState.setActive("whileTap","Start"===i);let r=n["onTap"+("End"===i?"":i)];r&&m.postRender(()=>r(e,iv(e)))}class rx extends ic{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,r,s]=ra(t,i),o=t=>{let n=t.currentTarget;if(!rv(t))return;rm.add(n);let s=e(n,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),rm.has(n)&&rm.delete(n),rv(t)&&"function"==typeof s&&s(t,{success:e})},a=t=>{o(t,n===window||n===document||i.useGlobalTarget||rd(n,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,r),window.addEventListener("pointercancel",l,r)};return n.forEach(t=>{if((i.useGlobalTarget?window:t).addEventListener("pointerdown",o,r),(0,eZ.s)(t))t.addEventListener("focus",t=>ry(t,r)),!rp.has(t.tagName)&&-1===t.tabIndex&&!t.hasAttribute("tabindex")&&(t.tabIndex=0)}),s}(t,(t,e)=>(rb(this.node,e,"Start"),(t,{success:e})=>rb(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let rw=new WeakMap,rk=new WeakMap,rT=t=>{let e=rw.get(t.target);e&&e(t)},rS=t=>{t.forEach(rT)},rP={some:0,all:1};class rA extends ic{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:n="some",once:r}=t,s={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof n?n:rP[n]};return function(t,e,i){let n=function({root:t,...e}){let i=t||document;rk.has(i)||rk.set(i,{});let n=rk.get(i),r=JSON.stringify(e);return n[r]||(n[r]=new IntersectionObserver(rS,{root:t,...e})),n[r]}(e);return rw.set(t,i),n.observe(t),()=>{rw.delete(t),n.unobserve(t)}}(this.node.current,s,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,r&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:n}=this.node.getProps(),s=e?i:n;s&&s(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let rM=(0,nt.createContext)({strict:!1});var rE=i(16519);let rV=(0,nt.createContext)({});function rD(t){return r(t.animate)||ir.some(e=>ie(t[e]))}function rC(t){return!!(rD(t)||t.variants)}function rj(t){return Array.isArray(t)?t.join(" "):t}var rL=i(23235);let rR={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},rO={};for(let t in rR)rO[t]={isEnabled:e=>rR[t].some(t=>!!e[t])};let rF=Symbol.for("motionComponentSymbol");var rB=i(60462),rI=i(2893);function rz(t,{layout:e,layoutId:i}){return b.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!na[t]||"opacity"===t)}let rN=(t,e)=>e&&"number"==typeof t?e.transform(t):t,rU={...G,transform:Math.round},r$={borderWidth:tu,borderTopWidth:tu,borderRightWidth:tu,borderBottomWidth:tu,borderLeftWidth:tu,borderRadius:tu,radius:tu,borderTopLeftRadius:tu,borderTopRightRadius:tu,borderBottomRightRadius:tu,borderBottomLeftRadius:tu,width:tu,maxWidth:tu,height:tu,maxHeight:tu,top:tu,right:tu,bottom:tu,left:tu,padding:tu,paddingTop:tu,paddingRight:tu,paddingBottom:tu,paddingLeft:tu,margin:tu,marginTop:tu,marginRight:tu,marginBottom:tu,marginLeft:tu,backgroundPositionX:tu,backgroundPositionY:tu,rotate:ta,rotateX:ta,rotateY:ta,rotateZ:ta,scale:Z,scaleX:Z,scaleY:Z,scaleZ:Z,skew:ta,skewX:ta,skewY:ta,distance:tu,translateX:tu,translateY:tu,translateZ:tu,x:tu,y:tu,z:tu,perspective:tu,transformPerspective:tu,opacity:K,originX:td,originY:td,originZ:tu,zIndex:rU,fillOpacity:K,strokeOpacity:K,numOctaves:rU},rW={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},rH=v.length;function rX(t,e,i){let{style:n,vars:r,transformOrigin:s}=t,o=!1,a=!1;for(let t in e){let i=e[t];if(b.has(t)){o=!0;continue}if(H(t)){r[t]=i;continue}{let e=rN(i,r$[t]);t.startsWith("origin")?(a=!0,s[t]=e):n[t]=e}}if(!e.transform&&(o||i?n.transform=function(t,e,i){let n="",r=!0;for(let s=0;s<rH;s++){let o=v[s],a=t[o];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!o.startsWith("scale"):0===parseFloat(a))||i){let t=rN(a,r$[o]);if(!l){r=!1;let e=rW[o]||o;n+=`${e}(${t}) `}i&&(e[o]=t)}}return n=n.trim(),i?n=i(e,r?"":n):r&&(n="none"),n}(e,t.transform,i):n.transform&&(n.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:i=0}=s;n.transformOrigin=`${t} ${e} ${i}`}}let rY=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function rq(t,e,i){for(let n in e)C(e[n])||rz(n,i)||(t[n]=e[n])}let rG={offset:"stroke-dashoffset",array:"stroke-dasharray"},rK={offset:"strokeDashoffset",array:"strokeDasharray"};function rZ(t,{attrX:e,attrY:i,attrScale:n,pathLength:r,pathSpacing:s=1,pathOffset:o=0,...a},l,u,c){if(rX(t,a,u),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:h,style:d}=t;h.transform&&(d.transform=h.transform,delete h.transform),(d.transform||h.transformOrigin)&&(d.transformOrigin=h.transformOrigin??"50% 50%",delete h.transformOrigin),d.transform&&(d.transformBox=c?.transformBox??"fill-box",delete h.transformBox),void 0!==e&&(h.x=e),void 0!==i&&(h.y=i),void 0!==n&&(h.scale=n),void 0!==r&&function(t,e,i=1,n=0,r=!0){t.pathLength=1;let s=r?rG:rK;t[s.offset]=tu.transform(-n);let o=tu.transform(e),a=tu.transform(i);t[s.array]=`${o} ${a}`}(h,r,s,o,!1)}let r_=()=>({...rY(),attrs:{}}),rQ=t=>"string"==typeof t&&"svg"===t.toLowerCase(),rJ=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function r0(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||rJ.has(t)}let r1=t=>!r0(t);try{!function(t){t&&(r1=e=>e.startsWith("on")?!r0(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}let r2=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function r4(t){if("string"!=typeof t||t.includes("-"));else if(r2.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var r3=i(7014);let r5=t=>(e,i)=>{let n=(0,nt.useContext)(rV),s=(0,nt.useContext)(rB.t),a=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,n,s){return{latestValues:function(t,e,i,n){let s={},a=n(t,{});for(let t in a)s[t]=nf(a[t]);let{initial:l,animate:u}=t,c=rD(t),h=rC(t);e&&h&&!c&&!1!==t.inherit&&(void 0===l&&(l=e.initial),void 0===u&&(u=e.animate));let d=!!i&&!1===i.initial,p=(d=d||!1===l)?u:l;if(p&&"boolean"!=typeof p&&!r(p)){let e=Array.isArray(p)?p:[p];for(let i=0;i<e.length;i++){let n=o(t,e[i]);if(n){let{transitionEnd:t,transition:e,...i}=n;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=d?e.length-1:0;e=e[t]}null!==e&&(s[t]=e)}for(let e in t)s[e]=t[e]}}}return s}(i,n,s,t),renderState:e()}})(t,e,n,s);return i?a():(0,r3.M)(a)};function r6(t,e,i){let{style:n}=t,r={};for(let s in n)(C(n[s])||e.style&&C(e.style[s])||rz(s,t)||i?.getValue(s)?.liveStyle!==void 0)&&(r[s]=n[s]);return r}let r9={useVisualState:r5({scrapeMotionValuesFromProps:r6,createRenderState:rY})};function r7(t,e,i){let n=r6(t,e,i);for(let i in t)(C(t[i])||C(e[i]))&&(n[-1!==v.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return n}let r8={useVisualState:r5({scrapeMotionValuesFromProps:r7,createRenderState:r_})},st=t=>e=>e.test(t),se=[G,tu,tl,ta,th,tc,{test:t=>"auto"===t,parse:t=>t}],si=t=>se.find(st(t)),sn=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),sr=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,ss=t=>/^0[^.\s]+$/u.test(t),so=new Set(["brightness","contrast","saturate","opacity"]);function sa(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[n]=i.match(Q)||[];if(!n)return t;let r=i.replace(n,""),s=+!!so.has(e);return n!==i&&(s*=100),e+"("+s+r+")"}let sl=/\b([a-z-]*)\(.*?\)/gu,su={...tT,getAnimatableNone:t=>{let e=t.match(sl);return e?e.map(sa).join(" "):t}},sc={...r$,color:tm,backgroundColor:tm,outlineColor:tm,fill:tm,stroke:tm,borderColor:tm,borderTopColor:tm,borderRightColor:tm,borderBottomColor:tm,borderLeftColor:tm,filter:su,WebkitFilter:su},sh=t=>sc[t];function sd(t,e){let i=sh(t);return i!==su&&(i=tT),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let sp=new Set(["auto","none","0"]);class sm extends eO{constructor(t,e,i,n,r){super(t,e,i,n,r,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let n=t[i];if("string"==typeof n&&Y(n=n.trim())){let r=function t(e,i,n=1){$(n<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[r,s]=function(t){let e=sr.exec(t);if(!e)return[,];let[,i,n,r]=e;return[`--${i??n}`,r]}(e);if(!r)return;let o=window.getComputedStyle(i).getPropertyValue(r);if(o){let t=o.trim();return sn(t)?parseFloat(t):t}return Y(s)?t(s,i,n+1):s}(n,e.current);void 0!==r&&(t[i]=r),i===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!x.has(i)||2!==t.length)return;let[n,r]=t,s=si(n),o=si(r);if(s!==o)if(eP(s)&&eP(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else eE[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var n;(null===t[e]||("number"==typeof(n=t[e])?0===n:null===n||"none"===n||"0"===n||ss(n)))&&i.push(e)}if(i.length){let n,r=0;for(;r<t.length&&!n;){let e=t[r];"string"==typeof e&&!sp.has(e)&&tb(e).values.length&&(n=t[r]),r++}if(n&&e)for(let r of i)t[r]=sd(e,n)}}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=eE[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let n=e[e.length-1];void 0!==n&&t.getValue(i,n).jump(n,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let n=t.getValue(e);n&&n.jump(this.measuredOrigin,!1);let r=i.length-1,s=i[r];i[r]=eE[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let sf=[...se,tm,tT],sg=t=>sf.find(st(t)),sy={current:null},sv={current:!1},sb=new WeakMap,sx=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class sw{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:n,blockInitialAnimation:r,visualState:s},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=eO,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=P.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,m.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=s;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=n,this.options=o,this.blockInitialAnimation=!!r,this.isControllingVariants=rD(e),this.isVariantNode=rC(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:u,...c}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in c){let e=c[t];void 0!==a[t]&&C(e)&&e.set(a[t],!1)}}mount(t){if(this.current=t,sb.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),!sv.current&&(sv.current=!0,rL.B))if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>sy.current=t.matches;t.addListener(e),e()}else sy.current=!1;this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||sy.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),f(this.notifyUpdate),f(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let n=b.has(t);n&&this.onBindTransform&&this.onBindTransform();let r=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&m.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),s=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{r(),s(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in rO){let e=rO[t];if(!e)continue;let{isEnabled:i,Feature:n}=e;if(!this.features[t]&&n&&i(this.props)&&(this.features[t]=new n(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):iC()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<sx.length;e++){let i=sx[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let n=t["on"+i];n&&(this.propEventSubscriptions[i]=this.on(i,n))}this.prevMotionValues=function(t,e,i){for(let n in e){let r=e[n],s=i[n];if(C(r))t.addValue(n,r);else if(C(s))t.addValue(n,V(r,{owner:t}));else if(s!==r)if(t.hasValue(n)){let e=t.getValue(n);!0===e.liveStyle?e.jump(r):e.hasAnimated||e.set(r)}else{let e=t.getStaticValue(n);t.addValue(n,V(void 0!==e?e:r,{owner:t}))}}for(let n in i)void 0===e[n]&&t.removeValue(n);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=V(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&(sn(i)||ss(i))?i=parseFloat(i):!sg(i)&&tT.test(e)&&(i=sd(t,e)),this.setBaseTarget(t,C(i)?i.get():i)),C(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let n=o(this.props,i,this.presenceContext?.custom);n&&(e=n[t])}if(i&&void 0!==e)return e;let n=this.getBaseTargetFromProps(this.props,t);return void 0===n||C(n)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new T),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class sk extends sw{constructor(){super(...arguments),this.KeyframeResolver=sm}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;C(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function sT(t,{style:e,vars:i},n,r){for(let s in Object.assign(t.style,e,r&&r.getProjectionStyles(n)),i)t.style.setProperty(s,i[s])}class sS extends sk{constructor(){super(...arguments),this.type="html",this.renderInstance=sT}readValueFromInstance(t,e){if(b.has(e))return this.projection?.isProjecting?ew(e):eT(t,e);{let i=window.getComputedStyle(t),n=(H(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(t,{transformPagePoint:e}){return iW(t,e)}build(t,e,i){rX(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return r6(t,e,i)}}let sP=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class sA extends sk{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=iC}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(b.has(e)){let t=sh(e);return t&&t.default||0}return e=sP.has(e)?e:L(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return r7(t,e,i)}build(t,e,i){rZ(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,n){for(let i in sT(t,e,void 0,n),e.attrs)t.setAttribute(sP.has(i)?i:L(i),e.attrs[i])}mount(t){this.isSVGTag=rQ(t.tagName),super.mount(t)}}let sM=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,n)=>"create"===n?t:(e.has(n)||e.set(n,t(n)),e.get(n))})}((eG={animation:{Feature:ih},exit:{Feature:ip},inView:{Feature:rA},tap:{Feature:rx},focus:{Feature:rh},hover:{Feature:rc},pan:{Feature:i9},drag:{Feature:i5,ProjectionNode:ro,MeasureLayout:nu},layout:{ProjectionNode:ro,MeasureLayout:nu}},eK=(t,e)=>r4(t)?new sA(e):new sS(e,{allowProjection:t!==nt.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function({preloadedFeatures:t,createVisualElement:e,useRender:i,useVisualState:n,Component:r}){function s(t,s){var o,a,l;let u,c={...(0,nt.useContext)(rE.Q),...t,layoutId:function({layoutId:t}){let e=(0,nt.useContext)(ni.L).id;return e&&void 0!==t?e+"-"+t:t}(t)},{isStatic:h}=c,d=function(t){let{initial:e,animate:i}=function(t,e){if(rD(t)){let{initial:e,animate:i}=t;return{initial:!1===e||ie(e)?e:void 0,animate:ie(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,nt.useContext)(rV));return(0,nt.useMemo)(()=>({initial:e,animate:i}),[rj(e),rj(i)])}(t),p=n(t,h);if(!h&&rL.B){a=0,l=0,(0,nt.useContext)(rM).strict;let t=function(t){let{drag:e,layout:i}=rO;if(!e&&!i)return{};let n={...e,...i};return{MeasureLayout:e?.isEnabled(t)||i?.isEnabled(t)?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(c);u=t.MeasureLayout,d.visualElement=function(t,e,i,n,r){let{visualElement:s}=(0,nt.useContext)(rV),o=(0,nt.useContext)(rM),a=(0,nt.useContext)(rB.t),l=(0,nt.useContext)(rE.Q).reducedMotion,u=(0,nt.useRef)(null);n=n||o.renderer,!u.current&&n&&(u.current=n(t,{visualState:e,parent:s,props:i,presenceContext:a,blockInitialAnimation:!!a&&!1===a.initial,reducedMotionConfig:l}));let c=u.current,h=(0,nt.useContext)(nn);c&&!c.projection&&r&&("html"===c.type||"svg"===c.type)&&function(t,e,i,n){let{layoutId:r,layout:s,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:c}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:r,layout:s,alwaysMeasureLayout:!!o||a&&iX(a),visualElement:t,animationType:"string"==typeof s?s:"both",initialPromotionConfig:n,crossfade:c,layoutScroll:l,layoutRoot:u})}(u.current,i,r,h);let d=(0,nt.useRef)(!1);(0,nt.useInsertionEffect)(()=>{c&&d.current&&c.update(i,a)});let p=i[R],m=(0,nt.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return(0,rI.E)(()=>{c&&(d.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),i8.render(c.render),m.current&&c.animationState&&c.animationState.animateChanges())}),(0,nt.useEffect)(()=>{c&&(!m.current&&c.animationState&&c.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(p)}),m.current=!1))}),c}(r,p,c,e,t.ProjectionNode)}return(0,i7.jsxs)(rV.Provider,{value:d,children:[u&&d.visualElement?(0,i7.jsx)(u,{visualElement:d.visualElement,...c}):null,i(r,t,(o=d.visualElement,(0,nt.useCallback)(t=>{t&&p.onMount&&p.onMount(t),o&&(t?o.mount(t):o.unmount()),s&&("function"==typeof s?s(t):iX(s)&&(s.current=t))},[o])),p,h,d.visualElement)]})}t&&function(t){for(let e in t)rO[e]={...rO[e],...t[e]}}(t),s.displayName=`motion.${"string"==typeof r?r:`create(${r.displayName??r.name??""})`}`;let o=(0,nt.forwardRef)(s);return o[rF]=r,o}({...r4(t)?r8:r9,preloadedFeatures:eG,useRender:function(t=!1){return(e,i,n,{latestValues:r},s)=>{let o=(r4(e)?function(t,e,i,n){let r=(0,nt.useMemo)(()=>{let i=r_();return rZ(i,e,rQ(n),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};rq(e,t.style,t),r.style={...e,...r.style}}return r}:function(t,e){let i={},n=function(t,e){let i=t.style||{},n={};return rq(n,i,t),Object.assign(n,function({transformTemplate:t},e){return(0,nt.useMemo)(()=>{let i=rY();return rX(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),n}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=n,i})(i,r,s,e),a=function(t,e,i){let n={};for(let r in t)("values"!==r||"object"!=typeof t.values)&&(r1(r)||!0===i&&r0(r)||!e&&!r0(r)||t.draggable&&r.startsWith("onDrag"))&&(n[r]=t[r]);return n}(i,"string"==typeof e,t),l=e!==nt.Fragment?{...a,...o,ref:n}:{},{children:u}=i,c=(0,nt.useMemo)(()=>C(u)?u.get():u,[u]);return(0,nt.createElement)(e,{...l,children:c})}}(e),createVisualElement:eK,Component:t})}))},16519:(t,e,i)=>{i.d(e,{Q:()=>n});let n=(0,i(44508).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},23235:(t,e,i)=>{i.d(e,{B:()=>n});let n="undefined"!=typeof window},26199:(t,e,i)=>{i.d(e,{F:()=>o});var n=i(71537);let r=t=>"boolean"==typeof t?`${t}`:0===t?"0":t,s=n.$,o=(t,e)=>i=>{var n;if((null==e?void 0:e.variants)==null)return s(t,null==i?void 0:i.class,null==i?void 0:i.className);let{variants:o,defaultVariants:a}=e,l=Object.keys(o).map(t=>{let e=null==i?void 0:i[t],n=null==a?void 0:a[t];if(null===e)return null;let s=r(e)||r(n);return o[t][s]}),u=i&&Object.entries(i).reduce((t,e)=>{let[i,n]=e;return void 0===n||(t[i]=n),t},{});return s(t,l,null==e?void 0:null===(n=e.compoundVariants)||void 0===n?void 0:n.reduce((t,e)=>{let{class:i,className:n,...r}=e;return Object.entries(r).every(t=>{let[e,i]=t;return Array.isArray(i)?i.includes({...a,...u}[e]):({...a,...u})[e]===i})?[...t,i,n]:t},[]),null==i?void 0:i.class,null==i?void 0:i.className)}},30917:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(72364).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},31933:(t,e,i)=>{i.d(e,{xQ:()=>s});var n=i(44508),r=i(60462);function s(t=!0){let e=(0,n.useContext)(r.t);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:o,register:a}=e,l=(0,n.useId)();(0,n.useEffect)(()=>{if(t)return a(l)},[t]);let u=(0,n.useCallback)(()=>t&&o&&o(l),[l,o,t]);return!i&&o?[!1,u]:[!0]}},35783:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(72364).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},37739:(t,e,i)=>{i.d(e,{FSj:()=>s,KuA:()=>r,f35:()=>o,iuJ:()=>u,xdT:()=>c,y8Q:()=>l,yRn:()=>a});var n=i(44866);function r(t){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 576 512"},child:[{tag:"path",attr:{d:"M208 0c-29.9 0-54.7 20.5-61.8 48.2-.8 0-1.4-.2-2.2-.2-35.3 0-64 28.7-64 64 0 4.8.6 9.5 1.7 14C52.5 138 32 166.6 32 200c0 12.6 3.2 24.3 8.3 34.9C16.3 248.7 0 274.3 0 304c0 33.3 20.4 61.9 49.4 73.9-.9 4.6-1.4 9.3-1.4 14.1 0 39.8 32.2 72 72 72 4.1 0 8.1-.5 12-1.2 9.6 28.5 36.2 49.2 68 49.2 39.8 0 72-32.2 72-72V64c0-35.3-28.7-64-64-64zm368 304c0-29.7-16.3-55.3-40.3-69.1 5.2-10.6 8.3-22.3 8.3-34.9 0-33.4-20.5-62-49.7-74 1-4.5 1.7-9.2 1.7-14 0-35.3-28.7-64-64-64-.8 0-1.5.2-2.2.2C422.7 20.5 397.9 0 368 0c-35.3 0-64 28.6-64 64v376c0 39.8 32.2 72 72 72 31.8 0 58.4-20.7 68-49.2 3.9.7 7.9 1.2 12 1.2 39.8 0 72-32.2 72-72 0-4.8-.5-9.5-1.4-14.1 29-12 49.4-40.6 49.4-73.9z"},child:[]}]})(t)}function s(t){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 640 512"},child:[{tag:"path",attr:{d:"M278.9 511.5l-61-17.7c-6.4-1.8-10-8.5-8.2-14.9L346.2 8.7c1.8-6.4 8.5-10 14.9-8.2l61 17.7c6.4 1.8 10 8.5 8.2 14.9L293.8 503.3c-1.9 6.4-8.5 10.1-14.9 8.2zm-114-112.2l43.5-46.4c4.6-4.9 4.3-12.7-.8-17.2L117 256l90.6-79.7c5.1-4.5 5.5-12.3.8-17.2l-43.5-46.4c-4.5-4.8-12.1-5.1-17-.5L3.8 247.2c-5.1 4.7-5.1 12.8 0 17.5l144.1 135.1c4.9 4.6 12.5 4.4 17-.5zm327.2.6l144.1-135.1c5.1-4.7 5.1-12.8 0-17.5L492.1 112.1c-4.8-4.5-12.4-4.3-17 .5L431.6 159c-4.6 4.9-4.3 12.7.8 17.2L523 256l-90.6 79.7c-5.1 4.5-5.5 12.3-.8 17.2l43.5 46.4c4.5 4.9 12.1 5.1 17 .6z"},child:[]}]})(t)}function o(t){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 496 512"},child:[{tag:"path",attr:{d:"M336.5 160C322 70.7 287.8 8 248 8s-74 62.7-88.5 152h177zM152 256c0 22.2 1.2 43.5 3.3 64h185.3c2.1-20.5 3.3-41.8 3.3-64s-1.2-43.5-3.3-64H155.3c-2.1 20.5-3.3 41.8-3.3 64zm324.7-96c-28.6-67.9-86.5-120.4-158-141.6 24.4 33.8 41.2 84.7 50 141.6h108zM177.2 18.4C105.8 39.6 47.8 92.1 19.3 160h108c8.7-56.9 25.5-107.8 49.9-141.6zM487.4 192H372.7c2.1 21 3.3 42.5 3.3 64s-1.2 43-3.3 64h114.6c5.5-20.5 8.6-41.8 8.6-64s-3.1-43.5-8.5-64zM120 256c0-21.5 1.2-43 3.3-64H8.6C3.2 212.5 0 233.8 0 256s3.2 43.5 8.6 64h114.6c-2-21-3.2-42.5-3.2-64zm39.5 96c14.5 89.3 48.7 152 88.5 152s74-62.7 88.5-152h-177zm159.3 141.6c71.4-21.2 129.4-73.7 158-141.6h-108c-8.8 56.9-25.6 107.8-50 141.6zM19.3 352c28.6 67.9 86.5 120.4 158 141.6-24.4-33.8-41.2-84.7-50-141.6h-108z"},child:[]}]})(t)}function a(t){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 576 512"},child:[{tag:"path",attr:{d:"M519.442 288.651c-41.519 0-59.5 31.593-82.058 31.593C377.409 320.244 432 144 432 144s-196.288 80-196.288-3.297c0-35.827 36.288-46.25 36.288-85.985C272 19.216 243.885 0 210.539 0c-34.654 0-66.366 18.891-66.366 56.346 0 41.364 31.711 59.277 31.711 81.75C175.885 207.719 0 166.758 0 166.758v333.237s178.635 41.047 178.635-28.662c0-22.473-40-40.107-40-81.471 0-37.456 29.25-56.346 63.577-56.346 33.673 0 61.788 19.216 61.788 54.717 0 39.735-36.288 50.158-36.288 85.985 0 60.803 129.675 25.73 181.23 25.73 0 0-34.725-120.101 25.827-120.101 35.962 0 46.423 36.152 86.308 36.152C556.712 416 576 387.99 576 354.443c0-34.199-18.962-65.792-56.558-65.792z"},child:[]}]})(t)}function l(t){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 640 512"},child:[{tag:"path",attr:{d:"M32,224H64V416H32A31.96166,31.96166,0,0,1,0,384V256A31.96166,31.96166,0,0,1,32,224Zm512-48V448a64.06328,64.06328,0,0,1-64,64H160a64.06328,64.06328,0,0,1-64-64V176a79.974,79.974,0,0,1,80-80H288V32a32,32,0,0,1,64,0V96H464A79.974,79.974,0,0,1,544,176ZM264,256a40,40,0,1,0-40,40A39.997,39.997,0,0,0,264,256Zm-8,128H192v32h64Zm96,0H288v32h64ZM456,256a40,40,0,1,0-40,40A39.997,39.997,0,0,0,456,256Zm-8,128H384v32h64ZM640,256V384a31.96166,31.96166,0,0,1-32,32H576V224h32A31.96166,31.96166,0,0,1,640,256Z"},child:[]}]})(t)}function u(t){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 640 512"},child:[{tag:"path",attr:{d:"M257.981 272.971L63.638 467.314c-9.373 9.373-24.569 9.373-33.941 0L7.029 444.647c-9.357-9.357-9.375-24.522-.04-33.901L161.011 256 6.99 101.255c-9.335-9.379-9.317-24.544.04-33.901l22.667-22.667c9.373-9.373 24.569-9.373 33.941 0L257.981 239.03c9.373 9.372 9.373 24.568 0 33.941zM640 456v-32c0-13.255-10.745-24-24-24H312c-13.255 0-24 10.745-24 24v32c0 13.255 10.745 24 24 24h304c13.255 0 24-10.745 24-24z"},child:[]}]})(t)}function c(t){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M501.1 395.7L384 278.6c-23.1-23.1-57.6-27.6-85.4-13.9L192 158.1V96L64 0 0 64l96 128h62.1l106.6 106.6c-13.6 27.8-9.2 62.3 13.9 85.4l117.1 117.1c14.6 14.6 38.2 14.6 52.7 0l52.7-52.7c14.5-14.6 14.5-38.2 0-52.7zM331.7 225c28.3 0 54.9 11 74.9 31l19.4 19.4c15.8-6.9 30.8-16.5 43.8-29.5 37.1-37.1 49.7-89.3 37.9-136.7-2.2-9-13.5-12.1-20.1-5.5l-74.4 74.4-67.9-11.3L334 98.9l74.4-74.4c6.6-6.6 3.4-17.9-5.7-20.2-47.4-11.7-99.6.9-136.6 37.9-28.5 28.5-41.9 66.1-41.2 103.6l82.1 82.1c8.1-1.9 16.5-2.9 24.7-2.9zm-103.9 82l-56.7-56.7L18.7 402.8c-25 25-25 65.5 0 90.5s65.5 25 90.5 0l123.6-123.6c-7.6-19.9-9.9-41.6-5-62.7zM64 472c-13.2 0-24-10.8-24-24 0-13.3 10.7-24 24-24s24 10.7 24 24c0 13.2-10.7 24-24 24z"},child:[]}]})(t)}},44490:(t,e,i)=>{i.d(e,{L:()=>n});let n=(0,i(44508).createContext)({})},50389:(t,e,i)=>{i.d(e,{DX:()=>o});var n=i(4446);function r(t,e){if("function"==typeof t)return t(e);null!=t&&(t.current=e)}var s=i(90811),o=function(t){let e=function(t){let e=n.forwardRef((t,e)=>{let{children:i,...s}=t;if(n.isValidElement(i)){var o;let t,a,l=(o=i,(a=(t=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?o.ref:(a=(t=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?o.props.ref:o.props.ref||o.ref),u=function(t,e){let i={...e};for(let n in e){let r=t[n],s=e[n];/^on[A-Z]/.test(n)?r&&s?i[n]=(...t)=>{let e=s(...t);return r(...t),e}:r&&(i[n]=r):"style"===n?i[n]={...r,...s}:"className"===n&&(i[n]=[r,s].filter(Boolean).join(" "))}return{...t,...i}}(s,i.props);return i.type!==n.Fragment&&(u.ref=e?function(...t){return e=>{let i=!1,n=t.map(t=>{let n=r(t,e);return i||"function"!=typeof n||(i=!0),n});if(i)return()=>{for(let e=0;e<n.length;e++){let i=n[e];"function"==typeof i?i():r(t[e],null)}}}}(e,l):l),n.cloneElement(i,u)}return n.Children.count(i)>1?n.Children.only(null):null});return e.displayName=`${t}.SlotClone`,e}(t),i=n.forwardRef((t,i)=>{let{children:r,...o}=t,a=n.Children.toArray(r),u=a.find(l);if(u){let t=u.props.children,r=a.map(e=>e!==u?e:n.Children.count(t)>1?n.Children.only(null):n.isValidElement(t)?t.props.children:null);return(0,s.jsx)(e,{...o,ref:i,children:n.isValidElement(t)?n.cloneElement(t,void 0,r):null})}return(0,s.jsx)(e,{...o,ref:i,children:r})});return i.displayName=`${t}.Slot`,i}("Slot"),a=Symbol("radix.slottable");function l(t){return n.isValidElement(t)&&"function"==typeof t.type&&"__radixId"in t.type&&t.type.__radixId===a}},60462:(t,e,i)=>{i.d(e,{t:()=>n});let n=(0,i(44508).createContext)(null)},63179:(t,e,i)=>{i.d(e,{G:()=>n});function n(t){return"object"==typeof t&&null!==t}},67005:(t,e,i)=>{i.d(e,{A:()=>r});let n={direction:"forward",speed:2,startDelay:1e3,active:!0,breakpoints:{},playOnInit:!0,stopOnFocusIn:!0,stopOnInteraction:!0,stopOnMouseEnter:!1,rootNode:null};function r(t={}){let e,i,s,o,a,l=0,u=!1,c=!1;function h(){if(s||u)return;i.emit("autoScroll:play");let t=i.internalEngine(),{ownerWindow:n}=t;l=n.setTimeout(()=>{t.scrollBody=function(t){let{location:n,previousLocation:r,offsetLocation:s,target:o,scrollTarget:a,index:l,indexPrevious:u,limit:{reachedMin:c,reachedMax:h,constrain:p},options:{loop:m}}=t,f="forward"===e.direction?-1:1,g=()=>k,y=0,v=0,b=n.get(),x=0,w=!1,k={direction:()=>v,duration:()=>-1,velocity:()=>y,settled:()=>w,seek:function(){let t=0;r.set(n),y=f*e.speed,b+=y,n.add(y),o.set(n),v=Math.sign(b-x),x=b;let g=a.byDistance(0,!1).index;l.get()!==g&&(u.set(l.get()),l.set(g),i.emit("select"));let T="forward"===e.direction?c(s.get()):h(s.get());if(!m&&T){w=!0;let t=p(n.get());n.set(t),o.set(n),d()}return k},useBaseFriction:g,useBaseDuration:g,useFriction:g,useDuration:g};return k}(t),t.animation.start()},o),u=!0}function d(){if(s||!u)return;i.emit("autoScroll:stop");let t=i.internalEngine(),{ownerWindow:e}=t;t.scrollBody=a,e.clearTimeout(l),l=0,u=!1}function p(){c||d()}function m(){c||v()}function f(){c=!0,d()}function g(){c=!1,h()}function y(){i.off("settle",y),h()}function v(){i.on("settle",y)}return{name:"autoScroll",options:t,init:function(l,u){i=l;let{mergeOptions:c,optionsAtMedia:y}=u,v=c(n,r.globalOptions);if(e=y(c(v,t)),i.scrollSnapList().length<=1)return;o=e.startDelay,s=!1,a=i.internalEngine().scrollBody;let{eventStore:b}=i.internalEngine(),x=!!i.internalEngine().options.watchDrag,w=function(t,e){let i=t.rootNode();return e&&e(i)||i}(i,e.rootNode);x&&i.on("pointerDown",p),x&&!e.stopOnInteraction&&i.on("pointerUp",m),e.stopOnMouseEnter&&b.add(w,"mouseenter",f),e.stopOnMouseEnter&&!e.stopOnInteraction&&b.add(w,"mouseleave",g),e.stopOnFocusIn&&i.on("slideFocusStart",d),e.stopOnFocusIn&&!e.stopOnInteraction&&b.add(i.containerNode(),"focusout",h),e.playOnInit&&h()},destroy:function(){i.off("pointerDown",p).off("pointerUp",m).off("slideFocusStart",d).off("settle",y),d(),s=!0,u=!1},play:function(t){void 0!==t&&(o=t),h()},stop:function(){u&&d()},reset:function(){u&&(d(),v())},isPlaying:function(){return u}}}r.globalOptions=void 0},68485:(t,e,i)=>{i.d(e,{N:()=>v});var n=i(3641),r=i(44508),s=i(44490),o=i(7014),a=i(2893),l=i(60462),u=i(80693),c=i(16519);class h extends r.Component{getSnapshotBeforeUpdate(t){let e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){let t=e.offsetParent,i=(0,u.s)(t)&&t.offsetWidth||0,n=this.props.sizeRef.current;n.height=e.offsetHeight||0,n.width=e.offsetWidth||0,n.top=e.offsetTop,n.left=e.offsetLeft,n.right=i-n.width-n.left}return null}componentDidUpdate(){}render(){return this.props.children}}function d({children:t,isPresent:e,anchorX:i}){let s=(0,r.useId)(),o=(0,r.useRef)(null),a=(0,r.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:l}=(0,r.useContext)(c.Q);return(0,r.useInsertionEffect)(()=>{let{width:t,height:n,top:r,left:u,right:c}=a.current;if(e||!o.current||!t||!n)return;let h="left"===i?`left: ${u}`:`right: ${c}`;o.current.dataset.motionPopId=s;let d=document.createElement("style");return l&&(d.nonce=l),document.head.appendChild(d),d.sheet&&d.sheet.insertRule(`
          [data-motion-pop-id="${s}"] {
            position: absolute !important;
            width: ${t}px !important;
            height: ${n}px !important;
            ${h}px !important;
            top: ${r}px !important;
          }
        `),()=>{document.head.contains(d)&&document.head.removeChild(d)}},[e]),(0,n.jsx)(h,{isPresent:e,childRef:o,sizeRef:a,children:r.cloneElement(t,{ref:o})})}let p=({children:t,initial:e,isPresent:i,onExitComplete:s,custom:a,presenceAffectsLayout:u,mode:c,anchorX:h})=>{let p=(0,o.M)(m),f=(0,r.useId)(),g=!0,y=(0,r.useMemo)(()=>(g=!1,{id:f,initial:e,isPresent:i,custom:a,onExitComplete:t=>{for(let e of(p.set(t,!0),p.values()))if(!e)return;s&&s()},register:t=>(p.set(t,!1),()=>p.delete(t))}),[i,p,s]);return u&&g&&(y={...y}),(0,r.useMemo)(()=>{p.forEach((t,e)=>p.set(e,!1))},[i]),r.useEffect(()=>{i||p.size||!s||s()},[i]),"popLayout"===c&&(t=(0,n.jsx)(d,{isPresent:i,anchorX:h,children:t})),(0,n.jsx)(l.t.Provider,{value:y,children:t})};function m(){return new Map}var f=i(31933);let g=t=>t.key||"";function y(t){let e=[];return r.Children.forEach(t,t=>{(0,r.isValidElement)(t)&&e.push(t)}),e}let v=({children:t,custom:e,initial:i=!0,onExitComplete:l,presenceAffectsLayout:u=!0,mode:c="sync",propagate:h=!1,anchorX:d="left"})=>{let[m,v]=(0,f.xQ)(h),b=(0,r.useMemo)(()=>y(t),[t]),x=h&&!m?[]:b.map(g),w=(0,r.useRef)(!0),k=(0,r.useRef)(b),T=(0,o.M)(()=>new Map),[S,P]=(0,r.useState)(b),[A,M]=(0,r.useState)(b);(0,a.E)(()=>{w.current=!1,k.current=b;for(let t=0;t<A.length;t++){let e=g(A[t]);x.includes(e)?T.delete(e):!0!==T.get(e)&&T.set(e,!1)}},[A,x.length,x.join("-")]);let E=[];if(b!==S){let t=[...b];for(let e=0;e<A.length;e++){let i=A[e],n=g(i);x.includes(n)||(t.splice(e,0,i),E.push(i))}return"wait"===c&&E.length&&(t=E),M(y(t)),P(b),null}let{forceRender:V}=(0,r.useContext)(s.L);return(0,n.jsx)(n.Fragment,{children:A.map(t=>{let r=g(t),s=(!h||!!m)&&(b===A||x.includes(r));return(0,n.jsx)(p,{isPresent:s,initial:(!w.current||!!i)&&void 0,custom:e,presenceAffectsLayout:u,mode:c,onExitComplete:s?void 0:()=>{if(!T.has(r))return;T.set(r,!0);let t=!0;T.forEach(e=>{e||(t=!1)}),t&&(V?.(),M(k.current),h&&v?.(),l&&l())},anchorX:d,children:t},r)})})}},69097:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(72364).A)("code-xml",[["path",{d:"m18 16 4-4-4-4",key:"1inbqp"}],["path",{d:"m6 8-4 4 4 4",key:"15zrgr"}],["path",{d:"m14.5 4-5 16",key:"e7oirm"}]])},71537:(t,e,i)=>{i.d(e,{$:()=>n});function n(){for(var t,e,i=0,n="",r=arguments.length;i<r;i++)(t=arguments[i])&&(e=function t(e){var i,n,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e)){var s=e.length;for(i=0;i<s;i++)e[i]&&(n=t(e[i]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}(t))&&(n&&(n+=" "),n+=e);return n}},80693:(t,e,i)=>{i.d(e,{s:()=>r});var n=i(63179);function r(t){return(0,n.G)(t)&&"offsetHeight"in t}},82085:(t,e,i)=>{i.d(e,{A:()=>r});let n={active:!0,breakpoints:{},delay:4e3,jump:!1,playOnInit:!0,stopOnFocusIn:!0,stopOnInteraction:!0,stopOnMouseEnter:!1,stopOnLastSnap:!1,rootNode:null};function r(t={}){let e,i,s,o,a=null,l=0,u=!1,c=!1,h=!1,d=!1;function p(){if(!s){if(g()){h=!0;return}u||i.emit("autoplay:play");let{ownerWindow:t}=i.internalEngine();t.clearTimeout(l),l=t.setTimeout(w,o[i.selectedScrollSnap()]),a=new Date().getTime(),i.emit("autoplay:timerset"),u=!0}}function m(){if(!s){u&&i.emit("autoplay:stop");let{ownerWindow:t}=i.internalEngine();t.clearTimeout(l),l=0,a=null,i.emit("autoplay:timerstopped"),u=!1}}function f(){if(g())return h=u,m();h&&p()}function g(){let{ownerDocument:t}=i.internalEngine();return"hidden"===t.visibilityState}function y(){c||m()}function v(){c||p()}function b(){c=!0,m()}function x(){c=!1,p()}function w(){let{index:t}=i.internalEngine(),n=t.clone().add(1).get(),r=i.scrollSnapList().length-1,s=e.stopOnLastSnap&&n===r;if(i.canScrollNext()?i.scrollNext(d):i.scrollTo(0,d),i.emit("autoplay:select"),s)return m();p()}return{name:"autoplay",options:t,init:function(a,l){i=a;let{mergeOptions:u,optionsAtMedia:c}=l,h=u(n,r.globalOptions);if(e=c(u(h,t)),i.scrollSnapList().length<=1)return;d=e.jump,s=!1,o=function(t,e){let i=t.scrollSnapList();return"number"==typeof e?i.map(()=>e):e(i,t)}(i,e.delay);let{eventStore:g,ownerDocument:w}=i.internalEngine(),k=!!i.internalEngine().options.watchDrag,T=function(t,e){let i=t.rootNode();return e&&e(i)||i}(i,e.rootNode);g.add(w,"visibilitychange",f),k&&i.on("pointerDown",y),k&&!e.stopOnInteraction&&i.on("pointerUp",v),e.stopOnMouseEnter&&g.add(T,"mouseenter",b),e.stopOnMouseEnter&&!e.stopOnInteraction&&g.add(T,"mouseleave",x),e.stopOnFocusIn&&i.on("slideFocusStart",m),e.stopOnFocusIn&&!e.stopOnInteraction&&g.add(i.containerNode(),"focusout",p),e.playOnInit&&p()},destroy:function(){i.off("pointerDown",y).off("pointerUp",v).off("slideFocusStart",m),m(),s=!0,u=!1},play:function(t){void 0!==t&&(d=t),p()},stop:function(){u&&m()},reset:function(){u&&p()},isPlaying:function(){return u},timeUntilNext:function(){return a?o[i.selectedScrollSnap()]-(new Date().getTime()-a):null}}}r.globalOptions=void 0},82612:(t,e,i)=>{i.d(e,{QP:()=>tu});let n=t=>{let e=a(t),{conflictingClassGroups:i,conflictingClassGroupModifiers:n}=t;return{getClassGroupId:t=>{let i=t.split("-");return""===i[0]&&1!==i.length&&i.shift(),r(i,e)||o(t)},getConflictingClassGroupIds:(t,e)=>{let r=i[t]||[];return e&&n[t]?[...r,...n[t]]:r}}},r=(t,e)=>{if(0===t.length)return e.classGroupId;let i=t[0],n=e.nextPart.get(i),s=n?r(t.slice(1),n):void 0;if(s)return s;if(0===e.validators.length)return;let o=t.join("-");return e.validators.find(({validator:t})=>t(o))?.classGroupId},s=/^\[(.+)\]$/,o=t=>{if(s.test(t)){let e=s.exec(t)[1],i=e?.substring(0,e.indexOf(":"));if(i)return"arbitrary.."+i}},a=t=>{let{theme:e,classGroups:i}=t,n={nextPart:new Map,validators:[]};for(let t in i)l(i[t],n,t,e);return n},l=(t,e,i,n)=>{t.forEach(t=>{if("string"==typeof t){(""===t?e:u(e,t)).classGroupId=i;return}if("function"==typeof t){if(c(t)){l(t(n),e,i,n);return}e.validators.push({validator:t,classGroupId:i});return}Object.entries(t).forEach(([t,r])=>{l(r,u(e,t),i,n)})})},u=(t,e)=>{let i=t;return e.split("-").forEach(t=>{i.nextPart.has(t)||i.nextPart.set(t,{nextPart:new Map,validators:[]}),i=i.nextPart.get(t)}),i},c=t=>t.isThemeGetter,h=t=>{if(t<1)return{get:()=>void 0,set:()=>{}};let e=0,i=new Map,n=new Map,r=(r,s)=>{i.set(r,s),++e>t&&(e=0,n=i,i=new Map)};return{get(t){let e=i.get(t);return void 0!==e?e:void 0!==(e=n.get(t))?(r(t,e),e):void 0},set(t,e){i.has(t)?i.set(t,e):r(t,e)}}},d=t=>{let{prefix:e,experimentalParseClassName:i}=t,n=t=>{let e,i=[],n=0,r=0,s=0;for(let o=0;o<t.length;o++){let a=t[o];if(0===n&&0===r){if(":"===a){i.push(t.slice(s,o)),s=o+1;continue}if("/"===a){e=o;continue}}"["===a?n++:"]"===a?n--:"("===a?r++:")"===a&&r--}let o=0===i.length?t:t.substring(s),a=p(o);return{modifiers:i,hasImportantModifier:a!==o,baseClassName:a,maybePostfixModifierPosition:e&&e>s?e-s:void 0}};if(e){let t=e+":",i=n;n=e=>e.startsWith(t)?i(e.substring(t.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:e,maybePostfixModifierPosition:void 0}}if(i){let t=n;n=e=>i({className:e,parseClassName:t})}return n},p=t=>t.endsWith("!")?t.substring(0,t.length-1):t.startsWith("!")?t.substring(1):t,m=t=>{let e=Object.fromEntries(t.orderSensitiveModifiers.map(t=>[t,!0]));return t=>{if(t.length<=1)return t;let i=[],n=[];return t.forEach(t=>{"["===t[0]||e[t]?(i.push(...n.sort(),t),n=[]):n.push(t)}),i.push(...n.sort()),i}},f=t=>({cache:h(t.cacheSize),parseClassName:d(t),sortModifiers:m(t),...n(t)}),g=/\s+/,y=(t,e)=>{let{parseClassName:i,getClassGroupId:n,getConflictingClassGroupIds:r,sortModifiers:s}=e,o=[],a=t.trim().split(g),l="";for(let t=a.length-1;t>=0;t-=1){let e=a[t],{isExternal:u,modifiers:c,hasImportantModifier:h,baseClassName:d,maybePostfixModifierPosition:p}=i(e);if(u){l=e+(l.length>0?" "+l:l);continue}let m=!!p,f=n(m?d.substring(0,p):d);if(!f){if(!m||!(f=n(d))){l=e+(l.length>0?" "+l:l);continue}m=!1}let g=s(c).join(":"),y=h?g+"!":g,v=y+f;if(o.includes(v))continue;o.push(v);let b=r(f,m);for(let t=0;t<b.length;++t){let e=b[t];o.push(y+e)}l=e+(l.length>0?" "+l:l)}return l};function v(){let t,e,i=0,n="";for(;i<arguments.length;)(t=arguments[i++])&&(e=b(t))&&(n&&(n+=" "),n+=e);return n}let b=t=>{let e;if("string"==typeof t)return t;let i="";for(let n=0;n<t.length;n++)t[n]&&(e=b(t[n]))&&(i&&(i+=" "),i+=e);return i},x=t=>{let e=e=>e[t]||[];return e.isThemeGetter=!0,e},w=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,k=/^\((?:(\w[\w-]*):)?(.+)\)$/i,T=/^\d+\/\d+$/,S=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,P=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,A=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,M=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,E=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,V=t=>T.test(t),D=t=>!!t&&!Number.isNaN(Number(t)),C=t=>!!t&&Number.isInteger(Number(t)),j=t=>t.endsWith("%")&&D(t.slice(0,-1)),L=t=>S.test(t),R=()=>!0,O=t=>P.test(t)&&!A.test(t),F=()=>!1,B=t=>M.test(t),I=t=>E.test(t),z=t=>!U(t)&&!q(t),N=t=>tt(t,tr,F),U=t=>w.test(t),$=t=>tt(t,ts,O),W=t=>tt(t,to,D),H=t=>tt(t,ti,F),X=t=>tt(t,tn,I),Y=t=>tt(t,tl,B),q=t=>k.test(t),G=t=>te(t,ts),K=t=>te(t,ta),Z=t=>te(t,ti),_=t=>te(t,tr),Q=t=>te(t,tn),J=t=>te(t,tl,!0),tt=(t,e,i)=>{let n=w.exec(t);return!!n&&(n[1]?e(n[1]):i(n[2]))},te=(t,e,i=!1)=>{let n=k.exec(t);return!!n&&(n[1]?e(n[1]):i)},ti=t=>"position"===t||"percentage"===t,tn=t=>"image"===t||"url"===t,tr=t=>"length"===t||"size"===t||"bg-size"===t,ts=t=>"length"===t,to=t=>"number"===t,ta=t=>"family-name"===t,tl=t=>"shadow"===t;Symbol.toStringTag;let tu=function(t,...e){let i,n,r,s=function(a){return n=(i=f(e.reduce((t,e)=>e(t),t()))).cache.get,r=i.cache.set,s=o,o(a)};function o(t){let e=n(t);if(e)return e;let s=y(t,i);return r(t,s),s}return function(){return s(v.apply(null,arguments))}}(()=>{let t=x("color"),e=x("font"),i=x("text"),n=x("font-weight"),r=x("tracking"),s=x("leading"),o=x("breakpoint"),a=x("container"),l=x("spacing"),u=x("radius"),c=x("shadow"),h=x("inset-shadow"),d=x("text-shadow"),p=x("drop-shadow"),m=x("blur"),f=x("perspective"),g=x("aspect"),y=x("ease"),v=x("animate"),b=()=>["auto","avoid","all","avoid-page","page","left","right","column"],w=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],k=()=>[...w(),q,U],T=()=>["auto","hidden","clip","visible","scroll"],S=()=>["auto","contain","none"],P=()=>[q,U,l],A=()=>[V,"full","auto",...P()],M=()=>[C,"none","subgrid",q,U],E=()=>["auto",{span:["full",C,q,U]},C,q,U],O=()=>[C,"auto",q,U],F=()=>["auto","min","max","fr",q,U],B=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],I=()=>["start","end","center","stretch","center-safe","end-safe"],tt=()=>["auto",...P()],te=()=>[V,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...P()],ti=()=>[t,q,U],tn=()=>[...w(),Z,H,{position:[q,U]}],tr=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ts=()=>["auto","cover","contain",_,N,{size:[q,U]}],to=()=>[j,G,$],ta=()=>["","none","full",u,q,U],tl=()=>["",D,G,$],tu=()=>["solid","dashed","dotted","double"],tc=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],th=()=>[D,j,Z,H],td=()=>["","none",m,q,U],tp=()=>["none",D,q,U],tm=()=>["none",D,q,U],tf=()=>[D,q,U],tg=()=>[V,"full",...P()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[L],breakpoint:[L],color:[R],container:[L],"drop-shadow":[L],ease:["in","out","in-out"],font:[z],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[L],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[L],shadow:[L],spacing:["px",D],text:[L],"text-shadow":[L],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",V,U,q,g]}],container:["container"],columns:[{columns:[D,U,q,a]}],"break-after":[{"break-after":b()}],"break-before":[{"break-before":b()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:k()}],overflow:[{overflow:T()}],"overflow-x":[{"overflow-x":T()}],"overflow-y":[{"overflow-y":T()}],overscroll:[{overscroll:S()}],"overscroll-x":[{"overscroll-x":S()}],"overscroll-y":[{"overscroll-y":S()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:A()}],"inset-x":[{"inset-x":A()}],"inset-y":[{"inset-y":A()}],start:[{start:A()}],end:[{end:A()}],top:[{top:A()}],right:[{right:A()}],bottom:[{bottom:A()}],left:[{left:A()}],visibility:["visible","invisible","collapse"],z:[{z:[C,"auto",q,U]}],basis:[{basis:[V,"full","auto",a,...P()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[D,V,"auto","initial","none",U]}],grow:[{grow:["",D,q,U]}],shrink:[{shrink:["",D,q,U]}],order:[{order:[C,"first","last","none",q,U]}],"grid-cols":[{"grid-cols":M()}],"col-start-end":[{col:E()}],"col-start":[{"col-start":O()}],"col-end":[{"col-end":O()}],"grid-rows":[{"grid-rows":M()}],"row-start-end":[{row:E()}],"row-start":[{"row-start":O()}],"row-end":[{"row-end":O()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":F()}],"auto-rows":[{"auto-rows":F()}],gap:[{gap:P()}],"gap-x":[{"gap-x":P()}],"gap-y":[{"gap-y":P()}],"justify-content":[{justify:[...B(),"normal"]}],"justify-items":[{"justify-items":[...I(),"normal"]}],"justify-self":[{"justify-self":["auto",...I()]}],"align-content":[{content:["normal",...B()]}],"align-items":[{items:[...I(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...I(),{baseline:["","last"]}]}],"place-content":[{"place-content":B()}],"place-items":[{"place-items":[...I(),"baseline"]}],"place-self":[{"place-self":["auto",...I()]}],p:[{p:P()}],px:[{px:P()}],py:[{py:P()}],ps:[{ps:P()}],pe:[{pe:P()}],pt:[{pt:P()}],pr:[{pr:P()}],pb:[{pb:P()}],pl:[{pl:P()}],m:[{m:tt()}],mx:[{mx:tt()}],my:[{my:tt()}],ms:[{ms:tt()}],me:[{me:tt()}],mt:[{mt:tt()}],mr:[{mr:tt()}],mb:[{mb:tt()}],ml:[{ml:tt()}],"space-x":[{"space-x":P()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":P()}],"space-y-reverse":["space-y-reverse"],size:[{size:te()}],w:[{w:[a,"screen",...te()]}],"min-w":[{"min-w":[a,"screen","none",...te()]}],"max-w":[{"max-w":[a,"screen","none","prose",{screen:[o]},...te()]}],h:[{h:["screen","lh",...te()]}],"min-h":[{"min-h":["screen","lh","none",...te()]}],"max-h":[{"max-h":["screen","lh",...te()]}],"font-size":[{text:["base",i,G,$]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[n,q,W]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",j,U]}],"font-family":[{font:[K,U,e]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[r,q,U]}],"line-clamp":[{"line-clamp":[D,"none",q,W]}],leading:[{leading:[s,...P()]}],"list-image":[{"list-image":["none",q,U]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",q,U]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:ti()}],"text-color":[{text:ti()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...tu(),"wavy"]}],"text-decoration-thickness":[{decoration:[D,"from-font","auto",q,$]}],"text-decoration-color":[{decoration:ti()}],"underline-offset":[{"underline-offset":[D,"auto",q,U]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:P()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",q,U]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",q,U]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:tn()}],"bg-repeat":[{bg:tr()}],"bg-size":[{bg:ts()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},C,q,U],radial:["",q,U],conic:[C,q,U]},Q,X]}],"bg-color":[{bg:ti()}],"gradient-from-pos":[{from:to()}],"gradient-via-pos":[{via:to()}],"gradient-to-pos":[{to:to()}],"gradient-from":[{from:ti()}],"gradient-via":[{via:ti()}],"gradient-to":[{to:ti()}],rounded:[{rounded:ta()}],"rounded-s":[{"rounded-s":ta()}],"rounded-e":[{"rounded-e":ta()}],"rounded-t":[{"rounded-t":ta()}],"rounded-r":[{"rounded-r":ta()}],"rounded-b":[{"rounded-b":ta()}],"rounded-l":[{"rounded-l":ta()}],"rounded-ss":[{"rounded-ss":ta()}],"rounded-se":[{"rounded-se":ta()}],"rounded-ee":[{"rounded-ee":ta()}],"rounded-es":[{"rounded-es":ta()}],"rounded-tl":[{"rounded-tl":ta()}],"rounded-tr":[{"rounded-tr":ta()}],"rounded-br":[{"rounded-br":ta()}],"rounded-bl":[{"rounded-bl":ta()}],"border-w":[{border:tl()}],"border-w-x":[{"border-x":tl()}],"border-w-y":[{"border-y":tl()}],"border-w-s":[{"border-s":tl()}],"border-w-e":[{"border-e":tl()}],"border-w-t":[{"border-t":tl()}],"border-w-r":[{"border-r":tl()}],"border-w-b":[{"border-b":tl()}],"border-w-l":[{"border-l":tl()}],"divide-x":[{"divide-x":tl()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":tl()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...tu(),"hidden","none"]}],"divide-style":[{divide:[...tu(),"hidden","none"]}],"border-color":[{border:ti()}],"border-color-x":[{"border-x":ti()}],"border-color-y":[{"border-y":ti()}],"border-color-s":[{"border-s":ti()}],"border-color-e":[{"border-e":ti()}],"border-color-t":[{"border-t":ti()}],"border-color-r":[{"border-r":ti()}],"border-color-b":[{"border-b":ti()}],"border-color-l":[{"border-l":ti()}],"divide-color":[{divide:ti()}],"outline-style":[{outline:[...tu(),"none","hidden"]}],"outline-offset":[{"outline-offset":[D,q,U]}],"outline-w":[{outline:["",D,G,$]}],"outline-color":[{outline:ti()}],shadow:[{shadow:["","none",c,J,Y]}],"shadow-color":[{shadow:ti()}],"inset-shadow":[{"inset-shadow":["none",h,J,Y]}],"inset-shadow-color":[{"inset-shadow":ti()}],"ring-w":[{ring:tl()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:ti()}],"ring-offset-w":[{"ring-offset":[D,$]}],"ring-offset-color":[{"ring-offset":ti()}],"inset-ring-w":[{"inset-ring":tl()}],"inset-ring-color":[{"inset-ring":ti()}],"text-shadow":[{"text-shadow":["none",d,J,Y]}],"text-shadow-color":[{"text-shadow":ti()}],opacity:[{opacity:[D,q,U]}],"mix-blend":[{"mix-blend":[...tc(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":tc()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[D]}],"mask-image-linear-from-pos":[{"mask-linear-from":th()}],"mask-image-linear-to-pos":[{"mask-linear-to":th()}],"mask-image-linear-from-color":[{"mask-linear-from":ti()}],"mask-image-linear-to-color":[{"mask-linear-to":ti()}],"mask-image-t-from-pos":[{"mask-t-from":th()}],"mask-image-t-to-pos":[{"mask-t-to":th()}],"mask-image-t-from-color":[{"mask-t-from":ti()}],"mask-image-t-to-color":[{"mask-t-to":ti()}],"mask-image-r-from-pos":[{"mask-r-from":th()}],"mask-image-r-to-pos":[{"mask-r-to":th()}],"mask-image-r-from-color":[{"mask-r-from":ti()}],"mask-image-r-to-color":[{"mask-r-to":ti()}],"mask-image-b-from-pos":[{"mask-b-from":th()}],"mask-image-b-to-pos":[{"mask-b-to":th()}],"mask-image-b-from-color":[{"mask-b-from":ti()}],"mask-image-b-to-color":[{"mask-b-to":ti()}],"mask-image-l-from-pos":[{"mask-l-from":th()}],"mask-image-l-to-pos":[{"mask-l-to":th()}],"mask-image-l-from-color":[{"mask-l-from":ti()}],"mask-image-l-to-color":[{"mask-l-to":ti()}],"mask-image-x-from-pos":[{"mask-x-from":th()}],"mask-image-x-to-pos":[{"mask-x-to":th()}],"mask-image-x-from-color":[{"mask-x-from":ti()}],"mask-image-x-to-color":[{"mask-x-to":ti()}],"mask-image-y-from-pos":[{"mask-y-from":th()}],"mask-image-y-to-pos":[{"mask-y-to":th()}],"mask-image-y-from-color":[{"mask-y-from":ti()}],"mask-image-y-to-color":[{"mask-y-to":ti()}],"mask-image-radial":[{"mask-radial":[q,U]}],"mask-image-radial-from-pos":[{"mask-radial-from":th()}],"mask-image-radial-to-pos":[{"mask-radial-to":th()}],"mask-image-radial-from-color":[{"mask-radial-from":ti()}],"mask-image-radial-to-color":[{"mask-radial-to":ti()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":w()}],"mask-image-conic-pos":[{"mask-conic":[D]}],"mask-image-conic-from-pos":[{"mask-conic-from":th()}],"mask-image-conic-to-pos":[{"mask-conic-to":th()}],"mask-image-conic-from-color":[{"mask-conic-from":ti()}],"mask-image-conic-to-color":[{"mask-conic-to":ti()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:tn()}],"mask-repeat":[{mask:tr()}],"mask-size":[{mask:ts()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",q,U]}],filter:[{filter:["","none",q,U]}],blur:[{blur:td()}],brightness:[{brightness:[D,q,U]}],contrast:[{contrast:[D,q,U]}],"drop-shadow":[{"drop-shadow":["","none",p,J,Y]}],"drop-shadow-color":[{"drop-shadow":ti()}],grayscale:[{grayscale:["",D,q,U]}],"hue-rotate":[{"hue-rotate":[D,q,U]}],invert:[{invert:["",D,q,U]}],saturate:[{saturate:[D,q,U]}],sepia:[{sepia:["",D,q,U]}],"backdrop-filter":[{"backdrop-filter":["","none",q,U]}],"backdrop-blur":[{"backdrop-blur":td()}],"backdrop-brightness":[{"backdrop-brightness":[D,q,U]}],"backdrop-contrast":[{"backdrop-contrast":[D,q,U]}],"backdrop-grayscale":[{"backdrop-grayscale":["",D,q,U]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[D,q,U]}],"backdrop-invert":[{"backdrop-invert":["",D,q,U]}],"backdrop-opacity":[{"backdrop-opacity":[D,q,U]}],"backdrop-saturate":[{"backdrop-saturate":[D,q,U]}],"backdrop-sepia":[{"backdrop-sepia":["",D,q,U]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":P()}],"border-spacing-x":[{"border-spacing-x":P()}],"border-spacing-y":[{"border-spacing-y":P()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",q,U]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[D,"initial",q,U]}],ease:[{ease:["linear","initial",y,q,U]}],delay:[{delay:[D,q,U]}],animate:[{animate:["none",v,q,U]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[f,q,U]}],"perspective-origin":[{"perspective-origin":k()}],rotate:[{rotate:tp()}],"rotate-x":[{"rotate-x":tp()}],"rotate-y":[{"rotate-y":tp()}],"rotate-z":[{"rotate-z":tp()}],scale:[{scale:tm()}],"scale-x":[{"scale-x":tm()}],"scale-y":[{"scale-y":tm()}],"scale-z":[{"scale-z":tm()}],"scale-3d":["scale-3d"],skew:[{skew:tf()}],"skew-x":[{"skew-x":tf()}],"skew-y":[{"skew-y":tf()}],transform:[{transform:[q,U,"","none","gpu","cpu"]}],"transform-origin":[{origin:k()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:tg()}],"translate-x":[{"translate-x":tg()}],"translate-y":[{"translate-y":tg()}],"translate-z":[{"translate-z":tg()}],"translate-none":["translate-none"],accent:[{accent:ti()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:ti()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",q,U]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":P()}],"scroll-mx":[{"scroll-mx":P()}],"scroll-my":[{"scroll-my":P()}],"scroll-ms":[{"scroll-ms":P()}],"scroll-me":[{"scroll-me":P()}],"scroll-mt":[{"scroll-mt":P()}],"scroll-mr":[{"scroll-mr":P()}],"scroll-mb":[{"scroll-mb":P()}],"scroll-ml":[{"scroll-ml":P()}],"scroll-p":[{"scroll-p":P()}],"scroll-px":[{"scroll-px":P()}],"scroll-py":[{"scroll-py":P()}],"scroll-ps":[{"scroll-ps":P()}],"scroll-pe":[{"scroll-pe":P()}],"scroll-pt":[{"scroll-pt":P()}],"scroll-pr":[{"scroll-pr":P()}],"scroll-pb":[{"scroll-pb":P()}],"scroll-pl":[{"scroll-pl":P()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",q,U]}],fill:[{fill:["none",...ti()]}],"stroke-w":[{stroke:[D,G,$,W]}],stroke:[{stroke:["none",...ti()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},85088:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(72364).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},90631:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(72364).A)("users-round",[["path",{d:"M18 21a8 8 0 0 0-16 0",key:"3ypg7q"}],["circle",{cx:"10",cy:"8",r:"5",key:"o932ke"}],["path",{d:"M22 20c0-3.37-2-6.5-4-8a5 5 0 0 0-.45-8.3",key:"10s06x"}]])},95542:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(72364).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])}};