"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[554],{18322:(e,t,r)=>{r.d(t,{cn:()=>l});var a=r(67111),o=r(51046);function l(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,o.QP)((0,a.$)(t))}},29301:(e,t,r)=>{r.d(t,{$:()=>i});var a=r(47093),o=r(34545),l=r(6255),n=r(2447),s=r(18322);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),i=o.forwardRef((e,t)=>{let{className:r,variant:o,size:n,asChild:i=!1,...c}=e,u=i?l.DX:"button";return(0,a.jsx)(u,{className:(0,s.cn)(d({variant:o,size:n,className:r})),ref:t,...c})});i.displayName="Button"},33731:(e,t,r)=>{r.r(t),r.d(t,{ChartContainer:()=>f,ChartLegend:()=>p,ChartLegendContent:()=>x,ChartStyle:()=>m,ChartTooltip:()=>g,ChartTooltipContent:()=>h});var a=r(47093),o=r(34545),l=r(37733),n=r(88258),s=r(26631),d=r(18322);let i={light:"",dark:".dark"},c=o.createContext(null);function u(){let e=o.useContext(c);if(!e)throw Error("useChart must be used within a <ChartContainer />");return e}let f=o.forwardRef((e,t)=>{let{id:r,className:n,children:s,config:i,...u}=e,f=o.useId(),g="chart-".concat(r||f.replace(/:/g,""));return(0,a.jsx)(c.Provider,{value:{config:i},children:(0,a.jsxs)("div",{"data-chart":g,ref:t,className:(0,d.cn)("flex aspect-video justify-center text-xs [&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-none [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-sector]:outline-none [&_.recharts-surface]:outline-none",n),...u,children:[(0,a.jsx)(m,{id:g,config:i}),(0,a.jsx)(l.u,{children:s})]})})});f.displayName="Chart";let m=e=>{let{id:t,config:r}=e,o=Object.entries(r).filter(e=>{let[,t]=e;return t.theme||t.color});return o.length?(0,a.jsx)("style",{dangerouslySetInnerHTML:{__html:Object.entries(i).map(e=>{let[r,a]=e;return"\n".concat(a," [data-chart=").concat(t,"] {\n").concat(o.map(e=>{var t;let[a,o]=e,l=(null===(t=o.theme)||void 0===t?void 0:t[r])||o.color;return l?"  --color-".concat(a,": ").concat(l,";"):null}).join("\n"),"\n}\n")}).join("\n")}}):null},g=n.m,h=o.forwardRef((e,t)=>{let{active:r,payload:l,className:n,indicator:s="dot",hideLabel:i=!1,hideIndicator:c=!1,label:f,labelFormatter:m,labelClassName:g,formatter:h,color:p,nameKey:x,labelKey:v}=e,{config:y}=u(),N=o.useMemo(()=>{var e;if(i||!(null==l?void 0:l.length))return null;let[t]=l,r="".concat(v||(null==t?void 0:t.dataKey)||(null==t?void 0:t.name)||"value"),o=b(y,t,r),n=v||"string"!=typeof f?null==o?void 0:o.label:(null===(e=y[f])||void 0===e?void 0:e.label)||f;return m?(0,a.jsx)("div",{className:(0,d.cn)("font-medium",g),children:m(n,l)}):n?(0,a.jsx)("div",{className:(0,d.cn)("font-medium",g),children:n}):null},[f,m,l,i,g,y,v]);if(!r||!(null==l?void 0:l.length))return null;let j=1===l.length&&"dot"!==s;return(0,a.jsxs)("div",{ref:t,className:(0,d.cn)("grid min-w-[8rem] items-start gap-1.5 rounded-lg border border-border/50 bg-background px-2.5 py-1.5 text-xs shadow-xl",n),children:[j?null:N,(0,a.jsx)("div",{className:"grid gap-1.5",children:l.map((e,t)=>{let r="".concat(x||e.name||e.dataKey||"value"),o=b(y,e,r),l=p||e.payload.fill||e.color;return(0,a.jsx)("div",{className:(0,d.cn)("flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground","dot"===s&&"items-center"),children:h&&(null==e?void 0:e.value)!==void 0&&e.name?h(e.value,e.name,e,t,e.payload):(0,a.jsxs)(a.Fragment,{children:[(null==o?void 0:o.icon)?(0,a.jsx)(o.icon,{}):!c&&(0,a.jsx)("div",{className:(0,d.cn)("shrink-0 rounded-[2px] border-[--color-border] bg-[--color-bg]",{"h-2.5 w-2.5":"dot"===s,"w-1":"line"===s,"w-0 border-[1.5px] border-dashed bg-transparent":"dashed"===s,"my-0.5":j&&"dashed"===s}),style:{"--color-bg":l,"--color-border":l}}),(0,a.jsxs)("div",{className:(0,d.cn)("flex flex-1 justify-between leading-none",j?"items-end":"items-center"),children:[(0,a.jsxs)("div",{className:"grid gap-1.5",children:[j?N:null,(0,a.jsx)("span",{className:"text-muted-foreground",children:(null==o?void 0:o.label)||e.name})]}),e.value&&(0,a.jsx)("span",{className:"font-mono font-medium tabular-nums text-foreground",children:e.value.toLocaleString()})]})]})},e.dataKey)})})]})});h.displayName="ChartTooltip";let p=s.s,x=o.forwardRef((e,t)=>{let{className:r,hideIcon:o=!1,payload:l,verticalAlign:n="bottom",nameKey:s}=e,{config:i}=u();return(null==l?void 0:l.length)?(0,a.jsx)("div",{ref:t,className:(0,d.cn)("flex items-center justify-center gap-4","top"===n?"pb-3":"pt-3",r),children:l.map(e=>{let t="".concat(s||e.dataKey||"value"),r=b(i,e,t);return(0,a.jsxs)("div",{className:(0,d.cn)("flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3 [&>svg]:text-muted-foreground"),children:[(null==r?void 0:r.icon)&&!o?(0,a.jsx)(r.icon,{}):(0,a.jsx)("div",{className:"h-2 w-2 shrink-0 rounded-[2px]",style:{backgroundColor:e.color}}),null==r?void 0:r.label]},e.value)})}):null});function b(e,t,r){if("object"!=typeof t||null===t)return;let a="payload"in t&&"object"==typeof t.payload&&null!==t.payload?t.payload:void 0,o=r;return r in t&&"string"==typeof t[r]?o=t[r]:a&&r in a&&"string"==typeof a[r]&&(o=a[r]),o in e?e[o]:e[r]}x.displayName="ChartLegend"},52496:(e,t,r)=>{r.r(t),r.d(t,{Dialog:()=>d,DialogClose:()=>u,DialogContent:()=>m,DialogDescription:()=>x,DialogFooter:()=>h,DialogHeader:()=>g,DialogOverlay:()=>f,DialogPortal:()=>c,DialogTitle:()=>p,DialogTrigger:()=>i});var a=r(47093),o=r(34545),l=r(50829),n=r(7676),s=r(18322);let d=l.bL,i=l.l9,c=l.ZL,u=l.bm,f=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,a.jsx)(l.hJ,{ref:t,className:(0,s.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",r),...o})});f.displayName=l.hJ.displayName;let m=o.forwardRef((e,t)=>{let{className:r,children:o,...d}=e;return(0,a.jsxs)(c,{children:[(0,a.jsx)(f,{}),(0,a.jsxs)(l.UC,{ref:t,className:(0,s.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border border-border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",r),...d,children:[o,(0,a.jsxs)(l.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(n.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});m.displayName=l.UC.displayName;let g=e=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,s.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...r})};g.displayName="DialogHeader";let h=e=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,s.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...r})};h.displayName="DialogFooter";let p=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,a.jsx)(l.hE,{ref:t,className:(0,s.cn)("text-lg font-semibold leading-none tracking-tight",r),...o})});p.displayName=l.hE.displayName;let x=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,a.jsx)(l.VY,{ref:t,className:(0,s.cn)("text-sm text-muted-foreground",r),...o})});x.displayName=l.VY.displayName},64979:(e,t,r)=>{r.r(t),r.d(t,{ScrollButton:()=>l});var a=r(47093),o=r(28923);function l(e){let{targetId:t,children:r,className:l="",onClick:n}=e,s=(0,o.useRouter)(),d=(0,o.usePathname)();return(0,a.jsx)("button",{onClick:()=>{if("/"===d){let e=document.getElementById(t);null==e||e.scrollIntoView({behavior:"smooth"})}else s.push("/#".concat(t));null==n||n()},className:l,children:r})}},91554:(e,t,r)=>{r.d(t,{$n:()=>a.$,at:()=>o.ChartContainer,_3:()=>o.ChartLegend,Hm:()=>o.ChartLegendContent,II:()=>o.ChartTooltip,Nt:()=>o.ChartTooltipContent,lG:()=>l.Dialog,Cf:()=>l.DialogContent,rr:()=>l.DialogDescription,Es:()=>l.DialogFooter,c7:()=>l.DialogHeader,L3:()=>l.DialogTitle,zM:()=>l.DialogTrigger,sb:()=>n.ScrollButton,XI:()=>c,BF:()=>f,r6:()=>p,nA:()=>h,nd:()=>g,A0:()=>u,Hj:()=>m});var a=r(29301),o=r(33731),l=r(52496),n=r(64979),s=r(47093),d=r(34545),i=r(18322);let c=d.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{className:"relative w-full overflow-auto",children:(0,s.jsx)("table",{ref:t,className:(0,i.cn)("w-full caption-bottom text-sm",r),...a})})});c.displayName="Table";let u=d.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("thead",{ref:t,className:(0,i.cn)("[&_tr]:border-b",r),...a})});u.displayName="TableHeader";let f=d.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("tbody",{ref:t,className:(0,i.cn)("[&_tr:last-child]:border-0",r),...a})});f.displayName="TableBody",d.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("tfoot",{ref:t,className:(0,i.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",r),...a})}).displayName="TableFooter";let m=d.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("tr",{ref:t,className:(0,i.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",r),...a})});m.displayName="TableRow";let g=d.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("th",{ref:t,className:(0,i.cn)("h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",r),...a})});g.displayName="TableHead";let h=d.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("td",{ref:t,className:(0,i.cn)("p-2 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",r),...a})});h.displayName="TableCell";let p=d.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("caption",{ref:t,className:(0,i.cn)("mt-4 text-sm text-muted-foreground",r),...a})});p.displayName="TableCaption"}}]);