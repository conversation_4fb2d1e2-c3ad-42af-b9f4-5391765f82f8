<common_mistakes_to_avoid>
  - Not creating a todo list at the start to track the review workflow
  - Using MCP tools instead of GitHub CLI commands for GitHub operations
  - Starting to review code WITHOUT first fetching existing comments and reviews
  - Failing to create a list of existing feedback before reviewing
  - Not systematically checking each existing comment against the current code
  - Repeating feedback that has already been addressed in the current code
  - Ignoring existing PR comments or failing to verify if they have already been resolved
  - Running tests or executing code during review
  - Making judgmental or harsh comments
  - Providing feedback on code outside the PR's scope
  - Overlooking unrelated changes not tied to the main issue
  - Including ANY praise or positive comments - focus only on issues
  - Using markdown headings (###, ##, #) in review comments
  - Using excessive markdown formatting when plain text would suffice
  - Submitting comments without user preview/approval
  - Forgetting to check for an associated issue for additional context
  - Missing critical security or performance issues
  - Not checking for proper i18n in UI changes
  - Failing to suggest breaking up large PRs
  - Using internal evaluation terminology in public comments
  - Not providing actionable suggestions for improvements
  - Reviewing only the diff without local context
  - Making assumptions instead of asking clarifying questions about potential intentions
  - Forgetting to link to specific lines with full GitHub URLs
  - Not presenting findings in a clear numbered list format
  - Failing to offer the task creation option for addressing suggestions
  - Creating tasks without specific context or file references
  - Choosing inappropriate modes when creating tasks for suggestions
  - Not updating the todo list after completing each step
  - Forgetting that GitHub CLI has limited support for inline review comments
  - Not including --repo flag when using gh commands for non-default repositories
</common_mistakes_to_avoid>