(()=>{var e={};e.id=974,e.ids=[974],e.modules={1918:(e,t)=>{"use strict";function r(e){return null!==e&&"object"==typeof e&&"then"in e&&"function"==typeof e.then}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isThenable",{enumerable:!0,get:function(){return r}})},2227:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"assignLocation",{enumerable:!0,get:function(){return l}});let n=r(2513);function l(e,t){if(e.startsWith(".")){let r=t.origin+t.pathname;return new URL((r.endsWith("/")?r:r+"/")+e)}return new URL((0,n.addBasePath)(e),t.href)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2327:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return h},MiddlewareNotFoundError:function(){return b},MissingStaticPage:function(){return v},NormalizeError:function(){return y},PageNotFoundError:function(){return g},SP:function(){return f},ST:function(){return p},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return i},getLocationOrigin:function(){return o},getURL:function(){return u},isAbsoluteUrl:function(){return a},isResSent:function(){return s},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return _}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,l=Array(n),a=0;a<n;a++)l[a]=arguments[a];return r||(r=!0,t=e(...l)),t}}let l=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>l.test(e);function o(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function u(){let{href:e}=window.location,t=o();return e.substring(t.length)}function i(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function s(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&s(r))return n;if(!n)throw Object.defineProperty(Error('"'+i(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let f="undefined"!=typeof performance,p=f&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class y extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class v extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class b extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function _(e){return JSON.stringify({message:e.message,stack:e.stack})}},2513:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addBasePath",{enumerable:!0,get:function(){return a}});let n=r(71832),l=r(64172);function a(e,t){return(0,l.normalizePathTrailingSlash)((0,n.addPathPrefix)(e,""))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3510:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{addRefreshMarkerToActiveParallelSegments:function(){return function e(t,r){let[n,l,,o]=t;for(let u in n.includes(a.PAGE_SEGMENT_KEY)&&"refresh"!==o&&(t[2]=r,t[3]="refresh"),l)e(l[u],r)}},refreshInactiveParallelSegments:function(){return o}});let n=r(95866),l=r(23702),a=r(95983);async function o(e){let t=new Set;await u({...e,rootTree:e.updatedTree,fetchedSegments:t})}async function u(e){let{state:t,updatedTree:r,updatedCache:a,includeNextUrl:o,fetchedSegments:i,rootTree:s=r,canonicalUrl:c}=e,[,d,f,p]=r,h=[];if(f&&f!==c&&"refresh"===p&&!i.has(f)){i.add(f);let e=(0,l.fetchServerResponse)(new URL(f,location.origin),{flightRouterState:[s[0],s[1],s[2],"refetch"],nextUrl:o?t.nextUrl:null}).then(e=>{let{flightData:t}=e;if("string"!=typeof t)for(let e of t)(0,n.applyFlightData)(a,a,e)});h.push(e)}for(let e in d){let r=u({state:t,updatedTree:d[e],updatedCache:a,includeNextUrl:o,fetchedSegments:i,rootTree:s,canonicalUrl:c});h.push(r)}await Promise.all(h)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4574:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return function e(t,r,a){let o=a.length<=2,[u,i]=a,s=(0,n.createRouterCacheKey)(i),c=r.parallelRoutes.get(u);if(!c)return;let d=t.parallelRoutes.get(u);if(d&&d!==c||(d=new Map(c),t.parallelRoutes.set(u,d)),o){d.delete(s);return}let f=c.get(s),p=d.get(s);p&&f&&(p===f&&(p={lazyData:p.lazyData,rsc:p.rsc,prefetchRsc:p.prefetchRsc,head:p.head,prefetchHead:p.prefetchHead,parallelRoutes:new Map(p.parallelRoutes)},d.set(s,p)),e(p,f,(0,l.getNextFlightSegmentPath)(a)))}}});let n=r(53789),l=r(26065);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5302:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTML_LIMITED_BOT_UA_RE:function(){return n.HTML_LIMITED_BOT_UA_RE},HTML_LIMITED_BOT_UA_RE_STRING:function(){return a},getBotType:function(){return i},isBot:function(){return u}});let n=r(63374),l=/Googlebot|Google-PageRenderer|AdsBot-Google|googleweblight|Storebot-Google/i,a=n.HTML_LIMITED_BOT_UA_RE.source;function o(e){return n.HTML_LIMITED_BOT_UA_RE.test(e)}function u(e){return l.test(e)||o(e)}function i(e){return l.test(e)?"dom":o(e)?"html":void 0}},7029:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return a},formatWithValidation:function(){return u},urlObjectKeys:function(){return o}});let n=r(24841)._(r(83221)),l=/https?|ftp|gopher|file/;function a(e){let{auth:t,hostname:r}=e,a=e.protocol||"",o=e.pathname||"",u=e.hash||"",i=e.query||"",s=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?s=t+e.host:r&&(s=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(s+=":"+e.port)),i&&"object"==typeof i&&(i=String(n.urlQueryToSearchParams(i)));let c=e.search||i&&"?"+i||"";return a&&!a.endsWith(":")&&(a+=":"),e.slashes||(!a||l.test(a))&&!1!==s?(s="//"+(s||""),o&&"/"!==o[0]&&(o="/"+o)):s||(s=""),u&&"#"!==u[0]&&(u="#"+u),c&&"?"!==c[0]&&(c="?"+c),""+a+s+(o=o.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+u}let o=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function u(e){return a(e)}},9784:(e,t,r)=>{"use strict";function n(e){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeBasePath",{enumerable:!0,get:function(){return n}}),r(98526),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},10055:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverActionReducer",{enumerable:!0,get:function(){return w}});let n=r(59254),l=r(17726),a=r(68205),o=r(57164),u=r(2227),i=r(66029),s=r(28206),c=r(20740),d=r(78944),f=r(39105),p=r(79130),h=r(52554),y=r(21484),g=r(64887),v=r(3510),b=r(26065),_=r(25821),m=r(32814),P=r(77636),R=r(9784),j=r(98526),E=r(21420);r(90727);let{createFromFetch:O,createTemporaryReferenceSet:x,encodeReply:T}=r(68027);async function M(e,t,r){let o,i,{actionId:s,actionArgs:c}=r,d=x(),f=(0,E.extractInfoFromServerReferenceId)(s),p="use-cache"===f.type?(0,E.omitUnusedArgs)(c,f):c,h=await T(p,{temporaryReferences:d}),y=await fetch("",{method:"POST",headers:{Accept:a.RSC_CONTENT_TYPE_HEADER,[a.ACTION_HEADER]:s,[a.NEXT_ROUTER_STATE_TREE_HEADER]:encodeURIComponent(JSON.stringify(e.tree)),...t?{[a.NEXT_URL]:t}:{}},body:h}),g=y.headers.get("x-action-redirect"),[v,_]=(null==g?void 0:g.split(";"))||[];switch(_){case"push":o=m.RedirectType.push;break;case"replace":o=m.RedirectType.replace;break;default:o=void 0}let P=!!y.headers.get(a.NEXT_IS_PRERENDER_HEADER);try{let e=JSON.parse(y.headers.get("x-action-revalidated")||"[[],0,0]");i={paths:e[0]||[],tag:!!e[1],cookie:e[2]}}catch(e){i={paths:[],tag:!1,cookie:!1}}let R=v?(0,u.assignLocation)(v,new URL(e.canonicalUrl,window.location.href)):void 0,j=y.headers.get("content-type");if(null==j?void 0:j.startsWith(a.RSC_CONTENT_TYPE_HEADER)){let e=await O(Promise.resolve(y),{callServer:n.callServer,findSourceMapURL:l.findSourceMapURL,temporaryReferences:d});return v?{actionFlightData:(0,b.normalizeFlightData)(e.f),redirectLocation:R,redirectType:o,revalidatedParts:i,isPrerender:P}:{actionResult:e.a,actionFlightData:(0,b.normalizeFlightData)(e.f),redirectLocation:R,redirectType:o,revalidatedParts:i,isPrerender:P}}if(y.status>=400)throw Object.defineProperty(Error("text/plain"===j?await y.text():"An unexpected response was received from the server."),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return{redirectLocation:R,redirectType:o,revalidatedParts:i,isPrerender:P}}function w(e,t){let{resolve:r,reject:n}=t,l={},a=e.tree;l.preserveCustomHistoryState=!1;let u=e.nextUrl&&(0,y.hasInterceptionRouteInCurrentTree)(e.tree)?e.nextUrl:null;return M(e,u,t).then(async y=>{let b,{actionResult:E,actionFlightData:O,redirectLocation:x,redirectType:T,isPrerender:M,revalidatedParts:w}=y;if(x&&(T===m.RedirectType.replace?(e.pushRef.pendingPush=!1,l.pendingPush=!1):(e.pushRef.pendingPush=!0,l.pendingPush=!0),l.canonicalUrl=b=(0,i.createHrefFromUrl)(x,!1)),!O)return(r(E),x)?(0,s.handleExternalUrl)(e,l,x.href,e.pushRef.pendingPush):e;if("string"==typeof O)return r(E),(0,s.handleExternalUrl)(e,l,O,e.pushRef.pendingPush);let S=w.paths.length>0||w.tag||w.cookie;for(let n of O){let{tree:o,seedData:i,head:f,isRootRender:y}=n;if(!y)return console.log("SERVER ACTION APPLY FAILED"),r(E),e;let _=(0,c.applyRouterStatePatchToTree)([""],a,o,b||e.canonicalUrl);if(null===_)return r(E),(0,g.handleSegmentMismatch)(e,t,o);if((0,d.isNavigatingToNewRootLayout)(a,_))return r(E),(0,s.handleExternalUrl)(e,l,b||e.canonicalUrl,e.pushRef.pendingPush);if(null!==i){let t=i[1],r=(0,h.createEmptyCacheNode)();r.rsc=t,r.prefetchRsc=null,r.loading=i[3],(0,p.fillLazyItemsTillLeafWithHead)(r,void 0,o,i,f,void 0),l.cache=r,l.prefetchCache=new Map,S&&await (0,v.refreshInactiveParallelSegments)({state:e,updatedTree:_,updatedCache:r,includeNextUrl:!!u,canonicalUrl:l.canonicalUrl||e.canonicalUrl})}l.patchedTree=_,a=_}return x&&b?(S||((0,P.createSeededPrefetchCacheEntry)({url:x,data:{flightData:O,canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1},tree:e.tree,prefetchCache:e.prefetchCache,nextUrl:e.nextUrl,kind:M?o.PrefetchKind.FULL:o.PrefetchKind.AUTO}),l.prefetchCache=e.prefetchCache),n((0,_.getRedirectError)((0,j.hasBasePath)(b)?(0,R.removeBasePath)(b):b,T||m.RedirectType.push))):r(E),(0,f.handleMutable)(e,l)},t=>(n(t),e))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13964:(e,t,r)=>{Promise.resolve().then(r.bind(r,99229))},18052:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{mountLinkInstance:function(){return s},onLinkVisibilityChanged:function(){return d},onNavigationIntent:function(){return f},pingVisibleLinks:function(){return h},unmountLinkInstance:function(){return c}}),r(65032);let n=r(52554),l=r(57164),a=r(90727),o="function"==typeof WeakMap?new WeakMap:new Map,u=new Set,i="function"==typeof IntersectionObserver?new IntersectionObserver(function(e){for(let t of e){let e=t.intersectionRatio>0;d(t.target,e)}},{rootMargin:"200px"}):null;function s(e,t,r,l){let a=null;try{if(a=(0,n.createPrefetchURL)(t),null===a)return}catch(e){("function"==typeof reportError?reportError:console.error)("Cannot prefetch '"+t+"' because it cannot be converted to a URL.");return}let u={prefetchHref:a.href,router:r,kind:l,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1};void 0!==o.get(e)&&c(e),o.set(e,u),null!==i&&i.observe(e)}function c(e){let t=o.get(e);if(void 0!==t){o.delete(e),u.delete(t);let r=t.prefetchTask;null!==r&&(0,a.cancelPrefetchTask)(r)}null!==i&&i.unobserve(e)}function d(e,t){let r=o.get(e);void 0!==r&&(r.isVisible=t,t?u.add(r):u.delete(r),p(r))}function f(e){let t=o.get(e);void 0!==t&&void 0!==t&&(t.wasHoveredOrTouched=!0,p(t))}function p(e){let t=e.prefetchTask;if(!e.isVisible){null!==t&&(0,a.cancelPrefetchTask)(t);return}}function h(e,t){let r=(0,a.getCurrentCacheVersion)();for(let n of u){let o=n.prefetchTask;if(null!==o&&n.cacheVersion===r&&o.key.nextUrl===e&&o.treeAtTimeOfPrefetch===t)continue;null!==o&&(0,a.cancelPrefetchTask)(o);let u=(0,a.createCacheKey)(n.prefetchHref,e),i=n.wasHoveredOrTouched?a.PrefetchPriority.Intent:a.PrefetchPriority.Default;n.prefetchTask=(0,a.schedulePrefetchTask)(u,t,n.kind===l.PrefetchKind.FULL,i),n.cacheVersion=(0,a.getCurrentCacheVersion)()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20740:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyRouterStatePatchToTree",{enumerable:!0,get:function(){return function e(t,r,n,i){let s,[c,d,f,p,h]=r;if(1===t.length){let e=u(r,n);return(0,o.addRefreshMarkerToActiveParallelSegments)(e,i),e}let[y,g]=t;if(!(0,a.matchSegment)(y,c))return null;if(2===t.length)s=u(d[g],n);else if(null===(s=e((0,l.getNextFlightSegmentPath)(t),d[g],n,i)))return null;let v=[t[0],{...d,[g]:s},f,p];return h&&(v[4]=!0),(0,o.addRefreshMarkerToActiveParallelSegments)(v,i),v}}});let n=r(95983),l=r(26065),a=r(97047),o=r(3510);function u(e,t){let[r,l]=e,[o,i]=t;if(o===n.DEFAULT_SEGMENT_KEY&&r!==n.DEFAULT_SEGMENT_KEY)return e;if((0,a.matchSegment)(r,o)){let t={};for(let e in l)void 0!==i[e]?t[e]=u(l[e],i[e]):t[e]=l[e];for(let e in i)t[e]||(t[e]=i[e]);let n=[r,t];return e[2]&&(n[2]=e[2]),e[3]&&(n[3]=e[3]),e[4]&&(n[4]=e[4]),n}return t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},20774:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"clearCacheNodeDataForSegmentPath",{enumerable:!0,get:function(){return function e(t,r,a){let o=a.length<=2,[u,i]=a,s=(0,l.createRouterCacheKey)(i),c=r.parallelRoutes.get(u),d=t.parallelRoutes.get(u);d&&d!==c||(d=new Map(c),t.parallelRoutes.set(u,d));let f=null==c?void 0:c.get(s),p=d.get(s);if(o){p&&p.lazyData&&p!==f||d.set(s,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null});return}if(!p||!f){p||d.set(s,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null});return}return p===f&&(p={lazyData:p.lazyData,rsc:p.rsc,prefetchRsc:p.prefetchRsc,head:p.head,prefetchHead:p.prefetchHead,parallelRoutes:new Map(p.parallelRoutes),loading:p.loading},d.set(s,p)),e(p,f,(0,n.getNextFlightSegmentPath)(a))}}});let n=r(26065),l=r(53789);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},20942:(e,t,r)=>{"use strict";r.d(t,{Runs:()=>n});let n=(0,r(87741).registerClientReference)(function(){throw Error("Attempted to call Runs() from the server but Runs is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\personale\\Roo-Code\\apps\\web-evals\\src\\components\\home\\runs.tsx","Runs")},21420:(e,t)=>{"use strict";function r(e){let t=parseInt(e.slice(0,2),16),r=t>>1&63,n=Array(6);for(let e=0;e<6;e++){let t=r>>5-e&1;n[e]=1===t}return{type:1==(t>>7&1)?"use-cache":"server-action",usedArgs:n,hasRestArgs:1==(1&t)}}function n(e,t){let r=Array(e.length);for(let n=0;n<e.length;n++)(n<6&&t.usedArgs[n]||n>=6&&t.hasRestArgs)&&(r[n]=e[n]);return r}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{extractInfoFromServerReferenceId:function(){return r},omitUnusedArgs:function(){return n}})},21820:e=>{"use strict";e.exports=require("os")},25252:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"reducer",{enumerable:!0,get:function(){return n}}),r(57164),r(28206),r(42161),r(82889),r(88672),r(73962),r(70818),r(10055);let n=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},27910:e=>{"use strict";e.exports=require("stream")},28206:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleExternalUrl:function(){return _},navigateReducer:function(){return function e(t,r){let{url:P,isExternalUrl:R,navigateType:j,shouldScroll:E,allowAliasing:O}=r,x={},{hash:T}=P,M=(0,l.createHrefFromUrl)(P),w="push"===j;if((0,g.prunePrefetchCache)(t.prefetchCache),x.preserveCustomHistoryState=!1,x.pendingPush=w,R)return _(t,x,P.toString(),w);if(document.getElementById("__next-page-redirect"))return _(t,x,M,w);let S=(0,g.getOrCreatePrefetchCacheEntry)({url:P,nextUrl:t.nextUrl,tree:t.tree,prefetchCache:t.prefetchCache,allowAliasing:O}),{treeAtTimeOfPrefetch:C,data:A}=S;return f.prefetchQueue.bump(A),A.then(f=>{let{flightData:g,canonicalUrl:R,postponed:j}=f,O=!1;if(S.lastUsedTime||(S.lastUsedTime=Date.now(),O=!0),S.aliased){let n=(0,b.handleAliasedPrefetchEntry)(t,g,P,x);return!1===n?e(t,{...r,allowAliasing:!1}):n}if("string"==typeof g)return _(t,x,g,w);let A=R?(0,l.createHrefFromUrl)(R):M;if(T&&t.canonicalUrl.split("#",1)[0]===A.split("#",1)[0])return x.onlyHashChange=!0,x.canonicalUrl=A,x.shouldScroll=E,x.hashFragment=T,x.scrollableSegments=[],(0,c.handleMutable)(t,x);let N=t.tree,U=t.cache,k=[];for(let e of g){let{pathToSegment:r,seedData:l,head:c,isHeadPartial:f,isRootRender:g}=e,b=e.tree,R=["",...r],E=(0,o.applyRouterStatePatchToTree)(R,N,b,M);if(null===E&&(E=(0,o.applyRouterStatePatchToTree)(R,C,b,M)),null!==E){if(l&&g&&j){let e=(0,y.startPPRNavigation)(U,N,b,l,c,f,!1,k);if(null!==e){if(null===e.route)return _(t,x,M,w);E=e.route;let r=e.node;null!==r&&(x.cache=r);let l=e.dynamicRequestTree;if(null!==l){let r=(0,n.fetchServerResponse)(P,{flightRouterState:l,nextUrl:t.nextUrl});(0,y.listenForDynamicRequest)(e,r)}}else E=b}else{if((0,i.isNavigatingToNewRootLayout)(N,E))return _(t,x,M,w);let n=(0,p.createEmptyCacheNode)(),l=!1;for(let t of(S.status!==s.PrefetchCacheEntryStatus.stale||O?l=(0,d.applyFlightData)(U,n,e,S):(l=function(e,t,r,n){let l=!1;for(let a of(e.rsc=t.rsc,e.prefetchRsc=t.prefetchRsc,e.loading=t.loading,e.parallelRoutes=new Map(t.parallelRoutes),m(n).map(e=>[...r,...e])))(0,v.clearCacheNodeDataForSegmentPath)(e,t,a),l=!0;return l}(n,U,r,b),S.lastUsedTime=Date.now()),(0,u.shouldHardNavigate)(R,N)?(n.rsc=U.rsc,n.prefetchRsc=U.prefetchRsc,(0,a.invalidateCacheBelowFlightSegmentPath)(n,U,r),x.cache=n):l&&(x.cache=n,U=n),m(b))){let e=[...r,...t];e[e.length-1]!==h.DEFAULT_SEGMENT_KEY&&k.push(e)}}N=E}}return x.patchedTree=N,x.canonicalUrl=A,x.scrollableSegments=k,x.hashFragment=T,x.shouldScroll=E,(0,c.handleMutable)(t,x)},()=>t)}}});let n=r(23702),l=r(66029),a=r(4574),o=r(20740),u=r(76765),i=r(78944),s=r(57164),c=r(39105),d=r(95866),f=r(73962),p=r(52554),h=r(95983),y=r(57570),g=r(77636),v=r(20774),b=r(70337);function _(e,t,r,n){return t.mpaNavigation=!0,t.canonicalUrl=r,t.pendingPush=n,t.scrollableSegments=void 0,(0,c.handleMutable)(e,t)}function m(e){let t=[],[r,n]=e;if(0===Object.keys(n).length)return[[r]];for(let[e,l]of Object.entries(n))for(let n of m(l))""===r?t.push([e,...n]):t.push([r,e,...n]);return t}r(90727),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32844:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{computeChangedPath:function(){return c},extractPathFromFlightRouterState:function(){return s},getSelectedParams:function(){return function e(t,r){for(let n of(void 0===r&&(r={}),Object.values(t[1]))){let t=n[0],a=Array.isArray(t),o=a?t[1]:t;!(!o||o.startsWith(l.PAGE_SEGMENT_KEY))&&(a&&("c"===t[2]||"oc"===t[2])?r[t[0]]=t[1].split("/"):a&&(r[t[0]]=t[1]),r=e(n,r))}return r}}});let n=r(57849),l=r(95983),a=r(97047),o=e=>"/"===e[0]?e.slice(1):e,u=e=>"string"==typeof e?"children"===e?"":e:(0,e[1]);function i(e){return e.reduce((e,t)=>""===(t=o(t))||(0,l.isGroupSegment)(t)?e:e+"/"+t,"")||"/"}function s(e){var t;let r=Array.isArray(e[0])?e[0][1]:e[0];if(r===l.DEFAULT_SEGMENT_KEY||n.INTERCEPTION_ROUTE_MARKERS.some(e=>r.startsWith(e)))return;if(r.startsWith(l.PAGE_SEGMENT_KEY))return"";let a=[u(r)],o=null!=(t=e[1])?t:{},c=o.children?s(o.children):void 0;if(void 0!==c)a.push(c);else for(let[e,t]of Object.entries(o)){if("children"===e)continue;let r=s(t);void 0!==r&&a.push(r)}return i(a)}function c(e,t){let r=function e(t,r){let[l,o]=t,[i,c]=r,d=u(l),f=u(i);if(n.INTERCEPTION_ROUTE_MARKERS.some(e=>d.startsWith(e)||f.startsWith(e)))return"";if(!(0,a.matchSegment)(l,i)){var p;return null!=(p=s(r))?p:""}for(let t in o)if(c[t]){let r=e(o[t],c[t]);if(null!==r)return u(i)+"/"+r}return null}(e,t);return null==r||"/"===r?r:i(r.split("/"))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35864:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AppRouterAnnouncer",{enumerable:!0,get:function(){return o}});let n=r(44508),l=r(1117),a="next-route-announcer";function o(e){let{tree:t}=e,[r,o]=(0,n.useState)(null);(0,n.useEffect)(()=>(o(function(){var e;let t=document.getElementsByName(a)[0];if(null==t?void 0:null==(e=t.shadowRoot)?void 0:e.childNodes[0])return t.shadowRoot.childNodes[0];{let e=document.createElement(a);e.style.cssText="position:absolute";let t=document.createElement("div");return t.ariaLive="assertive",t.id="__next-route-announcer__",t.role="alert",t.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal",e.attachShadow({mode:"open"}).appendChild(t),document.body.appendChild(e),t}}()),()=>{let e=document.getElementsByTagName(a)[0];(null==e?void 0:e.isConnected)&&document.body.removeChild(e)}),[]);let[u,i]=(0,n.useState)(""),s=(0,n.useRef)(void 0);return(0,n.useEffect)(()=>{let e="";if(document.title)e=document.title;else{let t=document.querySelector("h1");t&&(e=t.innerText||t.textContent||"")}void 0!==s.current&&s.current!==e&&i(e),s.current=e},[t]),r?(0,l.createPortal)(u,r):null}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},37675:(e,t)=>{"use strict";function r(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return r}})},39105:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleMutable",{enumerable:!0,get:function(){return a}});let n=r(32844);function l(e){return void 0!==e}function a(e,t){var r,a;let o=null==(r=t.shouldScroll)||r,u=e.nextUrl;if(l(t.patchedTree)){let r=(0,n.computeChangedPath)(e.tree,t.patchedTree);r?u=r:u||(u=e.canonicalUrl)}return{canonicalUrl:l(t.canonicalUrl)?t.canonicalUrl===e.canonicalUrl?e.canonicalUrl:t.canonicalUrl:e.canonicalUrl,pushRef:{pendingPush:l(t.pendingPush)?t.pendingPush:e.pushRef.pendingPush,mpaNavigation:l(t.mpaNavigation)?t.mpaNavigation:e.pushRef.mpaNavigation,preserveCustomHistoryState:l(t.preserveCustomHistoryState)?t.preserveCustomHistoryState:e.pushRef.preserveCustomHistoryState},focusAndScrollRef:{apply:!!o&&(!!l(null==t?void 0:t.scrollableSegments)||e.focusAndScrollRef.apply),onlyHashChange:t.onlyHashChange||!1,hashFragment:o?t.hashFragment&&""!==t.hashFragment?decodeURIComponent(t.hashFragment.slice(1)):e.focusAndScrollRef.hashFragment:null,segmentPaths:o?null!=(a=null==t?void 0:t.scrollableSegments)?a:e.focusAndScrollRef.segmentPaths:[]},cache:t.cache?t.cache:e.cache,prefetchCache:t.prefetchCache?t.prefetchCache:e.prefetchCache,tree:l(t.patchedTree)?t.patchedTree:e.tree,nextUrl:u}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},40644:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return h}});let n=r(94942),l=r(3641),a=n._(r(44508)),o=r(7029),u=r(8052),i=r(57164),s=r(90840),c=r(2327),d=r(2513);r(87126);let f=r(18052);function p(e){return"string"==typeof e?e:(0,o.formatUrl)(e)}let h=a.default.forwardRef(function(e,t){let r,n,{href:o,as:h,children:y,prefetch:g=null,passHref:v,replace:b,shallow:_,scroll:m,onClick:P,onMouseEnter:R,onTouchStart:j,legacyBehavior:E=!1,...O}=e;r=y,E&&("string"==typeof r||"number"==typeof r)&&(r=(0,l.jsx)("a",{children:r}));let x=a.default.useContext(u.AppRouterContext),T=!1!==g,M=null===g?i.PrefetchKind.AUTO:i.PrefetchKind.FULL,{href:w,as:S}=a.default.useMemo(()=>{let e=p(o);return{href:e,as:h?p(h):e}},[o,h]);E&&(n=a.default.Children.only(r));let C=E?n&&"object"==typeof n&&n.ref:t,A=a.default.useCallback(e=>(T&&null!==x&&(0,f.mountLinkInstance)(e,w,x,M),()=>{(0,f.unmountLinkInstance)(e)}),[T,w,x,M]),N={ref:(0,s.useMergedRef)(A,C),onClick(e){E||"function"!=typeof P||P(e),E&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),x&&(e.defaultPrevented||function(e,t,r,n,l,o,u){let{nodeName:i}=e.currentTarget;!("A"===i.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e))&&(e.preventDefault(),a.default.startTransition(()=>{let e=null==u||u;"beforePopState"in t?t[l?"replace":"push"](r,n,{shallow:o,scroll:e}):t[l?"replace":"push"](n||r,{scroll:e})}))}(e,x,w,S,b,_,m))},onMouseEnter(e){E||"function"!=typeof R||R(e),E&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e),x&&T&&(0,f.onNavigationIntent)(e.currentTarget)},onTouchStart:function(e){E||"function"!=typeof j||j(e),E&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e),x&&T&&(0,f.onNavigationIntent)(e.currentTarget)}};return(0,c.isAbsoluteUrl)(S)?N.href=S:E&&!v&&("a"!==n.type||"href"in n.props)||(N.href=(0,d.addBasePath)(S)),E?a.default.cloneElement(n,N):(0,l.jsx)("a",{...O,...N,children:r})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},41704:(e,t)=>{"use strict";function r(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return r}})},42161:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverPatchReducer",{enumerable:!0,get:function(){return c}});let n=r(66029),l=r(20740),a=r(78944),o=r(28206),u=r(95866),i=r(39105),s=r(52554);function c(e,t){let{serverResponse:{flightData:r,canonicalUrl:c}}=t,d={};if(d.preserveCustomHistoryState=!1,"string"==typeof r)return(0,o.handleExternalUrl)(e,d,r,e.pushRef.pendingPush);let f=e.tree,p=e.cache;for(let t of r){let{segmentPath:r,tree:i}=t,h=(0,l.applyRouterStatePatchToTree)(["",...r],f,i,e.canonicalUrl);if(null===h)return e;if((0,a.isNavigatingToNewRootLayout)(f,h))return(0,o.handleExternalUrl)(e,d,e.canonicalUrl,e.pushRef.pendingPush);let y=c?(0,n.createHrefFromUrl)(c):void 0;y&&(d.canonicalUrl=y);let g=(0,s.createEmptyCacheNode)();(0,u.applyFlightData)(p,g,t),d.patchedTree=h,d.cache=g,p=g,f=h}return(0,i.handleMutable)(e,d)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},50412:(e,t,r)=>{Promise.resolve().then(r.bind(r,20942))},52554:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createEmptyCacheNode:function(){return S},createPrefetchURL:function(){return M},default:function(){return U}});let n=r(24841),l=r(3641),a=n._(r(44508)),o=r(8052),u=r(57164),i=r(66029),s=r(81955),c=r(67835),d=n._(r(61430)),f=r(5302),p=r(2513),h=r(35864),y=r(55660),g=r(97439),v=r(15120),b=r(9784),_=r(98526),m=r(32844),P=r(39626),R=r(59254);r(90727);let j=r(25821),E=r(32814),O=r(73962);r(18052);let x={};function T(e){return e.origin!==window.location.origin}function M(e){let t;if((0,f.isBot)(window.navigator.userAgent))return null;try{t=new URL((0,p.addBasePath)(e),window.location.href)}catch(t){throw Object.defineProperty(Error("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),"__NEXT_ERROR_CODE",{value:"E234",enumerable:!1,configurable:!0})}return T(t)?null:t}function w(e){let{appRouterState:t}=e;return(0,a.useInsertionEffect)(()=>{let{tree:e,pushRef:r,canonicalUrl:n}=t,l={...r.preserveCustomHistoryState?window.history.state:{},__NA:!0,__PRIVATE_NEXTJS_INTERNALS_TREE:e};r.pendingPush&&(0,i.createHrefFromUrl)(new URL(window.location.href))!==n?(r.pendingPush=!1,window.history.pushState(l,"",n)):window.history.replaceState(l,"",n)},[t]),(0,a.useEffect)(()=>{},[t.nextUrl,t.tree]),null}function S(){return{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null}}function C(e){null==e&&(e={});let t=window.history.state,r=null==t?void 0:t.__NA;r&&(e.__NA=r);let n=null==t?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;return n&&(e.__PRIVATE_NEXTJS_INTERNALS_TREE=n),e}function A(e){let{headCacheNode:t}=e,r=null!==t?t.head:null,n=null!==t?t.prefetchHead:null,l=null!==n?n:r;return(0,a.useDeferredValue)(r,l)}function N(e){let t,{actionQueue:r,assetPrefix:n,globalError:i}=e,[f,P]=(0,c.useReducer)(r),{canonicalUrl:S}=(0,c.useUnwrapState)(f),{searchParams:N,pathname:U}=(0,a.useMemo)(()=>{let e=new URL(S,"http://n");return{searchParams:e.searchParams,pathname:(0,_.hasBasePath)(e.pathname)?(0,b.removeBasePath)(e.pathname):e.pathname}},[S]),k=(0,a.useCallback)(e=>{let{previousTree:t,serverResponse:r}=e;(0,a.startTransition)(()=>{P({type:u.ACTION_SERVER_PATCH,previousTree:t,serverResponse:r})})},[P]),I=(0,a.useCallback)((e,t,r)=>{let n=new URL((0,p.addBasePath)(e),location.href);return P({type:u.ACTION_NAVIGATE,url:n,isExternalUrl:T(n),locationSearch:location.search,shouldScroll:null==r||r,navigateType:t,allowAliasing:!0})},[P]);(0,R.useServerActionDispatcher)(P);let D=(0,a.useMemo)(()=>({back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(e,t)=>{let n=M(e);if(null!==n){var l;(0,O.prefetchReducer)(r.state,{type:u.ACTION_PREFETCH,url:n,kind:null!=(l=null==t?void 0:t.kind)?l:u.PrefetchKind.FULL})}},replace:(e,t)=>{void 0===t&&(t={}),(0,a.startTransition)(()=>{var r;I(e,"replace",null==(r=t.scroll)||r)})},push:(e,t)=>{void 0===t&&(t={}),(0,a.startTransition)(()=>{var r;I(e,"push",null==(r=t.scroll)||r)})},refresh:()=>{(0,a.startTransition)(()=>{P({type:u.ACTION_REFRESH,origin:window.location.origin})})},hmrRefresh:()=>{throw Object.defineProperty(Error("hmrRefresh can only be used in development mode. Please use refresh instead."),"__NEXT_ERROR_CODE",{value:"E485",enumerable:!1,configurable:!0})}}),[r,P,I]);(0,a.useEffect)(()=>{window.next&&(window.next.router=D)},[D]),(0,a.useEffect)(()=>{function e(e){var t;e.persisted&&(null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE)&&(x.pendingMpaPath=void 0,P({type:u.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE}))}return window.addEventListener("pageshow",e),()=>{window.removeEventListener("pageshow",e)}},[P]),(0,a.useEffect)(()=>{function e(e){let t="reason"in e?e.reason:e.error;if((0,E.isRedirectError)(t)){e.preventDefault();let r=(0,j.getURLFromRedirectError)(t);(0,j.getRedirectTypeFromError)(t)===E.RedirectType.push?D.push(r,{}):D.replace(r,{})}}return window.addEventListener("error",e),window.addEventListener("unhandledrejection",e),()=>{window.removeEventListener("error",e),window.removeEventListener("unhandledrejection",e)}},[D]);let{pushRef:F}=(0,c.useUnwrapState)(f);if(F.mpaNavigation){if(x.pendingMpaPath!==S){let e=window.location;F.pendingPush?e.assign(S):e.replace(S),x.pendingMpaPath=S}(0,a.use)(v.unresolvedThenable)}(0,a.useEffect)(()=>{let e=window.history.pushState.bind(window.history),t=window.history.replaceState.bind(window.history),r=e=>{var t;let r=window.location.href,n=null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;(0,a.startTransition)(()=>{P({type:u.ACTION_RESTORE,url:new URL(null!=e?e:r,r),tree:n})})};window.history.pushState=function(t,n,l){return(null==t?void 0:t.__NA)||(null==t?void 0:t._N)||(t=C(t),l&&r(l)),e(t,n,l)},window.history.replaceState=function(e,n,l){return(null==e?void 0:e.__NA)||(null==e?void 0:e._N)||(e=C(e),l&&r(l)),t(e,n,l)};let n=e=>{if(e.state){if(!e.state.__NA){window.location.reload();return}(0,a.startTransition)(()=>{P({type:u.ACTION_RESTORE,url:new URL(window.location.href),tree:e.state.__PRIVATE_NEXTJS_INTERNALS_TREE})})}};return window.addEventListener("popstate",n),()=>{window.history.pushState=e,window.history.replaceState=t,window.removeEventListener("popstate",n)}},[P]);let{cache:H,tree:K,nextUrl:z,focusAndScrollRef:B}=(0,c.useUnwrapState)(f),q=(0,a.useMemo)(()=>(0,g.findHeadInCache)(H,K[1]),[H,K]),G=(0,a.useMemo)(()=>(0,m.getSelectedParams)(K),[K]),V=(0,a.useMemo)(()=>({parentTree:K,parentCacheNode:H,parentSegmentPath:null,url:S}),[K,H,S]),W=(0,a.useMemo)(()=>({changeByServerResponse:k,tree:K,focusAndScrollRef:B,nextUrl:z}),[k,K,B,z]);if(null!==q){let[e,r]=q;t=(0,l.jsx)(A,{headCacheNode:e},r)}else t=null;let Y=(0,l.jsxs)(y.RedirectBoundary,{children:[t,H.rsc,(0,l.jsx)(h.AppRouterAnnouncer,{tree:K})]});return Y=(0,l.jsx)(d.ErrorBoundary,{errorComponent:i[0],errorStyles:i[1],children:Y}),(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(w,{appRouterState:(0,c.useUnwrapState)(f)}),(0,l.jsx)(L,{}),(0,l.jsx)(s.PathParamsContext.Provider,{value:G,children:(0,l.jsx)(s.PathnameContext.Provider,{value:U,children:(0,l.jsx)(s.SearchParamsContext.Provider,{value:N,children:(0,l.jsx)(o.GlobalLayoutRouterContext.Provider,{value:W,children:(0,l.jsx)(o.AppRouterContext.Provider,{value:D,children:(0,l.jsx)(o.LayoutRouterContext.Provider,{value:V,children:Y})})})})})})]})}function U(e){let{actionQueue:t,globalErrorComponentAndStyles:[r,n],assetPrefix:a}=e;return(0,P.useNavFailureHandler)(),(0,l.jsx)(d.ErrorBoundary,{errorComponent:d.default,children:(0,l.jsx)(N,{actionQueue:t,assetPrefix:a,globalError:[r,n]})})}let k=new Set,I=new Set;function L(){let[,e]=a.default.useState(0),t=k.size;return(0,a.useEffect)(()=>{let r=()=>e(e=>e+1);return I.add(r),t!==k.size&&r(),()=>{I.delete(r)}},[t,e]),[...k].map((e,t)=>(0,l.jsx)("link",{rel:"stylesheet",href:""+e,precedence:"next"},t))}globalThis._N_E_STYLE_LOAD=function(e){let t=k.size;return k.add(e),k.size!==t&&I.forEach(e=>e()),Promise.resolve()},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},55259:(e,t,r)=>{"use strict";function n(e,t){if(!Object.prototype.hasOwnProperty.call(e,t))throw TypeError("attempted to use private field on non-instance");return e}r.r(t),r.d(t,{_:()=>n})},55511:e=>{"use strict";e.exports=require("crypto")},56561:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return l}});let n=r(37675);function l(e,t){if("string"!=typeof e)return!1;let{pathname:r}=(0,n.parsePath)(e);return r===t||r.startsWith(t+"/")}},57570:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{abortTask:function(){return p},listenForDynamicRequest:function(){return f},startPPRNavigation:function(){return i},updateCacheNodeOnPopstateRestoration:function(){return function e(t,r){let n=r[1],l=t.parallelRoutes,o=new Map(l);for(let t in n){let r=n[t],u=r[0],i=(0,a.createRouterCacheKey)(u),s=l.get(t);if(void 0!==s){let n=s.get(i);if(void 0!==n){let l=e(n,r),a=new Map(s);a.set(i,l),o.set(t,a)}}}let u=t.rsc,i=g(u)&&"pending"===u.status;return{lazyData:null,rsc:u,head:t.head,prefetchHead:i?t.prefetchHead:[null,null],prefetchRsc:i?t.prefetchRsc:null,loading:t.loading,parallelRoutes:o}}}});let n=r(95983),l=r(97047),a=r(53789),o=r(78944),u={route:null,node:null,dynamicRequestTree:null,children:null};function i(e,t,r,o,i,d,f,p){return function e(t,r,o,i,d,f,p,h,y,g){let v=r[1],b=o[1],_=null!==d?d[2]:null;i||!0!==o[4]||(i=!0);let m=t.parallelRoutes,P=new Map(m),R={},j=null,E=!1,O={};for(let t in b){let r,o=b[t],c=v[t],d=m.get(t),x=null!==_?_[t]:null,T=o[0],M=y.concat([t,T]),w=(0,a.createRouterCacheKey)(T),S=void 0!==c?c[0]:void 0,C=void 0!==d?d.get(w):void 0;if(null!==(r=T===n.DEFAULT_SEGMENT_KEY?void 0!==c?{route:c,node:null,dynamicRequestTree:null,children:null}:s(c,o,i,void 0!==x?x:null,f,p,M,g):h&&0===Object.keys(o[1]).length?s(c,o,i,void 0!==x?x:null,f,p,M,g):void 0!==c&&void 0!==S&&(0,l.matchSegment)(T,S)&&void 0!==C&&void 0!==c?e(C,c,o,i,x,f,p,h,M,g):s(c,o,i,void 0!==x?x:null,f,p,M,g))){if(null===r.route)return u;null===j&&(j=new Map),j.set(t,r);let e=r.node;if(null!==e){let r=new Map(d);r.set(w,e),P.set(t,r)}let n=r.route;R[t]=n;let l=r.dynamicRequestTree;null!==l?(E=!0,O[t]=l):O[t]=n}else R[t]=o,O[t]=o}if(null===j)return null;let x={lazyData:null,rsc:t.rsc,prefetchRsc:t.prefetchRsc,head:t.head,prefetchHead:t.prefetchHead,loading:t.loading,parallelRoutes:P};return{route:c(o,R),node:x,dynamicRequestTree:E?c(o,O):null,children:j}}(e,t,r,!1,o,i,d,f,[],p)}function s(e,t,r,n,l,i,s,f){return!r&&(void 0===e||(0,o.isNavigatingToNewRootLayout)(e,t))?u:function e(t,r,n,l,o,u){if(null===r)return d(t,null,n,l,o,u);let i=t[1],s=r[4],f=0===Object.keys(i).length;if(s||l&&f)return d(t,r,n,l,o,u);let p=r[2],h=new Map,y=new Map,g={},v=!1;if(f)u.push(o);else for(let t in i){let r=i[t],s=null!==p?p[t]:null,c=r[0],d=o.concat([t,c]),f=(0,a.createRouterCacheKey)(c),b=e(r,s,n,l,d,u);h.set(t,b);let _=b.dynamicRequestTree;null!==_?(v=!0,g[t]=_):g[t]=r;let m=b.node;if(null!==m){let e=new Map;e.set(f,m),y.set(t,e)}}return{route:t,node:{lazyData:null,rsc:r[1],prefetchRsc:null,head:f?n:null,prefetchHead:null,loading:r[3],parallelRoutes:y},dynamicRequestTree:v?c(t,g):null,children:h}}(t,n,l,i,s,f)}function c(e,t){let r=[e[0],t];return 2 in e&&(r[2]=e[2]),3 in e&&(r[3]=e[3]),4 in e&&(r[4]=e[4]),r}function d(e,t,r,n,l,o){let u=c(e,e[1]);return u[3]="refetch",{route:e,node:function e(t,r,n,l,o,u){let i=t[1],s=null!==r?r[2]:null,c=new Map;for(let t in i){let r=i[t],d=null!==s?s[t]:null,f=r[0],p=o.concat([t,f]),h=(0,a.createRouterCacheKey)(f),y=e(r,void 0===d?null:d,n,l,p,u),g=new Map;g.set(h,y),c.set(t,g)}let d=0===c.size;d&&u.push(o);let f=null!==r?r[1]:null,p=null!==r?r[3]:null;return{lazyData:null,parallelRoutes:c,prefetchRsc:void 0!==f?f:null,prefetchHead:d?n:[null,null],loading:void 0!==p?p:null,rsc:v(),head:d?v():null}}(e,t,r,n,l,o),dynamicRequestTree:u,children:null}}function f(e,t){t.then(t=>{let{flightData:r}=t;if("string"!=typeof r){for(let t of r){let{segmentPath:r,tree:n,seedData:o,head:u}=t;o&&function(e,t,r,n,o){let u=e;for(let e=0;e<t.length;e+=2){let r=t[e],n=t[e+1],a=u.children;if(null!==a){let e=a.get(r);if(void 0!==e){let t=e.route[0];if((0,l.matchSegment)(n,t)){u=e;continue}}}return}(function e(t,r,n,o){if(null===t.dynamicRequestTree)return;let u=t.children,i=t.node;if(null===u){null!==i&&(function e(t,r,n,o,u){let i=r[1],s=n[1],c=o[2],d=t.parallelRoutes;for(let t in i){let r=i[t],n=s[t],o=c[t],f=d.get(t),p=r[0],y=(0,a.createRouterCacheKey)(p),g=void 0!==f?f.get(y):void 0;void 0!==g&&(void 0!==n&&(0,l.matchSegment)(p,n[0])&&null!=o?e(g,r,n,o,u):h(r,g,null))}let f=t.rsc,p=o[1];null===f?t.rsc=p:g(f)&&f.resolve(p);let y=t.head;g(y)&&y.resolve(u)}(i,t.route,r,n,o),t.dynamicRequestTree=null);return}let s=r[1],c=n[2];for(let t in r){let r=s[t],n=c[t],a=u.get(t);if(void 0!==a){let t=a.route[0];if((0,l.matchSegment)(r[0],t)&&null!=n)return e(a,r,n,o)}}})(u,r,n,o)}(e,r,n,o,u)}p(e,null)}},t=>{p(e,t)})}function p(e,t){let r=e.node;if(null===r)return;let n=e.children;if(null===n)h(e.route,r,t);else for(let e of n.values())p(e,t);e.dynamicRequestTree=null}function h(e,t,r){let n=e[1],l=t.parallelRoutes;for(let e in n){let t=n[e],o=l.get(e);if(void 0===o)continue;let u=t[0],i=(0,a.createRouterCacheKey)(u),s=o.get(i);void 0!==s&&h(t,s,r)}let o=t.rsc;g(o)&&(null===r?o.resolve(null):o.reject(r));let u=t.head;g(u)&&u.resolve(null)}let y=Symbol();function g(e){return e&&e.tag===y}function v(){let e,t,r=new Promise((r,n)=>{e=r,t=n});return r.status="pending",r.resolve=t=>{"pending"===r.status&&(r.status="fulfilled",r.value=t,e(t))},r.reject=e=>{"pending"===r.status&&(r.status="rejected",r.reason=e,t(e))},r.tag=y,r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},59428:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fillCacheWithNewSubTreeData:function(){return i},fillCacheWithNewSubTreeDataButOnlyLoading:function(){return s}});let n=r(80394),l=r(79130),a=r(53789),o=r(95983);function u(e,t,r,u,i){let{segmentPath:s,seedData:c,tree:d,head:f}=r,p=e,h=t;for(let e=0;e<s.length;e+=2){let t=s[e],r=s[e+1],y=e===s.length-2,g=(0,a.createRouterCacheKey)(r),v=h.parallelRoutes.get(t);if(!v)continue;let b=p.parallelRoutes.get(t);b&&b!==v||(b=new Map(v),p.parallelRoutes.set(t,b));let _=v.get(g),m=b.get(g);if(y){if(c&&(!m||!m.lazyData||m===_)){let e=c[0],t=c[1],r=c[3];m={lazyData:null,rsc:i||e!==o.PAGE_SEGMENT_KEY?t:null,prefetchRsc:null,head:null,prefetchHead:null,loading:r,parallelRoutes:i&&_?new Map(_.parallelRoutes):new Map},_&&i&&(0,n.invalidateCacheByRouterState)(m,_,d),i&&(0,l.fillLazyItemsTillLeafWithHead)(m,_,d,c,f,u),b.set(g,m)}continue}m&&_&&(m===_&&(m={lazyData:m.lazyData,rsc:m.rsc,prefetchRsc:m.prefetchRsc,head:m.head,prefetchHead:m.prefetchHead,parallelRoutes:new Map(m.parallelRoutes),loading:m.loading},b.set(g,m)),p=m,h=_)}}function i(e,t,r,n){u(e,t,r,n,!0)}function s(e,t,r,n){u(e,t,r,n,!1)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},62188:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u,dynamic:()=>o});var n=r(90811),l=r(97978),a=r(20942);let o="force-dynamic";async function u(){let e=await (0,l.Ze)();return(0,n.jsx)(a.Runs,{runs:e})}},62248:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(72364).A)("rocket",[["path",{d:"M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z",key:"m3kijz"}],["path",{d:"m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z",key:"1fmvmk"}],["path",{d:"M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0",key:"1f8sc4"}],["path",{d:"M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5",key:"qeys4"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63374:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"HTML_LIMITED_BOT_UA_RE",{enumerable:!0,get:function(){return r}});let r=/Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview/i},64172:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return a}});let n=r(41704),l=r(37675),a=e=>{if(!e.startsWith("/"))return e;let{pathname:t,query:r,hash:a}=(0,l.parsePath)(e);return""+(0,n.removeTrailingSlash)(t)+r+a};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},64887:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSegmentMismatch",{enumerable:!0,get:function(){return l}});let n=r(28206);function l(e,t,r){return(0,n.handleExternalUrl)(e,{},e.canonicalUrl,!0)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},65032:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createMutableActionQueue:function(){return s},getCurrentAppRouterState:function(){return c}});let n=r(57164),l=r(25252),a=r(44508),o=r(1918);function u(e,t){null!==e.pending&&(e.pending=e.pending.next,null!==e.pending?i({actionQueue:e,action:e.pending,setState:t}):e.needsRefresh&&(e.needsRefresh=!1,e.dispatch({type:n.ACTION_REFRESH,origin:window.location.origin},t)))}async function i(e){let{actionQueue:t,action:r,setState:n}=e,l=t.state;t.pending=r;let a=r.payload,i=t.action(l,a);function s(e){r.discarded||(t.state=e,u(t,n),r.resolve(e))}(0,o.isThenable)(i)?i.then(s,e=>{u(t,n),r.reject(e)}):s(i)}function s(e){let t={state:e,dispatch:(e,r)=>(function(e,t,r){let l={resolve:r,reject:()=>{}};if(t.type!==n.ACTION_RESTORE){let e=new Promise((e,t)=>{l={resolve:e,reject:t}});(0,a.startTransition)(()=>{r(e)})}let o={payload:t,next:null,resolve:l.resolve,reject:l.reject};null===e.pending?(e.last=o,i({actionQueue:e,action:o,setState:r})):t.type===n.ACTION_NAVIGATE||t.type===n.ACTION_RESTORE?(e.pending.discarded=!0,o.next=e.pending.next,e.pending.payload.type===n.ACTION_SERVER_ACTION&&(e.needsRefresh=!0),i({actionQueue:e,action:o,setState:r})):(null!==e.last&&(e.last.next=o),e.last=o)})(t,e,r),action:async(e,t)=>(0,l.reducer)(e,t),pending:null,last:null};return t}function c(){return null}},67835:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{useReducer:function(){return o},useUnwrapState:function(){return a}});let n=r(24841)._(r(44508)),l=r(1918);function a(e){return(0,l.isThenable)(e)?(0,n.use)(e):e}function o(e){let[t,r]=n.default.useState(e.state),l=i();return[t,(0,n.useCallback)(t=>{l(()=>e.dispatch(t,r))},[e,l])]}let u=e=>e(),i=()=>u;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},68998:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PromiseQueue",{enumerable:!0,get:function(){return s}});let n=r(55259),l=r(99607);var a=l._("_maxConcurrency"),o=l._("_runningCount"),u=l._("_queue"),i=l._("_processNext");class s{enqueue(e){let t,r,l=new Promise((e,n)=>{t=e,r=n}),a=async()=>{try{n._(this,o)[o]++;let r=await e();t(r)}catch(e){r(e)}finally{n._(this,o)[o]--,n._(this,i)[i]()}};return n._(this,u)[u].push({promiseFn:l,task:a}),n._(this,i)[i](),l}bump(e){let t=n._(this,u)[u].findIndex(t=>t.promiseFn===e);if(t>-1){let e=n._(this,u)[u].splice(t,1)[0];n._(this,u)[u].unshift(e),n._(this,i)[i](!0)}}constructor(e=5){Object.defineProperty(this,i,{value:c}),Object.defineProperty(this,a,{writable:!0,value:void 0}),Object.defineProperty(this,o,{writable:!0,value:void 0}),Object.defineProperty(this,u,{writable:!0,value:void 0}),n._(this,a)[a]=e,n._(this,o)[o]=0,n._(this,u)[u]=[]}}function c(e){if(void 0===e&&(e=!1),(n._(this,o)[o]<n._(this,a)[a]||e)&&n._(this,u)[u].length>0){var t;null==(t=n._(this,u)[u].shift())||t.task()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},70337:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{addSearchParamsToPageSegments:function(){return d},handleAliasedPrefetchEntry:function(){return c}});let n=r(95983),l=r(52554),a=r(20740),o=r(66029),u=r(53789),i=r(59428),s=r(39105);function c(e,t,r,c){let f,p=e.tree,h=e.cache,y=(0,o.createHrefFromUrl)(r);if("string"==typeof t)return!1;for(let e of t){if(!function e(t){if(!t)return!1;let r=t[2];if(t[3])return!0;for(let t in r)if(e(r[t]))return!0;return!1}(e.seedData))continue;let t=e.tree;t=d(t,Object.fromEntries(r.searchParams));let{seedData:o,isRootRender:s,pathToSegment:c}=e,g=["",...c];t=d(t,Object.fromEntries(r.searchParams));let v=(0,a.applyRouterStatePatchToTree)(g,p,t,y),b=(0,l.createEmptyCacheNode)();if(s&&o){let e=o[1];b.loading=o[3],b.rsc=e,function e(t,r,l,a){if(0!==Object.keys(l[1]).length)for(let o in l[1]){let i,s=l[1][o],c=s[0],d=(0,u.createRouterCacheKey)(c),f=null!==a&&void 0!==a[2][o]?a[2][o]:null;if(null!==f){let e=f[1],t=f[3];i={lazyData:null,rsc:c.includes(n.PAGE_SEGMENT_KEY)?null:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:t}}else i={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null};let p=t.parallelRoutes.get(o);p?p.set(d,i):t.parallelRoutes.set(o,new Map([[d,i]])),e(i,r,s,f)}}(b,h,t,o)}else b.rsc=h.rsc,b.prefetchRsc=h.prefetchRsc,b.loading=h.loading,b.parallelRoutes=new Map(h.parallelRoutes),(0,i.fillCacheWithNewSubTreeDataButOnlyLoading)(b,h,e);v&&(p=v,h=b,f=!0)}return!!f&&(c.patchedTree=p,c.cache=h,c.canonicalUrl=y,c.hashFragment=r.hash,(0,s.handleMutable)(e,c))}function d(e,t){let[r,l,...a]=e;if(r.includes(n.PAGE_SEGMENT_KEY))return[(0,n.addSearchParamsIfPageSegment)(r,t),l,...a];let o={};for(let[e,r]of Object.entries(l))o[e]=d(r,t);return[r,o,...a]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},70818:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hmrRefreshReducer",{enumerable:!0,get:function(){return n}}),r(23702),r(66029),r(20740),r(78944),r(28206),r(39105),r(95866),r(52554),r(64887),r(21484);let n=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},71832:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return l}});let n=r(37675);function l(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:l,hash:a}=(0,n.parsePath)(e);return""+t+r+l+a}},72818:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>d,pages:()=>c,routeModule:()=>f,tree:()=>s});var n=r(88253),l=r(21418),a=r(52052),o=r.n(a),u=r(75779),i={};for(let e in u)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>u[e]);r.d(t,i);let s={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,62188)),"C:\\Users\\<USER>\\Desktop\\personale\\Roo-Code\\apps\\web-evals\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,29230))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,56910)),"C:\\Users\\<USER>\\Desktop\\personale\\Roo-Code\\apps\\web-evals\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,64544,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,20441,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,93670,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,29230))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\personale\\Roo-Code\\apps\\web-evals\\src\\app\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},f=new n.AppPageRouteModule({definition:{kind:l.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:s}})},73962:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{prefetchQueue:function(){return a},prefetchReducer:function(){return o}});let n=r(68998),l=r(77636),a=new n.PromiseQueue(5),o=function(e,t){(0,l.prunePrefetchCache)(e.prefetchCache);let{url:r}=t;return(0,l.getOrCreatePrefetchCacheEntry)({url:r,nextUrl:e.nextUrl,prefetchCache:e.prefetchCache,kind:t.kind,tree:e.tree,allowAliasing:!0}),e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},74998:e=>{"use strict";e.exports=require("perf_hooks")},76765:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"shouldHardNavigate",{enumerable:!0,get:function(){return function e(t,r){let[a,o]=r,[u,i]=t;return(0,l.matchSegment)(u,a)?!(t.length<=2)&&e((0,n.getNextFlightSegmentPath)(t),o[i]):!!Array.isArray(u)}}});let n=r(26065),l=r(97047);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},77636:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{STATIC_STALETIME_MS:function(){return p},createSeededPrefetchCacheEntry:function(){return s},getOrCreatePrefetchCacheEntry:function(){return i},prunePrefetchCache:function(){return d}});let n=r(23702),l=r(57164),a=r(73962);function o(e,t,r){let n=e.pathname;return(t&&(n+=e.search),r)?""+r+"%"+n:n}function u(e,t,r){return o(e,t===l.PrefetchKind.FULL,r)}function i(e){let{url:t,nextUrl:r,tree:n,prefetchCache:a,kind:u,allowAliasing:i=!0}=e,s=function(e,t,r,n,a){for(let u of(void 0===t&&(t=l.PrefetchKind.TEMPORARY),[r,null])){let r=o(e,!0,u),i=o(e,!1,u),s=e.search?r:i,c=n.get(s);if(c&&a){if(c.url.pathname===e.pathname&&c.url.search!==e.search)return{...c,aliased:!0};return c}let d=n.get(i);if(a&&e.search&&t!==l.PrefetchKind.FULL&&d&&!d.key.includes("%"))return{...d,aliased:!0}}if(t!==l.PrefetchKind.FULL&&a){for(let t of n.values())if(t.url.pathname===e.pathname&&!t.key.includes("%"))return{...t,aliased:!0}}}(t,u,r,a,i);return s?(s.status=h(s),s.kind!==l.PrefetchKind.FULL&&u===l.PrefetchKind.FULL&&s.data.then(e=>{if(!(Array.isArray(e.flightData)&&e.flightData.some(e=>e.isRootRender&&null!==e.seedData)))return c({tree:n,url:t,nextUrl:r,prefetchCache:a,kind:null!=u?u:l.PrefetchKind.TEMPORARY})}),u&&s.kind===l.PrefetchKind.TEMPORARY&&(s.kind=u),s):c({tree:n,url:t,nextUrl:r,prefetchCache:a,kind:u||l.PrefetchKind.TEMPORARY})}function s(e){let{nextUrl:t,tree:r,prefetchCache:n,url:a,data:o,kind:i}=e,s=o.couldBeIntercepted?u(a,i,t):u(a,i),c={treeAtTimeOfPrefetch:r,data:Promise.resolve(o),kind:i,prefetchTime:Date.now(),lastUsedTime:Date.now(),staleTime:-1,key:s,status:l.PrefetchCacheEntryStatus.fresh,url:a};return n.set(s,c),c}function c(e){let{url:t,kind:r,tree:o,nextUrl:i,prefetchCache:s}=e,c=u(t,r),d=a.prefetchQueue.enqueue(()=>(0,n.fetchServerResponse)(t,{flightRouterState:o,nextUrl:i,prefetchKind:r}).then(e=>{let r;if(e.couldBeIntercepted&&(r=function(e){let{url:t,nextUrl:r,prefetchCache:n,existingCacheKey:l}=e,a=n.get(l);if(!a)return;let o=u(t,a.kind,r);return n.set(o,{...a,key:o}),n.delete(l),o}({url:t,existingCacheKey:c,nextUrl:i,prefetchCache:s})),e.prerendered){let t=s.get(null!=r?r:c);t&&(t.kind=l.PrefetchKind.FULL,-1!==e.staleTime&&(t.staleTime=e.staleTime))}return e})),f={treeAtTimeOfPrefetch:o,data:d,kind:r,prefetchTime:Date.now(),lastUsedTime:null,staleTime:-1,key:c,status:l.PrefetchCacheEntryStatus.fresh,url:t};return s.set(c,f),f}function d(e){for(let[t,r]of e)h(r)===l.PrefetchCacheEntryStatus.expired&&e.delete(t)}let f=1e3*Number("0"),p=1e3*Number("300");function h(e){let{kind:t,prefetchTime:r,lastUsedTime:n,staleTime:a}=e;return -1!==a?Date.now()<r+a?l.PrefetchCacheEntryStatus.fresh:l.PrefetchCacheEntryStatus.stale:Date.now()<(null!=n?n:r)+f?n?l.PrefetchCacheEntryStatus.reusable:l.PrefetchCacheEntryStatus.fresh:t===l.PrefetchKind.AUTO&&Date.now()<r+p?l.PrefetchCacheEntryStatus.stale:t===l.PrefetchKind.FULL&&Date.now()<r+p?l.PrefetchCacheEntryStatus.reusable:l.PrefetchCacheEntryStatus.expired}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},78944:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return function e(t,r){let n=t[0],l=r[0];if(Array.isArray(n)&&Array.isArray(l)){if(n[0]!==l[0]||n[2]!==l[2])return!0}else if(n!==l)return!0;if(t[4])return!r[4];if(r[4])return!0;let a=Object.values(t[1])[0],o=Object.values(r[1])[0];return!a||!o||e(a,o)}}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},79130:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return function e(t,r,a,o,u,i){if(0===Object.keys(a[1]).length){t.head=u;return}for(let s in a[1]){let c,d=a[1][s],f=d[0],p=(0,n.createRouterCacheKey)(f),h=null!==o&&void 0!==o[2][s]?o[2][s]:null;if(r){let n=r.parallelRoutes.get(s);if(n){let r,a=(null==i?void 0:i.kind)==="auto"&&i.status===l.PrefetchCacheEntryStatus.reusable,o=new Map(n),c=o.get(p);r=null!==h?{lazyData:null,rsc:h[1],prefetchRsc:null,head:null,prefetchHead:null,loading:h[3],parallelRoutes:new Map(null==c?void 0:c.parallelRoutes)}:a&&c?{lazyData:c.lazyData,rsc:c.rsc,prefetchRsc:c.prefetchRsc,head:c.head,prefetchHead:c.prefetchHead,parallelRoutes:new Map(c.parallelRoutes),loading:c.loading}:{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map(null==c?void 0:c.parallelRoutes),loading:null},o.set(p,r),e(r,c,d,h||null,u,i),t.parallelRoutes.set(s,o);continue}}if(null!==h){let e=h[1],t=h[3];c={lazyData:null,rsc:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:t}}else c={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null};let y=t.parallelRoutes.get(s);y?y.set(p,c):t.parallelRoutes.set(s,new Map([[p,c]])),e(c,void 0,d,h,u,i)}}}});let n=r(53789),l=r(57164);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},79551:e=>{"use strict";e.exports=require("url")},79748:e=>{"use strict";e.exports=require("fs/promises")},80394:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return l}});let n=r(53789);function l(e,t,r){for(let l in r[1]){let a=r[1][l][0],o=(0,n.createRouterCacheKey)(a),u=t.parallelRoutes.get(l);if(u){let t=new Map(u);t.delete(o),e.parallelRoutes.set(l,t)}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},82889:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"restoreReducer",{enumerable:!0,get:function(){return a}});let n=r(66029),l=r(32844);function a(e,t){var r;let{url:a,tree:o}=t,u=(0,n.createHrefFromUrl)(a),i=o||e.tree,s=e.cache;return{canonicalUrl:u,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:e.focusAndScrollRef,cache:s,prefetchCache:e.prefetchCache,tree:i,nextUrl:null!=(r=(0,l.extractPathFromFlightRouterState)(i))?r:a.pathname}}r(57570),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},83221:(e,t)=>{"use strict";function r(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function l(e){let t=new URLSearchParams;for(let[r,l]of Object.entries(e))if(Array.isArray(l))for(let e of l)t.append(r,n(e));else t.set(r,n(l));return t}function a(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return a},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return l}})},88672:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"refreshReducer",{enumerable:!0,get:function(){return h}});let n=r(23702),l=r(66029),a=r(20740),o=r(78944),u=r(28206),i=r(39105),s=r(79130),c=r(52554),d=r(64887),f=r(21484),p=r(3510);function h(e,t){let{origin:r}=t,h={},y=e.canonicalUrl,g=e.tree;h.preserveCustomHistoryState=!1;let v=(0,c.createEmptyCacheNode)(),b=(0,f.hasInterceptionRouteInCurrentTree)(e.tree);return v.lazyData=(0,n.fetchServerResponse)(new URL(y,r),{flightRouterState:[g[0],g[1],g[2],"refetch"],nextUrl:b?e.nextUrl:null}),v.lazyData.then(async r=>{let{flightData:n,canonicalUrl:c}=r;if("string"==typeof n)return(0,u.handleExternalUrl)(e,h,n,e.pushRef.pendingPush);for(let r of(v.lazyData=null,n)){let{tree:n,seedData:i,head:f,isRootRender:_}=r;if(!_)return console.log("REFRESH FAILED"),e;let m=(0,a.applyRouterStatePatchToTree)([""],g,n,e.canonicalUrl);if(null===m)return(0,d.handleSegmentMismatch)(e,t,n);if((0,o.isNavigatingToNewRootLayout)(g,m))return(0,u.handleExternalUrl)(e,h,y,e.pushRef.pendingPush);let P=c?(0,l.createHrefFromUrl)(c):void 0;if(c&&(h.canonicalUrl=P),null!==i){let e=i[1],t=i[3];v.rsc=e,v.prefetchRsc=null,v.loading=t,(0,s.fillLazyItemsTillLeafWithHead)(v,void 0,n,i,f,void 0),h.prefetchCache=new Map}await (0,p.refreshInactiveParallelSegments)({state:e,updatedTree:m,updatedCache:v,includeNextUrl:b,canonicalUrl:h.canonicalUrl||e.canonicalUrl}),h.cache=v,h.patchedTree=m,g=m}return(0,i.handleMutable)(e,h)},()=>e)}r(90727),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},90727:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{NavigationResultTag:function(){return d},PrefetchPriority:function(){return f},bumpPrefetchTask:function(){return s},cancelPrefetchTask:function(){return i},createCacheKey:function(){return c},getCurrentCacheVersion:function(){return o},navigate:function(){return l},prefetch:function(){return n},revalidateEntireCache:function(){return a},schedulePrefetchTask:function(){return u}});let r=()=>{throw Object.defineProperty(Error("Segment Cache experiment is not enabled. This is a bug in Next.js."),"__NEXT_ERROR_CODE",{value:"E654",enumerable:!1,configurable:!0})},n=r,l=r,a=r,o=r,u=r,i=r,s=r,c=r;var d=function(e){return e[e.MPA=0]="MPA",e[e.Success=1]="Success",e[e.NoOp=2]="NoOp",e[e.Async=3]="Async",e}({}),f=function(e){return e[e.Intent=2]="Intent",e[e.Default=1]="Default",e[e.Background=0]="Background",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},90840:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return l}});let n=r(44508);function l(e,t){let r=(0,n.useRef)(null),l=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=l.current;t&&(l.current=null,t())}else e&&(r.current=a(e,n)),t&&(l.current=a(t,n))},[e,t])}function a(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},91645:e=>{"use strict";e.exports=require("net")},92369:(e,t,r)=>{"use strict";r.d(t,{u:()=>y,x:()=>g});var n=r(96401);r(90109);var l=r(33873),a=r(29021),o=r.n(a),u=r(79551);let i=require("child_process");var s=r(10606);async function c(e,t,{concurrency:r=Number.POSITIVE_INFINITY,stopOnError:n=!0,signal:l}={}){return new Promise((a,o)=>{if(void 0===e[Symbol.iterator]&&void 0===e[Symbol.asyncIterator])throw TypeError(`Expected \`input\` to be either an \`Iterable\` or \`AsyncIterable\`, got (${typeof e})`);if("function"!=typeof t)throw TypeError("Mapper function is required");if(!(Number.isSafeInteger(r)&&r>=1||r===Number.POSITIVE_INFINITY))throw TypeError(`Expected \`concurrency\` to be an integer from 1 and up or \`Infinity\`, got \`${r}\` (${typeof r})`);let u=[],i=[],s=new Map,c=!1,f=!1,p=!1,h=0,y=0,g=void 0===e[Symbol.iterator]?e[Symbol.asyncIterator]():e[Symbol.iterator](),v=()=>{m(l.reason)},b=()=>{l?.removeEventListener("abort",v)},_=e=>{a(e),b()},m=e=>{c=!0,f=!0,o(e),b()};l&&(l.aborted&&m(l.reason),l.addEventListener("abort",v,{once:!0}));let P=async()=>{if(f)return;let e=await g.next(),r=y;if(y++,e.done){if(p=!0,0===h&&!f){if(!n&&i.length>0){m(AggregateError(i));return}if(f=!0,0===s.size){_(u);return}let e=[];for(let[t,r]of u.entries())s.get(t)!==d&&e.push(r);_(e)}return}h++,(async()=>{try{let n=await e.value;if(f)return;let l=await t(n,r);l===d&&s.set(r,l),u[r]=l,h--,await P()}catch(e){if(n)m(e);else{i.push(e),h--;try{await P()}catch(e){m(e)}}}})()};(async()=>{for(let e=0;e<r;e++){try{await P()}catch(e){m(e);break}if(p||c)break}})()})}let d=Symbol("skip");var f=r(65391),p=r(29701);let h=l.resolve(l.dirname((0,u.fileURLToPath)("file:///C:/Users/<USER>/Desktop/personale/Roo-Code/apps/web-evals/src/actions/runs.ts")),"../../../../../evals");async function y({suite:e,exercises:t=[],systemPrompt:r,timeout:n,...l}){let a=await (0,f.ut)({...l,timeout:n,socketPath:""});if("partial"===e)for(let e of t){let[t,r]=e.split("/");if(!t||!r)throw Error("Invalid exercise path: "+e);await (0,f.UT)({...l,runId:a.id,language:t,exercise:r})}else for(let e of f.w8){let t=await (0,f.z)(h,e);await c(t,t=>(0,f.UT)({runId:a.id,language:e,exercise:t}),{concurrency:10})}(0,s.revalidatePath)("/runs");try{let e=o().existsSync("/.dockerenv"),t=[`--name evals-controller-${a.id}`,"--rm","--network evals_default","-v /var/run/docker.sock:/var/run/docker.sock","-v /tmp/evals:/var/log/evals","-e HOST_EXECUTION_METHOD=docker"],r=`pnpm --filter @roo-code/evals cli --runId ${a.id}`,n=e?`docker run ${t.join(" ")} evals-runner sh -c "${r}"`:r;console.log("spawn ->",n);let l=(0,i.spawn)("sh",["-c",n],{detached:!0,stdio:["ignore","pipe","pipe"]}),u=o().createWriteStream("/tmp/roo-code-evals.log",{flags:"a"});l.stdout&&l.stdout.pipe(u),l.stderr&&l.stderr.pipe(u),l.unref()}catch(e){console.error(e)}return a}async function g(e){await (0,f.xF)(e),(0,s.revalidatePath)("/runs")}(0,p.D)([y,g]),(0,n.A)(y,"405173a3cf25fdf1c0d8eafa47a635bc87618994ab",null),(0,n.A)(g,"406579da86bcc89a2a20048f96b5d7590e0fa8d3a4",null)},95866:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyFlightData",{enumerable:!0,get:function(){return a}});let n=r(79130),l=r(59428);function a(e,t,r,a){let{tree:o,seedData:u,head:i,isRootRender:s}=r;if(null===u)return!1;if(s){let r=u[1];t.loading=u[3],t.rsc=r,t.prefetchRsc=null,(0,n.fillLazyItemsTillLeafWithHead)(t,e,o,u,i,a)}else t.rsc=e.rsc,t.prefetchRsc=e.prefetchRsc,t.parallelRoutes=new Map(e.parallelRoutes),t.loading=e.loading,(0,l.fillCacheWithNewSubTreeData)(t,e,r,a);return!0}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97439:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"findHeadInCache",{enumerable:!0,get:function(){return l}});let n=r(53789);function l(e,t){return function e(t,r,l){if(0===Object.keys(r).length)return[t,l];if(r.children){let[a,o]=r.children,u=t.parallelRoutes.get("children");if(u){let t=(0,n.createRouterCacheKey)(a),r=u.get(t);if(r){let n=e(r,o,l+"/"+t);if(n)return n}}}for(let a in r){if("children"===a)continue;let[o,u]=r[a],i=t.parallelRoutes.get(a);if(!i)continue;let s=(0,n.createRouterCacheKey)(o),c=i.get(s);if(!c)continue;let d=e(c,u,l+"/"+s);if(d)return d}return null}(e,t,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97695:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"4046e7bfdaec154e653714082481342fe8d4288f47":()=>o,"405173a3cf25fdf1c0d8eafa47a635bc87618994ab":()=>n.u,"406579da86bcc89a2a20048f96b5d7590e0fa8d3a4":()=>n.x});var n=r(92369),l=r(96401);r(90109);var a=r(65391);async function o(e){try{return await (0,a.J)({sourceDb:a.Sn,targetDb:(0,a.NL)(),runId:e}),{success:!0,message:`Run ${e} successfully copied to production.`}}catch(t){return{success:!1,error:`Failed to copy run ${e} to production: ${t instanceof Error?t.message:"Unknown error"}.`}}}(0,r(29701).D)([o]),(0,l.A)(o,"4046e7bfdaec154e653714082481342fe8d4288f47",null)},98526:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasBasePath",{enumerable:!0,get:function(){return l}});let n=r(56561);function l(e){return(0,n.pathHasPrefix)(e,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},99229:(e,t,r)=>{"use strict";r.d(t,{Runs:()=>w});var n=r(3641),l=r(30427),a=r(62248),o=r(15361),u=r(44508),i=r(40644),s=r.n(i),c=r(72364);let d=(0,c.A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]]),f=(0,c.A)("clipboard-list",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"M12 11h4",key:"1jrz19"}],["path",{d:"M12 16h4",key:"n85exb"}],["path",{d:"M8 11h.01",key:"1dfujw"}],["path",{d:"M8 16h.01",key:"18s6g9"}]]);var p=r(148),h=r(24378);let y=(0,c.A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]),g=(0,c.A)("trash",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]]);var v=r(29269);let b=(0,v.createServerReference)("406579da86bcc89a2a20048f96b5d7590e0fa8d3a4",v.callServer,void 0,v.findSourceMapURL,"deleteRun");var _=r(60770),m=r(51321),P=r(65084),R=r(13259),j=r(88549),E=class extends R.Q{#e;#t=void 0;#r;#n;constructor(e,t){super(),this.#e=e,this.setOptions(t),this.bindMethods(),this.#l()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let t=this.options;this.options=this.#e.defaultMutationOptions(e),(0,j.f8)(this.options,t)||this.#e.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#r,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,j.EN)(t.mutationKey)!==(0,j.EN)(this.options.mutationKey)?this.reset():this.#r?.state.status==="pending"&&this.#r.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#r?.removeObserver(this)}onMutationUpdate(e){this.#l(),this.#a(e)}getCurrentResult(){return this.#t}reset(){this.#r?.removeObserver(this),this.#r=void 0,this.#l(),this.#a()}mutate(e,t){return this.#n=t,this.#r?.removeObserver(this),this.#r=this.#e.getMutationCache().build(this.#e,this.options),this.#r.addObserver(this),this.#r.execute(e)}#l(){let e=this.#r?.state??(0,m.$)();this.#t={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#a(e){P.jG.batch(()=>{if(this.#n&&this.hasListeners()){let t=this.#t.variables,r=this.#t.context;e?.type==="success"?(this.#n.onSuccess?.(e.data,t,r),this.#n.onSettled?.(e.data,null,t,r)):e?.type==="error"&&(this.#n.onError?.(e.error,t,r),this.#n.onSettled?.(void 0,e.error,t,r))}this.listeners.forEach(e=>{e(this.#t)})})}},O=r(41122),x=r(91149);let T=(0,v.createServerReference)("4046e7bfdaec154e653714082481342fe8d4288f47",v.callServer,void 0,v.findSourceMapURL,"copyRunToProduction");function M({run:e,taskMetrics:t}){let[r,l]=(0,u.useState)(),a=(0,u.useRef)(null),{isPending:i,copyRun:c,copied:v}=function(e){let[t,r]=(0,u.useState)(!1),{isPending:n,mutate:l}=function(e,t){let r=(0,O.jE)(void 0),[n]=u.useState(()=>new E(r,e));u.useEffect(()=>{n.setOptions(e)},[n,e]);let l=u.useSyncExternalStore(u.useCallback(e=>n.subscribe(P.jG.batchCalls(e)),[n]),()=>n.getCurrentResult(),()=>n.getCurrentResult()),a=u.useCallback((e,t)=>{n.mutate(e,t).catch(j.lQ)},[n]);if(l.error&&(0,j.GU)(n.options.throwOnError,[l.error]))throw l.error;return{...l,mutate:a,mutateAsync:l.mutate}}({mutationFn:()=>T(e),onSuccess:e=>{e.success?(x.oR.success(e.message),r(!0),setTimeout(()=>r(!1),3e3)):x.oR.error(e.error)},onError:e=>{console.error("Copy to production failed:",e),x.oR.error("Failed to copy run to production")}});return{isPending:n,copyRun:l,copied:t}}(e.id),m=(0,u.useCallback)(async()=>{if(r)try{await b(r),l(void 0)}catch(e){console.error(e)}},[r]);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(o.Hj,{children:[(0,n.jsx)(o.nA,{children:e.model}),(0,n.jsx)(o.nA,{children:e.passed}),(0,n.jsx)(o.nA,{children:e.failed}),(0,n.jsx)(o.nA,{children:e.passed+e.failed>0&&(0,n.jsxs)("span",{children:[(e.passed/(e.passed+e.failed)*100).toFixed(1),"%"]})}),(0,n.jsx)(o.nA,{children:t&&(0,n.jsxs)("div",{className:"flex items-center gap-1.5",children:[(0,n.jsx)("div",{children:(0,_._y)(t.tokensIn)}),"/",(0,n.jsx)("div",{children:(0,_._y)(t.tokensOut)})]})}),(0,n.jsx)(o.nA,{children:t?.toolUsage?.apply_diff&&(0,n.jsxs)("div",{className:"flex flex-row items-center gap-1.5",children:[(0,n.jsx)("div",{children:t.toolUsage.apply_diff.attempts}),(0,n.jsx)("div",{children:"/"}),(0,n.jsx)("div",{children:(0,_.EH)(t.toolUsage.apply_diff)})]})}),(0,n.jsx)(o.nA,{children:t&&(0,_.vv)(t.cost)}),(0,n.jsx)(o.nA,{children:t&&(0,_.a3)(t.duration)}),(0,n.jsx)(o.nA,{children:(0,n.jsxs)(o.rI,{children:[(0,n.jsx)(o.$n,{variant:"ghost",size:"icon",asChild:!0,children:(0,n.jsx)(o.ty,{children:(0,n.jsx)(d,{})})}),(0,n.jsxs)(o.SQ,{align:"end",children:[(0,n.jsx)(o._2,{asChild:!0,children:(0,n.jsx)(s(),{href:`/runs/${e.id}`,children:(0,n.jsxs)("div",{className:"flex items-center gap-1",children:[(0,n.jsx)(f,{}),(0,n.jsx)("div",{children:"View Tasks"})]})})}),e.taskMetricsId&&(0,n.jsx)(o._2,{onClick:()=>c(),disabled:i||v,children:(0,n.jsx)("div",{className:"flex items-center gap-1",children:i?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(p.A,{className:"animate-spin"}),"Copying..."]}):v?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(h.A,{}),"Copied!"]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(y,{}),"Copy to Production"]})})}),(0,n.jsx)(o._2,{onClick:()=>{l(e.id),setTimeout(()=>a.current?.focus(),0)},children:(0,n.jsxs)("div",{className:"flex items-center gap-1",children:[(0,n.jsx)(g,{}),(0,n.jsx)("div",{children:"Delete"})]})})]})]})})]}),(0,n.jsx)(o.Lt,{open:!!r,onOpenChange:()=>l(void 0),children:(0,n.jsxs)(o.EO,{children:[(0,n.jsxs)(o.wd,{children:[(0,n.jsx)(o.r7,{children:"Are you sure?"}),(0,n.jsx)(o.$v,{children:"This action cannot be undone."})]}),(0,n.jsxs)(o.ck,{children:[(0,n.jsx)(o.Zr,{children:"Cancel"}),(0,n.jsx)(o.Rx,{ref:a,onClick:m,children:"Continue"})]})]})})]})}function w({runs:e}){let t=(0,l.useRouter)();return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(o.XI,{className:"border border-t-0",children:[(0,n.jsx)(o.A0,{children:(0,n.jsxs)(o.Hj,{children:[(0,n.jsx)(o.nd,{children:"Model"}),(0,n.jsx)(o.nd,{children:"Passed"}),(0,n.jsx)(o.nd,{children:"Failed"}),(0,n.jsx)(o.nd,{children:"% Correct"}),(0,n.jsx)(o.nd,{children:"Tokens In / Out"}),(0,n.jsx)(o.nd,{children:"Diff Edits"}),(0,n.jsx)(o.nd,{children:"Cost"}),(0,n.jsx)(o.nd,{children:"Duration"}),(0,n.jsx)(o.nd,{})]})}),(0,n.jsx)(o.BF,{children:e.length?e.map(({taskMetrics:e,...t})=>(0,n.jsx)(M,{run:t,taskMetrics:e},t.id)):(0,n.jsx)(o.Hj,{children:(0,n.jsxs)(o.nA,{colSpan:9,className:"text-center",children:["No eval runs yet.",(0,n.jsx)(o.$n,{variant:"link",onClick:()=>t.push("/runs/new"),children:"Launch"}),"one now."]})})})]}),(0,n.jsx)(o.$n,{variant:"default",className:"absolute top-4 right-12 size-12 rounded-full",onClick:()=>t.push("/runs/new"),children:(0,n.jsx)(a.A,{className:"size-6"})})]})}},99607:(e,t,r)=>{"use strict";r.r(t),r.d(t,{_:()=>l});var n=0;function l(e){return"__private_"+n+++"_"+e}}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[105,923,572,983,958,328,672],()=>r(72818));module.exports=n})();