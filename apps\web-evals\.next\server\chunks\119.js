"use strict";exports.id=119,exports.ids=[119],exports.modules={25119:(e,o,t)=>{t.d(o,{Ur7:()=>ew,jK9:()=>eh,LT1:()=>D,PjY:()=>ej,XxZ:()=>H,YZX:()=>eg,AQ$:()=>B,us4:()=>ek,HkW:()=>ef});let a={"claude-sonnet-4-20250514":{maxTokens:64e3,contextWindow:2e5,supportsImages:!0,supportsComputerUse:!0,supportsPromptCache:!0,inputPrice:3,outputPrice:15,cacheWritesPrice:3.75,cacheReadsPrice:.3,supportsReasoningBudget:!0},"claude-opus-4-20250514":{maxTokens:32e3,contextWindow:2e5,supportsImages:!0,supportsComputerUse:!0,supportsPromptCache:!0,inputPrice:15,outputPrice:75,cacheWritesPrice:18.75,cacheReadsPrice:1.5,supportsReasoningBudget:!0},"claude-3-7-sonnet-20250219":{maxTokens:8192,contextWindow:2e5,supportsImages:!0,supportsComputerUse:!0,supportsPromptCache:!0,inputPrice:3,outputPrice:15,cacheWritesPrice:3.75,cacheReadsPrice:.3},"claude-3-5-sonnet-20241022":{maxTokens:8192,contextWindow:2e5,supportsImages:!0,supportsComputerUse:!0,supportsPromptCache:!0,inputPrice:3,outputPrice:15,cacheWritesPrice:3.75,cacheReadsPrice:.3},"claude-3-5-haiku-20241022":{maxTokens:8192,contextWindow:2e5,supportsImages:!1,supportsPromptCache:!0,inputPrice:1,outputPrice:5,cacheWritesPrice:1.25,cacheReadsPrice:.1}};[{value:"us-east-1",label:"us-east-1"},{value:"us-east-2",label:"us-east-2"},{value:"us-west-1",label:"us-west-1"},{value:"us-west-2",label:"us-west-2"},{value:"ap-northeast-1",label:"ap-northeast-1"},{value:"ap-northeast-2",label:"ap-northeast-2"},{value:"ap-northeast-3",label:"ap-northeast-3"},{value:"ap-south-1",label:"ap-south-1"},{value:"ap-south-2",label:"ap-south-2"},{value:"ap-southeast-1",label:"ap-southeast-1"},{value:"ap-southeast-2",label:"ap-southeast-2"},{value:"ap-east-1",label:"ap-east-1"},{value:"eu-central-1",label:"eu-central-1"},{value:"eu-central-2",label:"eu-central-2"},{value:"eu-west-1",label:"eu-west-1"},{value:"eu-west-2",label:"eu-west-2"},{value:"eu-west-3",label:"eu-west-3"},{value:"eu-north-1",label:"eu-north-1"},{value:"eu-south-1",label:"eu-south-1"},{value:"eu-south-2",label:"eu-south-2"},{value:"ca-central-1",label:"ca-central-1"},{value:"sa-east-1",label:"sa-east-1"},{value:"us-gov-east-1",label:"us-gov-east-1"},{value:"us-gov-west-1",label:"us-gov-west-1"}].sort((e,o)=>e.value.localeCompare(o.value));a["claude-sonnet-4-20250514"],a["claude-opus-4-20250514"],a["claude-3-7-sonnet-20250219"],a["claude-3-5-sonnet-20241022"],a["claude-3-5-haiku-20241022"];var n=t(27013);let i={MIN_SEARCH_RESULTS:10,MAX_SEARCH_RESULTS:200},l=n.z.object({codebaseIndexEnabled:n.z.boolean().optional(),codebaseIndexQdrantUrl:n.z.string().optional(),codebaseIndexEmbedderProvider:n.z.enum(["openai","ollama","openai-compatible","gemini","mistral"]).optional(),codebaseIndexEmbedderBaseUrl:n.z.string().optional(),codebaseIndexEmbedderModelId:n.z.string().optional(),codebaseIndexEmbedderModelDimension:n.z.number().optional(),codebaseIndexSearchMinScore:n.z.number().min(0).max(1).optional(),codebaseIndexSearchMaxResults:n.z.number().min(i.MIN_SEARCH_RESULTS).max(i.MAX_SEARCH_RESULTS).optional(),codebaseIndexOpenAiCompatibleBaseUrl:n.z.string().optional(),codebaseIndexOpenAiCompatibleModelDimension:n.z.number().optional()}),r=n.z.object({openai:n.z.record(n.z.string(),n.z.object({dimension:n.z.number()})).optional(),ollama:n.z.record(n.z.string(),n.z.object({dimension:n.z.number()})).optional(),"openai-compatible":n.z.record(n.z.string(),n.z.object({dimension:n.z.number()})).optional(),gemini:n.z.record(n.z.string(),n.z.object({dimension:n.z.number()})).optional(),mistral:n.z.record(n.z.string(),n.z.object({dimension:n.z.number()})).optional()}),s=n.z.object({codeIndexOpenAiKey:n.z.string().optional(),codeIndexQdrantApiKey:n.z.string().optional(),codebaseIndexOpenAiCompatibleBaseUrl:n.z.string().optional(),codebaseIndexOpenAiCompatibleApiKey:n.z.string().optional(),codebaseIndexOpenAiCompatibleModelDimension:n.z.number().optional(),codebaseIndexGeminiApiKey:n.z.string().optional(),codebaseIndexMistralApiKey:n.z.string().optional()}),p=n.z.enum(["low","medium","high"]),d=n.z.enum(["max_tokens","temperature","reasoning","include_reasoning"]),z=n.z.object({maxTokens:n.z.number().nullish(),maxThinkingTokens:n.z.number().nullish(),contextWindow:n.z.number(),supportsImages:n.z.boolean().optional(),supportsComputerUse:n.z.boolean().optional(),supportsPromptCache:n.z.boolean(),supportsReasoningBudget:n.z.boolean().optional(),requiredReasoningBudget:n.z.boolean().optional(),supportsReasoningEffort:n.z.boolean().optional(),supportedParameters:n.z.array(d).optional(),inputPrice:n.z.number().optional(),outputPrice:n.z.number().optional(),cacheWritesPrice:n.z.number().optional(),cacheReadsPrice:n.z.number().optional(),description:n.z.string().optional(),reasoningEffort:p.optional(),minTokensPerCachePoint:n.z.number().optional(),maxCachePoints:n.z.number().optional(),cachableFields:n.z.array(n.z.string()).optional(),tiers:n.z.array(n.z.object({contextWindow:n.z.number(),inputPrice:n.z.number().optional(),outputPrice:n.z.number().optional(),cacheWritesPrice:n.z.number().optional(),cacheReadsPrice:n.z.number().optional()})).optional()}),u=["anthropic","claude-code","glama","openrouter","bedrock","vertex","openai","ollama","vscode-lm","lmstudio","gemini","gemini-cli","openai-native","mistral","moonshot","deepseek","unbound","requesty","human-relay","fake-ai","xai","groq","chutes","litellm","huggingface"],m=n.z.enum(u),c=n.z.object({id:n.z.string(),name:n.z.string(),apiProvider:m.optional()}),b=n.z.object({includeMaxTokens:n.z.boolean().optional(),diffEnabled:n.z.boolean().optional(),todoListEnabled:n.z.boolean().optional(),fuzzyMatchThreshold:n.z.number().optional(),modelTemperature:n.z.number().nullish(),rateLimitSeconds:n.z.number().optional(),consecutiveMistakeLimit:n.z.number().min(0).optional(),enableReasoningEffort:n.z.boolean().optional(),reasoningEffort:p.optional(),modelMaxTokens:n.z.number().optional(),modelMaxThinkingTokens:n.z.number().optional()}),g=b.extend({apiModelId:n.z.string().optional()}),h=g.extend({apiKey:n.z.string().optional(),anthropicBaseUrl:n.z.string().optional(),anthropicUseAuthToken:n.z.boolean().optional()}),k=g.extend({claudeCodePath:n.z.string().optional(),claudeCodeMaxOutputTokens:n.z.number().int().min(1).max(2e5).optional()}),y=b.extend({glamaModelId:n.z.string().optional(),glamaApiKey:n.z.string().optional()}),w=b.extend({openRouterApiKey:n.z.string().optional(),openRouterModelId:n.z.string().optional(),openRouterBaseUrl:n.z.string().optional(),openRouterSpecificProvider:n.z.string().optional(),openRouterUseMiddleOutTransform:n.z.boolean().optional()}),x=g.extend({awsAccessKey:n.z.string().optional(),awsSecretKey:n.z.string().optional(),awsSessionToken:n.z.string().optional(),awsRegion:n.z.string().optional(),awsUseCrossRegionInference:n.z.boolean().optional(),awsUsePromptCache:n.z.boolean().optional(),awsProfile:n.z.string().optional(),awsUseProfile:n.z.boolean().optional(),awsApiKey:n.z.string().optional(),awsUseApiKey:n.z.boolean().optional(),awsCustomArn:n.z.string().optional(),awsModelContextWindow:n.z.number().optional(),awsBedrockEndpointEnabled:n.z.boolean().optional(),awsBedrockEndpoint:n.z.string().optional()}),A=g.extend({vertexKeyFile:n.z.string().optional(),vertexJsonCredentials:n.z.string().optional(),vertexProjectId:n.z.string().optional(),vertexRegion:n.z.string().optional()}),v=b.extend({openAiBaseUrl:n.z.string().optional(),openAiApiKey:n.z.string().optional(),openAiLegacyFormat:n.z.boolean().optional(),openAiR1FormatEnabled:n.z.boolean().optional(),openAiModelId:n.z.string().optional(),openAiCustomModelInfo:z.nullish(),openAiUseAzure:n.z.boolean().optional(),azureApiVersion:n.z.string().optional(),openAiStreamingEnabled:n.z.boolean().optional(),openAiHostHeader:n.z.string().optional(),openAiHeaders:n.z.record(n.z.string(),n.z.string()).optional()}),C=b.extend({ollamaModelId:n.z.string().optional(),ollamaBaseUrl:n.z.string().optional()}),I=b.extend({vsCodeLmModelSelector:n.z.object({vendor:n.z.string().optional(),family:n.z.string().optional(),version:n.z.string().optional(),id:n.z.string().optional()}).optional()}),j=b.extend({lmStudioModelId:n.z.string().optional(),lmStudioBaseUrl:n.z.string().optional(),lmStudioDraftModelId:n.z.string().optional(),lmStudioSpeculativeDecodingEnabled:n.z.boolean().optional()}),P=g.extend({geminiApiKey:n.z.string().optional(),googleGeminiBaseUrl:n.z.string().optional(),enableUrlContext:n.z.boolean().optional(),enableGrounding:n.z.boolean().optional()}),S=g.extend({geminiCliOAuthPath:n.z.string().optional(),geminiCliProjectId:n.z.string().optional()}),T=g.extend({openAiNativeApiKey:n.z.string().optional(),openAiNativeBaseUrl:n.z.string().optional()}),f=g.extend({mistralApiKey:n.z.string().optional(),mistralCodestralUrl:n.z.string().optional()}),M=g.extend({deepSeekBaseUrl:n.z.string().optional(),deepSeekApiKey:n.z.string().optional()}),_=g.extend({moonshotBaseUrl:n.z.union([n.z.literal("https://api.moonshot.ai/v1"),n.z.literal("https://api.moonshot.cn/v1")]).optional(),moonshotApiKey:n.z.string().optional()}),U=b.extend({unboundApiKey:n.z.string().optional(),unboundModelId:n.z.string().optional()}),R=b.extend({requestyApiKey:n.z.string().optional(),requestyModelId:n.z.string().optional()}),E=b.extend({fakeAi:n.z.unknown().optional()}),K=g.extend({xaiApiKey:n.z.string().optional()}),O=g.extend({groqApiKey:n.z.string().optional()}),W=b.extend({huggingFaceApiKey:n.z.string().optional(),huggingFaceModelId:n.z.string().optional(),huggingFaceInferenceProvider:n.z.string().optional()}),F=g.extend({chutesApiKey:n.z.string().optional()}),L=b.extend({litellmBaseUrl:n.z.string().optional(),litellmApiKey:n.z.string().optional(),litellmModelId:n.z.string().optional(),litellmUsePromptCache:n.z.boolean().optional()}),N=n.z.object({apiProvider:n.z.undefined()});n.z.discriminatedUnion("apiProvider",[h.merge(n.z.object({apiProvider:n.z.literal("anthropic")})),k.merge(n.z.object({apiProvider:n.z.literal("claude-code")})),y.merge(n.z.object({apiProvider:n.z.literal("glama")})),w.merge(n.z.object({apiProvider:n.z.literal("openrouter")})),x.merge(n.z.object({apiProvider:n.z.literal("bedrock")})),A.merge(n.z.object({apiProvider:n.z.literal("vertex")})),v.merge(n.z.object({apiProvider:n.z.literal("openai")})),C.merge(n.z.object({apiProvider:n.z.literal("ollama")})),I.merge(n.z.object({apiProvider:n.z.literal("vscode-lm")})),j.merge(n.z.object({apiProvider:n.z.literal("lmstudio")})),P.merge(n.z.object({apiProvider:n.z.literal("gemini")})),S.merge(n.z.object({apiProvider:n.z.literal("gemini-cli")})),T.merge(n.z.object({apiProvider:n.z.literal("openai-native")})),f.merge(n.z.object({apiProvider:n.z.literal("mistral")})),M.merge(n.z.object({apiProvider:n.z.literal("deepseek")})),_.merge(n.z.object({apiProvider:n.z.literal("moonshot")})),U.merge(n.z.object({apiProvider:n.z.literal("unbound")})),R.merge(n.z.object({apiProvider:n.z.literal("requesty")})),b.merge(n.z.object({apiProvider:n.z.literal("human-relay")})),E.merge(n.z.object({apiProvider:n.z.literal("fake-ai")})),K.merge(n.z.object({apiProvider:n.z.literal("xai")})),O.merge(n.z.object({apiProvider:n.z.literal("groq")})),W.merge(n.z.object({apiProvider:n.z.literal("huggingface")})),F.merge(n.z.object({apiProvider:n.z.literal("chutes")})),L.merge(n.z.object({apiProvider:n.z.literal("litellm")})),N]);let B=n.z.object({apiProvider:m.optional(),...h.shape,...k.shape,...y.shape,...w.shape,...x.shape,...A.shape,...v.shape,...C.shape,...I.shape,...j.shape,...P.shape,...S.shape,...T.shape,...f.shape,...M.shape,..._.shape,...U.shape,...R.shape,...b.shape,...E.shape,...K.shape,...O.shape,...W.shape,...F.shape,...L.shape,...s.shape}),D=B.keyof().options,q=["apiModelId","glamaModelId","openRouterModelId","openAiModelId","ollamaModelId","lmStudioModelId","lmStudioDraftModelId","unboundModelId","requestyModelId","litellmModelId","huggingFaceModelId"],H=e=>{let o=q.find(o=>e[o]);return o?e[o]:void 0},Z=n.z.object({id:n.z.string(),number:n.z.number(),ts:n.z.number(),task:n.z.string(),tokensIn:n.z.number(),tokensOut:n.z.number(),cacheWrites:n.z.number().optional(),cacheReads:n.z.number().optional(),totalCost:n.z.number(),size:n.z.number().optional(),workspace:n.z.string().optional(),mode:n.z.string().optional()});n.z.enum(["powerSteering","multiFileApplyDiff","preventFocusDisruption"]);let V=n.z.object({powerSteering:n.z.boolean().optional(),multiFileApplyDiff:n.z.boolean().optional(),preventFocusDisruption:n.z.boolean().optional()}),Q=n.z.enum(["followup","command","command_output","completion_result","tool","api_req_failed","resume_task","resume_completed_task","mistake_limit_reached","browser_action_launch","use_mcp_server","auto_approval_max_req_reached"]),G=n.z.enum(["error","api_req_started","api_req_finished","api_req_retried","api_req_retry_delayed","api_req_deleted","text","reasoning","completion_result","user_feedback","user_feedback_diff","command_output","shell_integration_warning","browser_action","browser_action_result","mcp_server_request_started","mcp_server_response","subtask_result","checkpoint_saved","rooignore_error","diff_error","condense_context","condense_context_error","codebase_search_result","user_edit_todos"]),X=n.z.object({icon:n.z.string().optional(),text:n.z.string().optional()}),Y=n.z.object({cost:n.z.number(),prevContextTokens:n.z.number(),newContextTokens:n.z.number(),summary:n.z.string()}),$=n.z.object({ts:n.z.number(),type:n.z.union([n.z.literal("ask"),n.z.literal("say")]),ask:Q.optional(),say:G.optional(),text:n.z.string().optional(),images:n.z.array(n.z.string()).optional(),partial:n.z.boolean().optional(),reasoning:n.z.string().optional(),conversationHistoryIndex:n.z.number().optional(),checkpoint:n.z.record(n.z.string(),n.z.unknown()).optional(),progressStatus:X.optional(),contextCondense:Y.optional(),isProtected:n.z.boolean().optional(),apiProtocol:n.z.union([n.z.literal("openai"),n.z.literal("anthropic")]).optional()}),J=n.z.object({totalTokensIn:n.z.number(),totalTokensOut:n.z.number(),totalCacheWrites:n.z.number().optional(),totalCacheReads:n.z.number().optional(),totalCost:n.z.number(),contextTokens:n.z.number()}),ee=n.z.enum(["unset","enabled","disabled"]),eo=n.z.object({appName:n.z.string(),appVersion:n.z.string(),vscodeVersion:n.z.string(),platform:n.z.string(),editorName:n.z.string(),language:n.z.string(),mode:n.z.string(),cloudIsAuthenticated:n.z.boolean().optional()}),et=n.z.object({taskId:n.z.string().optional(),apiProvider:n.z.enum(u).optional(),modelId:n.z.string().optional(),diffStrategy:n.z.string().optional(),isSubtask:n.z.boolean().optional(),todos:n.z.object({total:n.z.number(),completed:n.z.number(),inProgress:n.z.number(),pending:n.z.number()}).optional()}),ea=n.z.object({repositoryUrl:n.z.string().optional(),repositoryName:n.z.string().optional(),defaultBranch:n.z.string().optional()}),en=n.z.object({...eo.shape,...et.shape,...ea.shape});n.z.discriminatedUnion("type",[n.z.object({type:n.z.enum(["Task Created","Task Reopened","Task Completed","Conversation Message","Mode Switched","Mode Selector Opened","Tool Used","Checkpoint Created","Checkpoint Restored","Checkpoint Diffed","Code Action Used","Prompt Enhanced","Title Button Clicked","Authentication Initiated","Marketplace Item Installed","Marketplace Item Removed","Marketplace Tab Viewed","Marketplace Install Button Clicked","Share Button Clicked","Share Organization Clicked","Share Public Clicked","Share Connect To Cloud Clicked","Account Connect Clicked","Account Connect Success","Account Logout Clicked","Account Logout Success","Schema Validation Error","Diff Application Error","Shell Integration Error","Consecutive Mistake Error","Code Index Error","Context Condensed","Sliding Window Truncation","Tab Shown","Mode Setting Changed","Custom Mode Created"]),properties:en}),n.z.object({type:n.z.literal("Task Message"),properties:n.z.object({...en.shape,taskId:n.z.string(),message:$})}),n.z.object({type:n.z.literal("LLM Completion"),properties:n.z.object({...en.shape,inputTokens:n.z.number(),outputTokens:n.z.number(),cacheReadTokens:n.z.number().optional(),cacheWriteTokens:n.z.number().optional(),cost:n.z.number().optional()})})]);let ei=n.z.enum(["read","edit","browser","command","mcp","modes"]),el=n.z.enum(["execute_command","read_file","write_to_file","apply_diff","insert_content","search_and_replace","search_files","list_files","list_code_definition_names","browser_action","use_mcp_tool","access_mcp_resource","ask_followup_question","attempt_completion","switch_mode","new_task","fetch_instructions","codebase_search","update_todo_list"]),er=n.z.record(el,n.z.object({attempts:n.z.number(),failures:n.z.number()})),es=n.z.object({fileRegex:n.z.string().optional().refine(e=>{if(!e)return!0;try{return new RegExp(e),!0}catch{return!1}},{message:"Invalid regular expression pattern"}),description:n.z.string().optional()}),ep=n.z.union([ei,n.z.tuple([ei,es])]),ed=n.z.array(ep).refine(e=>{let o=new Set;return e.every(e=>{let t=Array.isArray(e)?e[0]:e;return!o.has(t)&&(o.add(t),!0)})},{message:"Duplicate groups are not allowed"}),ez=n.z.object({slug:n.z.string().regex(/^[a-zA-Z0-9-]+$/,"Slug must contain only letters numbers and dashes"),name:n.z.string().min(1,"Name is required"),roleDefinition:n.z.string().min(1,"Role definition is required"),whenToUse:n.z.string().optional(),description:n.z.string().optional(),customInstructions:n.z.string().optional(),groups:ed,source:n.z.enum(["global","project"]).optional()});n.z.object({customModes:n.z.array(ez).refine(e=>{let o=new Set;return e.every(e=>!o.has(e.slug)&&(o.add(e.slug),!0))},{message:"Duplicate mode slugs are not allowed"})});let eu=n.z.object({roleDefinition:n.z.string().optional(),whenToUse:n.z.string().optional(),description:n.z.string().optional(),customInstructions:n.z.string().optional()}),em=n.z.record(n.z.string(),eu.optional()),ec=n.z.record(n.z.string(),n.z.string().optional()),eb=n.z.enum(["ca","de","en","es","fr","hi","id","it","ja","ko","nl","pl","pt-BR","ru","tr","vi","zh-CN","zh-TW"]),eg=n.z.object({currentApiConfigName:n.z.string().optional(),listApiConfigMeta:n.z.array(c).optional(),pinnedApiConfigs:n.z.record(n.z.string(),n.z.boolean()).optional(),lastShownAnnouncementId:n.z.string().optional(),customInstructions:n.z.string().optional(),taskHistory:n.z.array(Z).optional(),condensingApiConfigId:n.z.string().optional(),customCondensingPrompt:n.z.string().optional(),autoApprovalEnabled:n.z.boolean().optional(),alwaysAllowReadOnly:n.z.boolean().optional(),alwaysAllowReadOnlyOutsideWorkspace:n.z.boolean().optional(),alwaysAllowWrite:n.z.boolean().optional(),alwaysAllowWriteOutsideWorkspace:n.z.boolean().optional(),alwaysAllowWriteProtected:n.z.boolean().optional(),writeDelayMs:n.z.number().min(0).optional(),alwaysAllowBrowser:n.z.boolean().optional(),alwaysApproveResubmit:n.z.boolean().optional(),requestDelaySeconds:n.z.number().optional(),alwaysAllowMcp:n.z.boolean().optional(),alwaysAllowModeSwitch:n.z.boolean().optional(),alwaysAllowSubtasks:n.z.boolean().optional(),alwaysAllowExecute:n.z.boolean().optional(),alwaysAllowFollowupQuestions:n.z.boolean().optional(),followupAutoApproveTimeoutMs:n.z.number().optional(),alwaysAllowUpdateTodoList:n.z.boolean().optional(),allowedCommands:n.z.array(n.z.string()).optional(),deniedCommands:n.z.array(n.z.string()).optional(),commandExecutionTimeout:n.z.number().optional(),commandTimeoutAllowlist:n.z.array(n.z.string()).optional(),preventCompletionWithOpenTodos:n.z.boolean().optional(),allowedMaxRequests:n.z.number().nullish(),autoCondenseContext:n.z.boolean().optional(),autoCondenseContextPercent:n.z.number().optional(),maxConcurrentFileReads:n.z.number().optional(),includeDiagnosticMessages:n.z.boolean().optional(),maxDiagnosticMessages:n.z.number().optional(),browserToolEnabled:n.z.boolean().optional(),browserViewportSize:n.z.string().optional(),screenshotQuality:n.z.number().optional(),remoteBrowserEnabled:n.z.boolean().optional(),remoteBrowserHost:n.z.string().optional(),cachedChromeHostUrl:n.z.string().optional(),enableCheckpoints:n.z.boolean().optional(),ttsEnabled:n.z.boolean().optional(),ttsSpeed:n.z.number().optional(),soundEnabled:n.z.boolean().optional(),soundVolume:n.z.number().optional(),maxOpenTabsContext:n.z.number().optional(),maxWorkspaceFiles:n.z.number().optional(),showRooIgnoredFiles:n.z.boolean().optional(),maxReadFileLine:n.z.number().optional(),maxImageFileSize:n.z.number().optional(),maxTotalImageSize:n.z.number().optional(),terminalOutputLineLimit:n.z.number().optional(),terminalOutputCharacterLimit:n.z.number().optional(),terminalShellIntegrationTimeout:n.z.number().optional(),terminalShellIntegrationDisabled:n.z.boolean().optional(),terminalCommandDelay:n.z.number().optional(),terminalPowershellCounter:n.z.boolean().optional(),terminalZshClearEolMark:n.z.boolean().optional(),terminalZshOhMy:n.z.boolean().optional(),terminalZshP10k:n.z.boolean().optional(),terminalZdotdir:n.z.boolean().optional(),terminalCompressProgressBar:n.z.boolean().optional(),diagnosticsEnabled:n.z.boolean().optional(),rateLimitSeconds:n.z.number().optional(),diffEnabled:n.z.boolean().optional(),fuzzyMatchThreshold:n.z.number().optional(),experiments:V.optional(),codebaseIndexModels:r.optional(),codebaseIndexConfig:l.optional(),language:eb.optional(),telemetrySetting:ee.optional(),mcpEnabled:n.z.boolean().optional(),enableMcpServerCreation:n.z.boolean().optional(),mode:n.z.string().optional(),modeApiConfigs:n.z.record(n.z.string(),n.z.string()).optional(),customModes:n.z.array(ez).optional(),customModePrompts:em.optional(),customSupportPrompts:ec.optional(),enhancementApiConfigId:n.z.string().optional(),historyPreviewCollapsed:n.z.boolean().optional(),profileThresholds:n.z.record(n.z.string(),n.z.number()).optional(),hasOpenedModeSelector:n.z.boolean().optional(),lastModeExportPath:n.z.string().optional(),lastModeImportPath:n.z.string().optional()}),eh=eg.keyof().options,ek=B.merge(eg),ey=["apiKey","glamaApiKey","openRouterApiKey","awsAccessKey","awsApiKey","awsSecretKey","awsSessionToken","openAiApiKey","geminiApiKey","openAiNativeApiKey","deepSeekApiKey","moonshotApiKey","mistralApiKey","unboundApiKey","requestyApiKey","xaiApiKey","groqApiKey","chutesApiKey","litellmApiKey","codeIndexOpenAiKey","codeIndexQdrantApiKey","codebaseIndexOpenAiCompatibleApiKey","codebaseIndexGeminiApiKey","codebaseIndexMistralApiKey","huggingFaceApiKey"];[...eh,...D].filter(e=>!ey.includes(e));let ew={apiProvider:"openrouter",openRouterUseMiddleOutTransform:!1,lastShownAnnouncementId:"jul-09-2025-3-23-0",pinnedApiConfigs:{},autoApprovalEnabled:!0,alwaysAllowReadOnly:!0,alwaysAllowReadOnlyOutsideWorkspace:!1,alwaysAllowWrite:!0,alwaysAllowWriteOutsideWorkspace:!1,alwaysAllowWriteProtected:!1,writeDelayMs:1e3,alwaysAllowBrowser:!0,alwaysApproveResubmit:!0,requestDelaySeconds:10,alwaysAllowMcp:!0,alwaysAllowModeSwitch:!0,alwaysAllowSubtasks:!0,alwaysAllowExecute:!0,alwaysAllowFollowupQuestions:!0,alwaysAllowUpdateTodoList:!0,followupAutoApproveTimeoutMs:0,allowedCommands:["*"],commandExecutionTimeout:20,commandTimeoutAllowlist:[],preventCompletionWithOpenTodos:!1,browserToolEnabled:!1,browserViewportSize:"900x600",screenshotQuality:75,remoteBrowserEnabled:!1,ttsEnabled:!1,ttsSpeed:1,soundEnabled:!1,soundVolume:.5,terminalOutputLineLimit:500,terminalOutputCharacterLimit:5e4,terminalShellIntegrationTimeout:3e4,terminalCommandDelay:0,terminalPowershellCounter:!1,terminalZshOhMy:!0,terminalZshClearEolMark:!0,terminalZshP10k:!1,terminalZdotdir:!0,terminalCompressProgressBar:!0,terminalShellIntegrationDisabled:!0,diagnosticsEnabled:!0,diffEnabled:!0,fuzzyMatchThreshold:1,enableCheckpoints:!1,rateLimitSeconds:0,maxOpenTabsContext:20,maxWorkspaceFiles:200,showRooIgnoredFiles:!0,maxReadFileLine:-1,includeDiagnosticMessages:!0,maxDiagnosticMessages:50,language:"en",telemetrySetting:"enabled",mcpEnabled:!1,mode:"code",customModes:[]},ex=n.z.object({allowAll:n.z.boolean(),providers:n.z.record(n.z.object({allowAll:n.z.boolean(),models:n.z.array(n.z.string()).optional()}))}),eA=eg.pick({enableCheckpoints:!0,fuzzyMatchThreshold:!0,maxOpenTabsContext:!0,maxReadFileLine:!0,maxWorkspaceFiles:!0,showRooIgnoredFiles:!0,terminalCommandDelay:!0,terminalCompressProgressBar:!0,terminalOutputLineLimit:!0,terminalShellIntegrationDisabled:!0,terminalShellIntegrationTimeout:!0,terminalZshClearEolMark:!0}).merge(n.z.object({maxOpenTabsContext:n.z.number().int().nonnegative().optional(),maxReadFileLine:n.z.number().int().gte(-1).optional(),maxWorkspaceFiles:n.z.number().int().nonnegative().optional(),terminalCommandDelay:n.z.number().int().nonnegative().optional(),terminalOutputLineLimit:n.z.number().int().nonnegative().optional(),terminalShellIntegrationTimeout:n.z.number().int().nonnegative().optional()})),ev=n.z.object({recordTaskMessages:n.z.boolean().optional(),enableTaskSharing:n.z.boolean().optional(),taskShareExpirationDays:n.z.number().int().positive().optional(),allowMembersViewAllTasks:n.z.boolean().optional()});n.z.object({version:n.z.number(),cloudSettings:ev.optional(),defaultSettings:eA,allowList:ex}),n.z.object({success:n.z.boolean(),shareUrl:n.z.string().optional(),error:n.z.string().optional(),isNewShare:n.z.boolean().optional(),manageUrl:n.z.string().optional()});let eC=n.z.object({answer:n.z.string(),mode:n.z.string().optional()});n.z.object({question:n.z.string().optional(),suggest:n.z.array(eC).optional()});let eI=n.z.object({isSubtask:n.z.boolean()});var ej=function(e){return e.Message="message",e.TaskCreated="taskCreated",e.TaskStarted="taskStarted",e.TaskModeSwitched="taskModeSwitched",e.TaskPaused="taskPaused",e.TaskUnpaused="taskUnpaused",e.TaskAskResponded="taskAskResponded",e.TaskAborted="taskAborted",e.TaskSpawned="taskSpawned",e.TaskCompleted="taskCompleted",e.TaskTokenUsageUpdated="taskTokenUsageUpdated",e.TaskToolFailed="taskToolFailed",e.EvalPass="evalPass",e.EvalFail="evalFail",e}({});let eP=n.z.object({message:n.z.tuple([n.z.object({taskId:n.z.string(),action:n.z.union([n.z.literal("created"),n.z.literal("updated")]),message:$})]),taskCreated:n.z.tuple([n.z.string()]),taskStarted:n.z.tuple([n.z.string()]),taskModeSwitched:n.z.tuple([n.z.string(),n.z.string()]),taskPaused:n.z.tuple([n.z.string()]),taskUnpaused:n.z.tuple([n.z.string()]),taskAskResponded:n.z.tuple([n.z.string()]),taskAborted:n.z.tuple([n.z.string()]),taskSpawned:n.z.tuple([n.z.string(),n.z.string()]),taskCompleted:n.z.tuple([n.z.string(),J,er,eI]),taskTokenUsageUpdated:n.z.tuple([n.z.string(),J]),taskToolFailed:n.z.tuple([n.z.string(),el,n.z.string()])}),eS=n.z.object({clientId:n.z.string(),pid:n.z.number(),ppid:n.z.number()}),eT=n.z.discriminatedUnion("commandName",[n.z.object({commandName:n.z.literal("StartNewTask"),data:n.z.object({configuration:ek,text:n.z.string(),images:n.z.array(n.z.string()).optional(),newTab:n.z.boolean().optional()})}),n.z.object({commandName:n.z.literal("CancelTask"),data:n.z.string()}),n.z.object({commandName:n.z.literal("CloseTask"),data:n.z.string()})]),ef=n.z.discriminatedUnion("eventName",[n.z.object({eventName:n.z.literal("message"),payload:eP.shape.message,taskId:n.z.number().optional()}),n.z.object({eventName:n.z.literal("taskCreated"),payload:eP.shape.taskCreated,taskId:n.z.number().optional()}),n.z.object({eventName:n.z.literal("taskStarted"),payload:eP.shape.taskStarted,taskId:n.z.number().optional()}),n.z.object({eventName:n.z.literal("taskModeSwitched"),payload:eP.shape.taskModeSwitched,taskId:n.z.number().optional()}),n.z.object({eventName:n.z.literal("taskPaused"),payload:eP.shape.taskPaused,taskId:n.z.number().optional()}),n.z.object({eventName:n.z.literal("taskUnpaused"),payload:eP.shape.taskUnpaused,taskId:n.z.number().optional()}),n.z.object({eventName:n.z.literal("taskAskResponded"),payload:eP.shape.taskAskResponded,taskId:n.z.number().optional()}),n.z.object({eventName:n.z.literal("taskAborted"),payload:eP.shape.taskAborted,taskId:n.z.number().optional()}),n.z.object({eventName:n.z.literal("taskSpawned"),payload:eP.shape.taskSpawned,taskId:n.z.number().optional()}),n.z.object({eventName:n.z.literal("taskCompleted"),payload:eP.shape.taskCompleted,taskId:n.z.number().optional()}),n.z.object({eventName:n.z.literal("taskTokenUsageUpdated"),payload:eP.shape.taskTokenUsageUpdated,taskId:n.z.number().optional()}),n.z.object({eventName:n.z.literal("taskToolFailed"),payload:eP.shape.taskToolFailed,taskId:n.z.number().optional()}),n.z.object({eventName:n.z.literal("evalPass"),payload:n.z.undefined(),taskId:n.z.number()}),n.z.object({eventName:n.z.literal("evalFail"),payload:n.z.undefined(),taskId:n.z.number()})]);n.z.discriminatedUnion("type",[n.z.object({type:n.z.literal("Ack"),origin:n.z.literal("server"),data:eS}),n.z.object({type:n.z.literal("TaskCommand"),origin:n.z.literal("client"),clientId:n.z.string(),data:eT}),n.z.object({type:n.z.literal("TaskEvent"),origin:n.z.literal("server"),relayClientId:n.z.string().optional(),data:ef})]);let eM=n.z.object({name:n.z.string().min(1),key:n.z.string().min(1),placeholder:n.z.string().optional(),optional:n.z.boolean().optional().default(!1)}),e_=n.z.object({name:n.z.string().min(1),content:n.z.string().min(1),parameters:n.z.array(eM).optional(),prerequisites:n.z.array(n.z.string()).optional()});n.z.enum(["mode","mcp"]);let eU=n.z.object({id:n.z.string().min(1),name:n.z.string().min(1,"Name is required"),description:n.z.string(),author:n.z.string().optional(),authorUrl:n.z.string().url("Author URL must be a valid URL").optional(),tags:n.z.array(n.z.string()).optional(),prerequisites:n.z.array(n.z.string()).optional()}),eR=eU.extend({content:n.z.string().min(1)}),eE=eU.extend({url:n.z.string().url(),content:n.z.union([n.z.string().min(1),n.z.array(e_)]),parameters:n.z.array(eM).optional()});n.z.discriminatedUnion("type",[eR.extend({type:n.z.literal("mode")}),eE.extend({type:n.z.literal("mcp")})]),n.z.object({target:n.z.enum(["global","project"]).optional().default("project"),parameters:n.z.record(n.z.string(),n.z.any()).optional()}),n.z.discriminatedUnion("status",[n.z.object({executionId:n.z.string(),status:n.z.literal("started"),serverName:n.z.string(),toolName:n.z.string()}),n.z.object({executionId:n.z.string(),status:n.z.literal("output"),response:n.z.string()}),n.z.object({executionId:n.z.string(),status:n.z.literal("completed"),response:n.z.string().optional()}),n.z.object({executionId:n.z.string(),status:n.z.literal("error"),error:n.z.string().optional()})]),n.z.discriminatedUnion("status",[n.z.object({executionId:n.z.string(),status:n.z.literal("started"),pid:n.z.number().optional(),command:n.z.string()}),n.z.object({executionId:n.z.string(),status:n.z.literal("output"),output:n.z.string()}),n.z.object({executionId:n.z.string(),status:n.z.literal("exited"),exitCode:n.z.number().optional()}),n.z.object({executionId:n.z.string(),status:n.z.literal("fallback")}),n.z.object({executionId:n.z.string(),status:n.z.literal("timeout")})]);let eK=n.z.enum(["pending","in_progress","completed"]);n.z.object({id:n.z.string(),content:n.z.string(),status:eK})}};