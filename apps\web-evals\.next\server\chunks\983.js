"use strict";exports.id=983,exports.ids=[983],exports.modules={12:(e,t,i)=>{i.d(t,{Fx:()=>l,kn:()=>a});var r=i(21153),n=i(68052);class s extends n.pe{static [r.i]="PgJsonbBuilder";constructor(e){super(e,"json","PgJsonb")}build(e){return new a(e,this.config)}}class a extends n.Kl{static [r.i]="PgJsonb";constructor(e,t){super(e,t)}getSQLType(){return"jsonb"}mapToDriverValue(e){return JSON.stringify(e)}mapFromDriverValue(e){if("string"==typeof e)try{return JSON.parse(e)}catch{}return e}}function l(e){return new s(e??"")}},4666:(e,t,i)=>{i.d(t,{dw:()=>c,p6:()=>h,qw:()=>o});var r=i(21153),n=i(88965),s=i(68052),a=i(16731);class l extends a.u{static [r.i]="PgDateBuilder";constructor(e){super(e,"date","PgDate")}build(e){return new o(e,this.config)}}class o extends s.Kl{static [r.i]="PgDate";getSQLType(){return"date"}mapFromDriverValue(e){return new Date(e)}mapToDriverValue(e){return e.toISOString()}}class u extends a.u{static [r.i]="PgDateStringBuilder";constructor(e){super(e,"string","PgDateString")}build(e){return new c(e,this.config)}}class c extends s.Kl{static [r.i]="PgDateString";getSQLType(){return"date"}}function h(e,t){let{name:i,config:r}=(0,n.Ll)(e,t);return r?.mode==="date"?new l(i):new u(i)}},10992:(e,t,i)=>{i.d(t,{mu:()=>eV,cJ:()=>eX});var r=i(21153),n=i(11064),s=i(88965),a=i(68052),l=i(44106);class o extends l.p{static [r.i]="PgBigInt53Builder";constructor(e){super(e,"number","PgBigInt53")}build(e){return new u(e,this.config)}}class u extends a.Kl{static [r.i]="PgBigInt53";getSQLType(){return"bigint"}mapFromDriverValue(e){return"number"==typeof e?e:Number(e)}}class c extends l.p{static [r.i]="PgBigInt64Builder";constructor(e){super(e,"bigint","PgBigInt64")}build(e){return new h(e,this.config)}}class h extends a.Kl{static [r.i]="PgBigInt64";getSQLType(){return"bigint"}mapFromDriverValue(e){return BigInt(e)}}function f(e,t){let{name:i,config:r}=(0,s.Ll)(e,t);return"number"===r.mode?new o(i):new c(i)}class d extends a.pe{static [r.i]="PgBigSerial53Builder";constructor(e){super(e,"number","PgBigSerial53"),this.config.hasDefault=!0,this.config.notNull=!0}build(e){return new p(e,this.config)}}class p extends a.Kl{static [r.i]="PgBigSerial53";getSQLType(){return"bigserial"}mapFromDriverValue(e){return"number"==typeof e?e:Number(e)}}class m extends a.pe{static [r.i]="PgBigSerial64Builder";constructor(e){super(e,"bigint","PgBigSerial64"),this.config.hasDefault=!0}build(e){return new g(e,this.config)}}class g extends a.Kl{static [r.i]="PgBigSerial64";getSQLType(){return"bigserial"}mapFromDriverValue(e){return BigInt(e)}}function y(e,t){let{name:i,config:r}=(0,s.Ll)(e,t);return"number"===r.mode?new d(i):new m(i)}var b=i(22520);class w extends a.pe{static [r.i]="PgCharBuilder";constructor(e,t){super(e,"string","PgChar"),this.config.length=t.length,this.config.enumValues=t.enum}build(e){return new v(e,this.config)}}class v extends a.Kl{static [r.i]="PgChar";length=this.config.length;enumValues=this.config.enumValues;getSQLType(){return void 0===this.length?"char":`char(${this.length})`}}function S(e,t={}){let{name:i,config:r}=(0,s.Ll)(e,t);return new w(i,r)}class T extends a.pe{static [r.i]="PgCidrBuilder";constructor(e){super(e,"string","PgCidr")}build(e){return new N(e,this.config)}}class N extends a.Kl{static [r.i]="PgCidr";getSQLType(){return"cidr"}}function P(e){return new T(e??"")}class $ extends a.pe{static [r.i]="PgCustomColumnBuilder";constructor(e,t,i){super(e,"custom","PgCustomColumn"),this.config.fieldConfig=t,this.config.customTypeParams=i}build(e){return new x(e,this.config)}}class x extends a.Kl{static [r.i]="PgCustomColumn";sqlName;mapTo;mapFrom;constructor(e,t){super(e,t),this.sqlName=t.customTypeParams.dataType(t.fieldConfig),this.mapTo=t.customTypeParams.toDriver,this.mapFrom=t.customTypeParams.fromDriver}getSQLType(){return this.sqlName}mapFromDriverValue(e){return"function"==typeof this.mapFrom?this.mapFrom(e):e}mapToDriverValue(e){return"function"==typeof this.mapTo?this.mapTo(e):e}}function C(e){return(t,i)=>{let{name:r,config:n}=(0,s.Ll)(t,i);return new $(r,n,e)}}var q=i(4666);class A extends a.pe{static [r.i]="PgDoublePrecisionBuilder";constructor(e){super(e,"number","PgDoublePrecision")}build(e){return new I(e,this.config)}}class I extends a.Kl{static [r.i]="PgDoublePrecision";getSQLType(){return"double precision"}mapFromDriverValue(e){return"string"==typeof e?Number.parseFloat(e):e}}function O(e){return new A(e??"")}class j extends a.pe{static [r.i]="PgInetBuilder";constructor(e){super(e,"string","PgInet")}build(e){return new B(e,this.config)}}class B extends a.Kl{static [r.i]="PgInet";getSQLType(){return"inet"}}function E(e){return new j(e??"")}var _=i(17542);class L extends a.pe{static [r.i]="PgIntervalBuilder";constructor(e,t){super(e,"string","PgInterval"),this.config.intervalConfig=t}build(e){return new D(e,this.config)}}class D extends a.Kl{static [r.i]="PgInterval";fields=this.config.intervalConfig.fields;precision=this.config.intervalConfig.precision;getSQLType(){let e=this.fields?` ${this.fields}`:"",t=this.precision?`(${this.precision})`:"";return`interval${e}${t}`}}function k(e,t={}){let{name:i,config:r}=(0,s.Ll)(e,t);return new L(i,r)}var z=i(50844),Q=i(12);class F extends a.pe{static [r.i]="PgLineBuilder";constructor(e){super(e,"array","PgLine")}build(e){return new V(e,this.config)}}class V extends a.Kl{static [r.i]="PgLine";getSQLType(){return"line"}mapFromDriverValue(e){let[t,i,r]=e.slice(1,-1).split(",");return[Number.parseFloat(t),Number.parseFloat(i),Number.parseFloat(r)]}mapToDriverValue(e){return`{${e[0]},${e[1]},${e[2]}}`}}class X extends a.pe{static [r.i]="PgLineABCBuilder";constructor(e){super(e,"json","PgLineABC")}build(e){return new U(e,this.config)}}class U extends a.Kl{static [r.i]="PgLineABC";getSQLType(){return"line"}mapFromDriverValue(e){let[t,i,r]=e.slice(1,-1).split(",");return{a:Number.parseFloat(t),b:Number.parseFloat(i),c:Number.parseFloat(r)}}mapToDriverValue(e){return`{${e.a},${e.b},${e.c}}`}}function M(e,t){let{name:i,config:r}=(0,s.Ll)(e,t);return r?.mode&&"tuple"!==r.mode?new X(i):new F(i)}class K extends a.pe{static [r.i]="PgMacaddrBuilder";constructor(e){super(e,"string","PgMacaddr")}build(e){return new R(e,this.config)}}class R extends a.Kl{static [r.i]="PgMacaddr";getSQLType(){return"macaddr"}}function J(e){return new K(e??"")}class W extends a.pe{static [r.i]="PgMacaddr8Builder";constructor(e){super(e,"string","PgMacaddr8")}build(e){return new G(e,this.config)}}class G extends a.Kl{static [r.i]="PgMacaddr8";getSQLType(){return"macaddr8"}}function H(e){return new W(e??"")}var Y=i(19439);class Z extends a.pe{static [r.i]="PgPointTupleBuilder";constructor(e){super(e,"array","PgPointTuple")}build(e){return new ee(e,this.config)}}class ee extends a.Kl{static [r.i]="PgPointTuple";getSQLType(){return"point"}mapFromDriverValue(e){if("string"==typeof e){let[t,i]=e.slice(1,-1).split(",");return[Number.parseFloat(t),Number.parseFloat(i)]}return[e.x,e.y]}mapToDriverValue(e){return`(${e[0]},${e[1]})`}}class et extends a.pe{static [r.i]="PgPointObjectBuilder";constructor(e){super(e,"json","PgPointObject")}build(e){return new ei(e,this.config)}}class ei extends a.Kl{static [r.i]="PgPointObject";getSQLType(){return"point"}mapFromDriverValue(e){if("string"==typeof e){let[t,i]=e.slice(1,-1).split(",");return{x:Number.parseFloat(t),y:Number.parseFloat(i)}}return e}mapToDriverValue(e){return`(${e.x},${e.y})`}}function er(e,t){let{name:i,config:r}=(0,s.Ll)(e,t);return r?.mode&&"tuple"!==r.mode?new et(i):new Z(i)}function en(e,t){let i=new DataView(new ArrayBuffer(8));for(let r=0;r<8;r++)i.setUint8(r,e[t+r]);return i.getFloat64(0,!0)}function es(e){let t=function(e){let t=[];for(let i=0;i<e.length;i+=2)t.push(Number.parseInt(e.slice(i,i+2),16));return new Uint8Array(t)}(e),i=0,r=t[0];i+=1;let n=new DataView(t.buffer),s=n.getUint32(i,1===r);if(i+=4,0x20000000&s&&(n.getUint32(i,1===r),i+=4),(65535&s)==1){let e=en(t,i),r=en(t,i+=8);return i+=8,[e,r]}throw Error("Unsupported geometry type")}class ea extends a.pe{static [r.i]="PgGeometryBuilder";constructor(e){super(e,"array","PgGeometry")}build(e){return new el(e,this.config)}}class el extends a.Kl{static [r.i]="PgGeometry";getSQLType(){return"geometry(point)"}mapFromDriverValue(e){return es(e)}mapToDriverValue(e){return`point(${e[0]} ${e[1]})`}}class eo extends a.pe{static [r.i]="PgGeometryObjectBuilder";constructor(e){super(e,"json","PgGeometryObject")}build(e){return new eu(e,this.config)}}class eu extends a.Kl{static [r.i]="PgGeometryObject";getSQLType(){return"geometry(point)"}mapFromDriverValue(e){let t=es(e);return{x:t[0],y:t[1]}}mapToDriverValue(e){return`point(${e.x} ${e.y})`}}function ec(e,t){let{name:i,config:r}=(0,s.Ll)(e,t);return r?.mode&&"tuple"!==r.mode?new eo(i):new ea(i)}var eh=i(98894);class ef extends a.pe{static [r.i]="PgSerialBuilder";constructor(e){super(e,"number","PgSerial"),this.config.hasDefault=!0,this.config.notNull=!0}build(e){return new ed(e,this.config)}}class ed extends a.Kl{static [r.i]="PgSerial";getSQLType(){return"serial"}}function ep(e){return new ef(e??"")}class em extends l.p{static [r.i]="PgSmallIntBuilder";constructor(e){super(e,"number","PgSmallInt")}build(e){return new eg(e,this.config)}}class eg extends a.Kl{static [r.i]="PgSmallInt";getSQLType(){return"smallint"}mapFromDriverValue=e=>"string"==typeof e?Number(e):e}function ey(e){return new em(e??"")}class eb extends a.pe{static [r.i]="PgSmallSerialBuilder";constructor(e){super(e,"number","PgSmallSerial"),this.config.hasDefault=!0,this.config.notNull=!0}build(e){return new ew(e,this.config)}}class ew extends a.Kl{static [r.i]="PgSmallSerial";getSQLType(){return"smallserial"}}function ev(e){return new eb(e??"")}var eS=i(98741),eT=i(52539),eN=i(94396),eP=i(40329);class e$ extends a.pe{static [r.i]="PgVarcharBuilder";constructor(e,t){super(e,"string","PgVarchar"),this.config.length=t.length,this.config.enumValues=t.enum}build(e){return new ex(e,this.config)}}class ex extends a.Kl{static [r.i]="PgVarchar";length=this.config.length;enumValues=this.config.enumValues;getSQLType(){return void 0===this.length?"varchar":`varchar(${this.length})`}}function eC(e,t={}){let{name:i,config:r}=(0,s.Ll)(e,t);return new e$(i,r)}class eq extends a.pe{static [r.i]="PgBinaryVectorBuilder";constructor(e,t){super(e,"string","PgBinaryVector"),this.config.dimensions=t.dimensions}build(e){return new eA(e,this.config)}}class eA extends a.Kl{static [r.i]="PgBinaryVector";dimensions=this.config.dimensions;getSQLType(){return`bit(${this.dimensions})`}}function eI(e,t){let{name:i,config:r}=(0,s.Ll)(e,t);return new eq(i,r)}class eO extends a.pe{static [r.i]="PgHalfVectorBuilder";constructor(e,t){super(e,"array","PgHalfVector"),this.config.dimensions=t.dimensions}build(e){return new ej(e,this.config)}}class ej extends a.Kl{static [r.i]="PgHalfVector";dimensions=this.config.dimensions;getSQLType(){return`halfvec(${this.dimensions})`}mapToDriverValue(e){return JSON.stringify(e)}mapFromDriverValue(e){return e.slice(1,-1).split(",").map(e=>Number.parseFloat(e))}}function eB(e,t){let{name:i,config:r}=(0,s.Ll)(e,t);return new eO(i,r)}class eE extends a.pe{static [r.i]="PgSparseVectorBuilder";constructor(e,t){super(e,"string","PgSparseVector"),this.config.dimensions=t.dimensions}build(e){return new e_(e,this.config)}}class e_ extends a.Kl{static [r.i]="PgSparseVector";dimensions=this.config.dimensions;getSQLType(){return`sparsevec(${this.dimensions})`}}function eL(e,t){let{name:i,config:r}=(0,s.Ll)(e,t);return new eE(i,r)}class eD extends a.pe{static [r.i]="PgVectorBuilder";constructor(e,t){super(e,"array","PgVector"),this.config.dimensions=t.dimensions}build(e){return new ek(e,this.config)}}class ek extends a.Kl{static [r.i]="PgVector";dimensions=this.config.dimensions;getSQLType(){return`vector(${this.dimensions})`}mapToDriverValue(e){return JSON.stringify(e)}mapFromDriverValue(e){return e.slice(1,-1).split(",").map(e=>Number.parseFloat(e))}}function ez(e,t){let{name:i,config:r}=(0,s.Ll)(e,t);return new eD(i,r)}let eQ=Symbol.for("drizzle:PgInlineForeignKeys"),eF=Symbol.for("drizzle:EnableRLS");class eV extends n.XI{static [r.i]="PgTable";static Symbol=Object.assign({},n.XI.Symbol,{InlineForeignKeys:eQ,EnableRLS:eF});[eQ]=[];[eF]=!1;[n.XI.Symbol.ExtraConfigBuilder]=void 0;[n.XI.Symbol.ExtraConfigColumns]={}}let eX=(e,t,i)=>(function(e,t,i,r,s=e){let a=new eV(e,r,s),l="function"==typeof t?t({bigint:f,bigserial:y,boolean:b.zM,char:S,cidr:P,customType:C,date:q.p6,doublePrecision:O,inet:E,integer:_.nd,interval:k,json:z.Pq,jsonb:Q.Fx,line:M,macaddr:J,macaddr8:H,numeric:Y.sH,point:er,geometry:ec,real:eh.x,serial:ep,smallint:ey,smallserial:ev,text:eS.Qq,time:eT.kB,timestamp:eN.vE,uuid:eP.uR,varchar:eC,bit:eI,halfvec:eB,sparsevec:eL,vector:ez}):t,o=Object.fromEntries(Object.entries(l).map(([e,t])=>{t.setName(e);let i=t.build(a);return a[eQ].push(...t.buildForeignKeys(i,a)),[e,i]})),u=Object.fromEntries(Object.entries(l).map(([e,t])=>(t.setName(e),[e,t.buildExtraConfigColumn(a)]))),c=Object.assign(a,o);return c[n.XI.Symbol.Columns]=o,c[n.XI.Symbol.ExtraConfigColumns]=u,i&&(c[eV.Symbol.ExtraConfigBuilder]=i),Object.assign(c,{enableRLS:()=>(c[eV.Symbol.EnableRLS]=!0,c)})})(e,t,i,void 0)},11064:(e,t,i)=>{i.d(t,{HE:()=>c,Io:()=>p,Lf:()=>m,Sj:()=>s,XI:()=>d,e:()=>a});var r=i(21153),n=i(49571);let s=Symbol.for("drizzle:Schema"),a=Symbol.for("drizzle:Columns"),l=Symbol.for("drizzle:ExtraConfigColumns"),o=Symbol.for("drizzle:OriginalName"),u=Symbol.for("drizzle:BaseName"),c=Symbol.for("drizzle:IsAlias"),h=Symbol.for("drizzle:ExtraConfigBuilder"),f=Symbol.for("drizzle:IsDrizzleTable");class d{static [r.i]="Table";static Symbol={Name:n.E,Schema:s,OriginalName:o,Columns:a,ExtraConfigColumns:l,BaseName:u,IsAlias:c,ExtraConfigBuilder:h};[n.E];[o];[s];[a];[l];[u];[c]=!1;[f]=!0;[h]=void 0;constructor(e,t,i){this[n.E]=this[o]=e,this[s]=t,this[u]=i}}function p(e){return e[n.E]}function m(e){return`${e[s]??"public"}.${e[n.E]}`}},15224:(e,t,i)=>{i.d(t,{GL:()=>u});var r=i(77952),n=i(21153),s=i(68052);class a{constructor(e,t){this.unique=e,this.name=t}static [n.i]="PgIndexBuilderOn";on(...e){return new l(e.map(e=>{if((0,n.is)(e,r.Xs))return e;let t=new s.ae(e.name,!!e.keyAsName,e.columnType,e.indexConfig);return e.indexConfig=JSON.parse(JSON.stringify(e.defaultConfig)),t}),this.unique,!1,this.name)}onOnly(...e){return new l(e.map(e=>{if((0,n.is)(e,r.Xs))return e;let t=new s.ae(e.name,!!e.keyAsName,e.columnType,e.indexConfig);return e.indexConfig=e.defaultConfig,t}),this.unique,!0,this.name)}using(e,...t){return new l(t.map(e=>{if((0,n.is)(e,r.Xs))return e;let t=new s.ae(e.name,!!e.keyAsName,e.columnType,e.indexConfig);return e.indexConfig=JSON.parse(JSON.stringify(e.defaultConfig)),t}),this.unique,!0,this.name,e)}}class l{static [n.i]="PgIndexBuilder";config;constructor(e,t,i,r,n="btree"){this.config={name:r,columns:e,unique:t,only:i,method:n}}concurrently(){return this.config.concurrently=!0,this}with(e){return this.config.with=e,this}where(e){return this.config.where=e,this}build(e){return new o(this.config,e)}}class o{static [n.i]="PgIndex";config;constructor(e,t){this.config={...e,table:t}}}function u(e){return new a(!0,e)}},16731:(e,t,i)=>{i.d(t,{u:()=>a});var r=i(21153),n=i(77952),s=i(68052);class a extends s.pe{static [r.i]="PgDateColumnBaseBuilder";defaultNow(){return this.default((0,n.ll)`now()`)}}},17542:(e,t,i)=>{i.d(t,{nd:()=>o});var r=i(21153),n=i(68052),s=i(44106);class a extends s.p{static [r.i]="PgIntegerBuilder";constructor(e){super(e,"number","PgInteger")}build(e){return new l(e,this.config)}}class l extends n.Kl{static [r.i]="PgInteger";getSQLType(){return"integer"}mapFromDriverValue(e){return"string"==typeof e?Number.parseInt(e):e}}function o(e){return new a(e??"")}},19439:(e,t,i)=>{i.d(t,{Z5:()=>l,sH:()=>f});var r=i(21153),n=i(88965),s=i(68052);class a extends s.pe{static [r.i]="PgNumericBuilder";constructor(e,t,i){super(e,"string","PgNumeric"),this.config.precision=t,this.config.scale=i}build(e){return new l(e,this.config)}}class l extends s.Kl{static [r.i]="PgNumeric";precision;scale;constructor(e,t){super(e,t),this.precision=t.precision,this.scale=t.scale}mapFromDriverValue(e){return"string"==typeof e?e:String(e)}getSQLType(){return void 0!==this.precision&&void 0!==this.scale?`numeric(${this.precision}, ${this.scale})`:void 0===this.precision?"numeric":`numeric(${this.precision})`}}class o extends s.pe{static [r.i]="PgNumericNumberBuilder";constructor(e,t,i){super(e,"number","PgNumericNumber"),this.config.precision=t,this.config.scale=i}build(e){return new u(e,this.config)}}class u extends s.Kl{static [r.i]="PgNumericNumber";precision;scale;constructor(e,t){super(e,t),this.precision=t.precision,this.scale=t.scale}mapFromDriverValue(e){return"number"==typeof e?e:Number(e)}mapToDriverValue=String;getSQLType(){return void 0!==this.precision&&void 0!==this.scale?`numeric(${this.precision}, ${this.scale})`:void 0===this.precision?"numeric":`numeric(${this.precision})`}}class c extends s.pe{static [r.i]="PgNumericBigIntBuilder";constructor(e,t,i){super(e,"bigint","PgNumericBigInt"),this.config.precision=t,this.config.scale=i}build(e){return new h(e,this.config)}}class h extends s.Kl{static [r.i]="PgNumericBigInt";precision;scale;constructor(e,t){super(e,t),this.precision=t.precision,this.scale=t.scale}mapFromDriverValue=BigInt;mapToDriverValue=String;getSQLType(){return void 0!==this.precision&&void 0!==this.scale?`numeric(${this.precision}, ${this.scale})`:void 0===this.precision?"numeric":`numeric(${this.precision})`}}function f(e,t){let{name:i,config:r}=(0,n.Ll)(e,t),s=r?.mode;return"number"===s?new o(i,r?.precision,r?.scale):"bigint"===s?new c(i,r?.precision,r?.scale):new a(i,r?.precision,r?.scale)}},20573:(e,t,i)=>{i.d(t,{n:()=>r});let r=Symbol.for("drizzle:ViewBaseConfig")},21153:(e,t,i)=>{i.d(t,{i:()=>r,is:()=>n});let r=Symbol.for("drizzle:entityKind");function n(e,t){if(!e||"object"!=typeof e)return!1;if(e instanceof t)return!0;if(!Object.prototype.hasOwnProperty.call(t,r))throw Error(`Class "${t.name??"<unknown>"}" doesn't look like a Drizzle entity. If this is incorrect and the class is provided by Drizzle, please report this as a bug.`);let i=Object.getPrototypeOf(e).constructor;if(i)for(;i;){if(r in i&&i[r]===t[r])return!0;i=Object.getPrototypeOf(i)}return!1}Symbol.for("drizzle:hasOwnEntityKind")},22509:(e,t,i)=>{i.d(t,{iv:()=>m,pD:()=>p,DZ:()=>S,_k:()=>b,mm:()=>g,rl:()=>y,I$:()=>function e(t,i,r,a,l=e=>e){let o={};for(let[u,c]of a.entries())if(c.isJson){let n=i.relations[c.tsKey],a=r[u],h="string"==typeof a?JSON.parse(a):a;o[c.tsKey]=(0,s.is)(n,p)?h&&e(t,t[c.relationTableTsKey],h,c.selection,l):h.map(i=>e(t,t[c.relationTableTsKey],i,c.selection,l))}else{let e,t=l(r[u]),i=c.field;e=(0,s.is)(i,n.V)?i:(0,s.is)(i,h.Xs)?i.decoder:i.sql.decoder,o[c.tsKey]=null===t?null:e.mapFromDriverValue(t)}return o},W0:()=>v,K1:()=>w});var r=i(11064),n=i(43216),s=i(21153),a=i(10992);class l{static [s.i]="PgPrimaryKeyBuilder";columns;name;constructor(e,t){this.columns=e,this.name=t}build(e){return new o(e,this.columns,this.name)}}class o{constructor(e,t,i){this.table=e,this.columns=t,this.name=i}static [s.i]="PgPrimaryKey";columns;name;getName(){return this.name??`${this.table[a.mu.Symbol.Name]}_${this.columns.map(e=>e.name).join("_")}_pk`}}var u=i(88589),c=i(23891),h=i(77952);class f{constructor(e,t,i){this.sourceTable=e,this.referencedTable=t,this.relationName=i,this.referencedTableName=t[r.XI.Symbol.Name]}static [s.i]="Relation";referencedTableName;fieldName}class d{constructor(e,t){this.table=e,this.config=t}static [s.i]="Relations"}class p extends f{constructor(e,t,i,r){super(e,t,i?.relationName),this.config=i,this.isNullable=r}static [s.i]="One";withFieldName(e){let t=new p(this.sourceTable,this.referencedTable,this.config,this.isNullable);return t.fieldName=e,t}}class m extends f{constructor(e,t,i){super(e,t,i?.relationName),this.config=i}static [s.i]="Many";withFieldName(e){let t=new m(this.sourceTable,this.referencedTable,this.config);return t.fieldName=e,t}}function g(){return{and:u.Uo,between:u.Tq,eq:u.eq,exists:u.t2,gt:u.gt,gte:u.RO,ilike:u.B3,inArray:u.RV,isNull:u.kZ,isNotNull:u.Pe,like:u.mj,lt:u.lt,lte:u.wJ,ne:u.ne,not:u.AU,notBetween:u.o8,notExists:u.KJ,notLike:u.RK,notIlike:u.q1,notInArray:u.KL,or:u.or,sql:h.ll}}function y(){return{sql:h.ll,asc:c.Y,desc:c.i}}function b(e,t){1===Object.keys(e).length&&"default"in e&&!(0,s.is)(e.default,r.XI)&&(e=e.default);let i={},n={},a={};for(let[o,u]of Object.entries(e))if((0,s.is)(u,r.XI)){let e=(0,r.Lf)(u),t=n[e];for(let n of(i[e]=o,a[o]={tsName:o,dbName:u[r.XI.Symbol.Name],schema:u[r.XI.Symbol.Schema],columns:u[r.XI.Symbol.Columns],relations:t?.relations??{},primaryKey:t?.primaryKey??[]},Object.values(u[r.XI.Symbol.Columns])))n.primary&&a[o].primaryKey.push(n);let c=u[r.XI.Symbol.ExtraConfigBuilder]?.(u[r.XI.Symbol.ExtraConfigColumns]);if(c)for(let e of Object.values(c))(0,s.is)(e,l)&&a[o].primaryKey.push(...e.columns)}else if((0,s.is)(u,d)){let e,s=(0,r.Lf)(u.table),l=i[s];for(let[i,r]of Object.entries(u.config(t(u.table))))if(l){let t=a[l];t.relations[i]=r,e&&t.primaryKey.push(...e)}else s in n||(n[s]={relations:{},primaryKey:e}),n[s].relations[i]=r}return{tables:a,tableNamesMap:i}}function w(e,t){return new d(e,e=>Object.fromEntries(Object.entries(t(e)).map(([e,t])=>[e,t.withFieldName(e)])))}function v(e,t,i){if((0,s.is)(i,p)&&i.config)return{fields:i.config.fields,references:i.config.references};let n=t[(0,r.Lf)(i.referencedTable)];if(!n)throw Error(`Table "${i.referencedTable[r.XI.Symbol.Name]}" not found in schema`);let a=e[n];if(!a)throw Error(`Table "${n}" not found in schema`);let l=i.sourceTable,o=t[(0,r.Lf)(l)];if(!o)throw Error(`Table "${l[r.XI.Symbol.Name]}" not found in schema`);let u=[];for(let e of Object.values(a.relations))(i.relationName&&i!==e&&e.relationName===i.relationName||!i.relationName&&e.referencedTable===i.sourceTable)&&u.push(e);if(u.length>1)throw i.relationName?Error(`There are multiple relations with name "${i.relationName}" in table "${n}"`):Error(`There are multiple relations between "${n}" and "${i.sourceTable[r.XI.Symbol.Name]}". Please specify relation name`);if(u[0]&&(0,s.is)(u[0],p)&&u[0].config)return{fields:u[0].config.references,references:u[0].config.fields};throw Error(`There is not enough information to infer relation "${o}.${i.fieldName}"`)}function S(e){return{one:function(t,i){return new p(e,t,i,i?.fields.reduce((e,t)=>e&&t.notNull,!0)??!1)},many:function(t,i){return new m(e,t,i)}}}},22520:(e,t,i)=>{i.d(t,{zM:()=>l});var r=i(21153),n=i(68052);class s extends n.pe{static [r.i]="PgBooleanBuilder";constructor(e){super(e,"boolean","PgBoolean")}build(e){return new a(e,this.config)}}class a extends n.Kl{static [r.i]="PgBoolean";getSQLType(){return"boolean"}}function l(e){return new s(e??"")}},22954:(e,t,i)=>{i.d(t,{i:()=>r});function r(e,...t){return e(...t)}},23891:(e,t,i)=>{i.d(t,{Y:()=>n,i:()=>s});var r=i(77952);function n(e){return(0,r.ll)`${e} asc`}function s(e){return(0,r.ll)`${e} desc`}},40078:(e,t,i)=>{i.d(t,{A:()=>ev});var r=i(21820),n=i(29021);let s=new Map,a=new Map,l=Symbol("OriginError"),o={};class u extends Promise{constructor(e,t,i,r,n={}){let a,o;super((e,t)=>{a=e,o=t}),this.tagged=Array.isArray(e.raw),this.strings=e,this.args=t,this.handler=i,this.canceller=r,this.options=n,this.state=null,this.statement=null,this.resolve=e=>(this.active=!1,a(e)),this.reject=e=>(this.active=!1,o(e)),this.active=!1,this.cancelled=null,this.executed=!1,this.signature="",this[l]=this.handler.debug?Error():this.tagged&&function(e){if(s.has(e))return s.get(e);let t=Error.stackTraceLimit;return Error.stackTraceLimit=4,s.set(e,Error()),Error.stackTraceLimit=t,s.get(e)}(this.strings)}get origin(){return(this.handler.debug?this[l].stack:this.tagged&&a.has(this.strings)?a.get(this.strings):a.set(this.strings,this[l].stack).get(this.strings))||""}static get[Symbol.species](){return Promise}cancel(){return this.canceller&&(this.canceller(this),this.canceller=null)}simple(){return this.options.simple=!0,this.options.prepare=!1,this}async readable(){return this.simple(),this.streaming=!0,this}async writable(){return this.simple(),this.streaming=!0,this}cursor(e=1,t){let i;return(this.options.simple=!1,"function"==typeof e&&(t=e,e=1),this.cursorRows=e,"function"==typeof t)?(this.cursorFn=t,this):{[Symbol.asyncIterator]:()=>({next:()=>{if(this.executed&&!this.active)return{done:!0};i&&i();let e=new Promise((e,t)=>{this.cursorFn=t=>(e({value:t,done:!1}),new Promise(e=>i=e)),this.resolve=()=>(this.active=!1,e({done:!0})),this.reject=e=>(this.active=!1,t(e))});return this.execute(),e},return:()=>(i&&i(o),{done:!0})})}}describe(){return this.options.simple=!1,this.onlyDescribe=this.options.prepare=!0,this}stream(){throw Error(".stream has been renamed to .forEach")}forEach(e){return this.forEachFn=e,this.handle(),this}raw(){return this.isRaw=!0,this}values(){return this.isRaw="values",this}async handle(){!this.executed&&(this.executed=!0)&&await 1&&this.handler(this)}execute(){return this.handle(),this}then(){return this.handle(),super.then.apply(this,arguments)}catch(){return this.handle(),super.catch.apply(this,arguments)}finally(){return this.handle(),super.finally.apply(this,arguments)}}class c extends Error{constructor(e){super(e.message),this.name=this.constructor.name,Object.assign(this,e)}}let h={connection:function e(t,i,r){let{host:n,port:s}=r||i,a=Object.assign(Error("write "+t+" "+(i.path||n+":"+s)),{code:t,errno:t,address:i.path||n},i.path?{}:{port:s});return Error.captureStackTrace(a,e),a},postgres:function e(t){let i=new c(t);return Error.captureStackTrace(i,e),i},generic:function e(t,i){let r=Object.assign(Error(t+": "+i),{code:t});return Error.captureStackTrace(r,e),r},notSupported:function e(t){let i=Object.assign(Error(t+" (B) is not supported"),{code:"MESSAGE_NOT_SUPPORTED",name:t});return Error.captureStackTrace(i,e),i}};class f{then(){$()}catch(){$()}finally(){$()}}class d extends f{constructor(e){super(),this.value=O(e)}}class p extends f{constructor(e,t,i){super(),this.value=e,this.type=t,this.array=i}}class m extends f{constructor(e,t){super(),this.first=e,this.rest=t}build(e,t,i,r){let n=P.map(([t,i])=>({fn:i,i:e.search(t)})).sort((e,t)=>e.i-t.i).pop();return -1===n.i?I(this.first,r):n.fn(this.first,this.rest,t,i,r)}}function g(e,t,i,r){let n=e instanceof p?e.value:e;if(void 0===n&&(e instanceof p?e.value=r.transform.undefined:n=e=r.transform.undefined,void 0===n))throw h.generic("UNDEFINED_VALUE","Undefined values are not allowed");return"$"+i.push(e instanceof p?(t.push(e.value),e.array?e.array[e.type||j(e.value)]||e.type||function e(t){return Array.isArray(t)?e(t[0]):1009*("string"==typeof t)}(e.value):e.type):(t.push(e),j(e)))}let y=A({string:{to:25,from:null,serialize:e=>""+e},number:{to:0,from:[21,23,26,700,701],serialize:e=>""+e,parse:e=>+e},json:{to:114,from:[114,3802],serialize:e=>JSON.stringify(e),parse:e=>JSON.parse(e)},boolean:{to:16,from:16,serialize:e=>!0===e?"t":"f",parse:e=>"t"===e},date:{to:1184,from:[1082,1114,1184],serialize:e=>(e instanceof Date?e:new Date(e)).toISOString(),parse:e=>new Date(e)},bytea:{to:17,from:17,serialize:e=>"\\x"+Buffer.from(e).toString("hex"),parse:e=>Buffer.from(e.slice(2),"hex")}});function b(e,t,i,r,n,s){for(let a=1;a<e.strings.length;a++)t+=w(t,i,r,n,s)+e.strings[a],i=e.args[a];return t}function w(e,t,i,r,n){return t instanceof m?t.build(e,i,r,n):t instanceof u?v(t,i,r,n):t instanceof d?t.value:t&&t[0]instanceof u?t.reduce((e,t)=>e+" "+v(t,i,r,n),""):g(t,i,r,n)}function v(e,t,i,r){return e.fragment=!0,b(e,e.strings[0],e.args[0],t,i,r)}function S(e,t,i,r,n){return e.map(e=>"("+r.map(r=>w("values",e[r],t,i,n)).join(",")+")").join(",")}function T(e,t,i,r,n){let s=Array.isArray(e[0]),a=t.length?t.flat():Object.keys(s?e[0]:e);return S(s?e:[e],i,r,a,n)}function N(e,t,i,r,n){let s;return("string"==typeof e&&(e=[e].concat(t)),Array.isArray(e))?I(e,n):(t.length?t.flat():Object.keys(e)).map(t=>((s=e[t])instanceof u?v(s,i,r,n):s instanceof d?s.value:g(s,i,r,n))+" as "+O(n.transform.column.to?n.transform.column.to(t):t)).join(",")}let P=Object.entries({values:T,in:(...e)=>{let t=T(...e);return"()"===t?"(null)":t},select:N,as:N,returning:N,"\\(":N,update:(e,t,i,r,n)=>(t.length?t.flat():Object.keys(e)).map(t=>O(n.transform.column.to?n.transform.column.to(t):t)+"="+w("values",e[t],i,r,n)),insert(e,t,i,r,n){let s=t.length?t.flat():Object.keys(Array.isArray(e)?e[0]:e);return"("+I(s,n)+")values"+S(Array.isArray(e)?e:[e],i,r,s,n)}}).map(([e,t])=>[RegExp("((?:^|[\\s(])"+e+"(?:$|[\\s(]))(?![\\s\\S]*\\1)","i"),t]);function $(){throw h.generic("NOT_TAGGED_CALL","Query not called as a tagged template literal")}let x=y.serializers,C=y.parsers,q=function(e){let t=A(e||{});return{serializers:Object.assign({},x,t.serializers),parsers:Object.assign({},C,t.parsers)}};function A(e){return Object.keys(e).reduce((t,i)=>(e[i].from&&[].concat(e[i].from).forEach(r=>t.parsers[r]=e[i].parse),e[i].serialize&&(t.serializers[e[i].to]=e[i].serialize,e[i].from&&[].concat(e[i].from).forEach(r=>t.serializers[r]=e[i].serialize)),t),{parsers:{},serializers:{}})}function I(e,{transform:{column:t}}){return e.map(e=>O(t.to?t.to(e):e)).join(",")}let O=function(e){return'"'+e.replace(/"/g,'""').replace(/\./g,'"."')+'"'},j=function e(t){return t instanceof p?t.type:t instanceof Date?1184:t instanceof Uint8Array?17:!0===t||!1===t?16:"bigint"==typeof t?20:Array.isArray(t)?e(t[0]):0},B=/\\/g,E=/"/g,_=function e(t,i,r,n){if(!1===Array.isArray(t))return t;if(!t.length)return"{}";let s=t[0],a=1020===n?";":",";return Array.isArray(s)&&!s.type?"{"+t.map(t=>e(t,i,r,n)).join(a)+"}":"{"+t.map(e=>{if(void 0===e&&void 0===(e=r.transform.undefined))throw h.generic("UNDEFINED_VALUE","Undefined values are not allowed");return null===e?"null":'"'+(i?i(e.type?e.value:e):""+e).replace(B,"\\\\").replace(E,'\\"')+'"'}).join(a)+"}"},L={i:0,char:null,str:"",quoted:!1,last:0},D=e=>{let t=e[0];for(let i=1;i<e.length;i++)t+="_"===e[i]?e[++i].toUpperCase():e[i];return t},k=e=>{let t=e[0].toUpperCase();for(let i=1;i<e.length;i++)t+="_"===e[i]?e[++i].toUpperCase():e[i];return t},z=e=>e.replace(/_/g,"-"),Q=e=>e.replace(/([A-Z])/g,"_$1").toLowerCase(),F=e=>(e.slice(0,1)+e.slice(1).replace(/([A-Z])/g,"_$1")).toLowerCase(),V=e=>e.replace(/-/g,"_");function X(e){return function t(i,r){return"object"==typeof i&&null!==i&&(114===r.type||3802===r.type)?Array.isArray(i)?i.map(e=>t(e,r)):Object.entries(i).reduce((i,[n,s])=>Object.assign(i,{[e(n)]:t(s,r)}),{}):i}}D.column={from:D},D.value={from:X(D)},Q.column={to:Q};let U={...D};U.column.to=Q,k.column={from:k},k.value={from:X(k)},F.column={to:F};let M={...k};M.column.to=F,z.column={from:z},z.value={from:X(z)},V.column={to:V};let K={...z};K.column.to=V;var R=i(91645),J=i(34631),W=i(55511),G=i(27910),H=i(74998);class Y extends Array{constructor(){super(),Object.defineProperties(this,{count:{value:null,writable:!0},state:{value:null,writable:!0},command:{value:null,writable:!0},columns:{value:null,writable:!0},statement:{value:null,writable:!0}})}static get[Symbol.species](){return Array}}let Z=function(e=[]){let t=e.slice(),i=0;return{get length(){return t.length-i},remove:e=>{let i=t.indexOf(e);return -1===i?null:(t.splice(i,1),e)},push:e=>(t.push(e),e),shift:()=>{let e=t[i++];return i===t.length?(i=0,t=[]):t[i-1]=void 0,e}}},ee=Buffer.allocUnsafe(256),et=Object.assign(function(){return et.i=0,et},"BCcDdEFfHPpQSX".split("").reduce((e,t)=>{let i=t.charCodeAt(0);return e[t]=()=>(ee[0]=i,et.i=5,et),e},{}),{N:"\0",i:0,inc:e=>(et.i+=e,et),str(e){let t=Buffer.byteLength(e);return ei(t),et.i+=ee.write(e,et.i,t,"utf8"),et},i16:e=>(ei(2),ee.writeUInt16BE(e,et.i),et.i+=2,et),i32:(e,t)=>(t||0===t?ee.writeUInt32BE(e,t):(ei(4),ee.writeUInt32BE(e,et.i),et.i+=4),et),z:e=>(ei(e),ee.fill(0,et.i,et.i+e),et.i+=e,et),raw:e=>(ee=Buffer.concat([ee.subarray(0,et.i),e]),et.i=ee.length,et),end(e=1){ee.writeUInt32BE(et.i-e,e);let t=ee.subarray(0,et.i);return et.i=0,ee=Buffer.allocUnsafe(256),t}});function ei(e){if(ee.length-et.i<e){let t=ee,i=t.length;ee=Buffer.allocUnsafe(i+(i>>1)+e),t.copy(ee)}}let er=function e(t,i={},{onopen:r=ec,onend:n=ec,onclose:s=ec}={}){let{ssl:a,max:l,user:c,host:f,port:d,database:p,parsers:m,transform:y,onnotice:w,onnotify:v,onparameter:S,max_pipeline:T,keep_alive:N,backoff:P,target_session_attrs:$}=t,x=Z(),C=en++,q={pid:null,secret:null},A=eg(eK,t.idle_timeout),I=eg(eK,t.max_lifetime),O=eg(function(){eU(h.connection("CONNECT_TIMEOUT",t,j)),j.destroy()},t.connect_timeout),j=null,B,E=new Y,D=Buffer.alloc(0),k=t.fetch_types,z={},Q={},F=Math.random().toString(36).slice(2),V=1,X=0,U=0,M=0,K=0,ee=0,ei=0,er=0,ef=null,ey=null,eb=!1,ew=null,ev=null,eS=null,eT=null,eN=null,eP=null,e$=null,ex=null,eC=null,eq=null,eA={queue:i.closed,idleTimer:A,connect(e){eS=e,eF()},terminate:eR,execute:ej,cancel:eO,end:eK,count:0,id:C};return i.closed&&i.closed.push(eA),eA;async function eI(){let e;try{e=t.socket?await Promise.resolve(t.socket(t)):new R.Socket}catch(e){eX(e);return}return e.on("error",eX),e.on("close",eJ),e.on("drain",ek),e}async function eO({pid:e,secret:t},i,r){try{B=et().i32(16).i32(0x4d2162e).i32(e).i32(t).end(16),await eQ(),j.once("error",r),j.once("close",i)}catch(e){r(e)}}function ej(e){if(eb)return eM(e,h.connection("CONNECTION_DESTROYED",t));if(!e.cancelled)try{return e.state=q,eC?x.push(e):(eC=e).active=!0,function(e){let i=[],r=[],n=b(e,e.strings[0],e.args[0],i,r,t);e.tagged||e.args.forEach(e=>g(e,i,r,t)),e.prepare=t.prepare&&(!("prepare"in e.options)||e.options.prepare),e.string=n,e.signature=e.prepare&&r+n,e.onlyDescribe&&delete Q[e.signature],e.parameters=e.parameters||i,e.prepared=e.prepare&&e.signature in Q,e.describeFirst=e.onlyDescribe||i.length&&!e.prepared,e.statement=e.prepared?Q[e.signature]:{string:n,types:r,name:e.prepare?F+V++:""},"function"==typeof t.debug&&t.debug(C,n,i,r)}(e),e_(function(e){var t;if(e.parameters.length>=65534)throw h.generic("MAX_PARAMETERS_EXCEEDED","Max number of parameters (65534) exceeded");return e.options.simple?et().Q().str(e.statement.string+et.N).end():e.describeFirst?Buffer.concat([eB(e),ea]):e.prepare?e.prepared?eE(e):Buffer.concat([eB(e),eE(e)]):(t=e,Buffer.concat([e3(t.statement.string,t.parameters,t.statement.types),eu,eE(t)]))}(e))&&!e.describeFirst&&!e.cursorFn&&x.length<T&&(!e.options.onexecute||e.options.onexecute(eA))}catch(e){return 0===x.length&&e_(es),eU(e),!0}}function eB(e){return Buffer.concat([e3(e.statement.string,e.parameters,e.statement.types,e.statement.name),function(e,t=""){return et().D().str("S").str(t+et.N).end()}("S",e.statement.name)])}function eE(e){return Buffer.concat([function(e,i,r="",n=""){let s,a;return et().B().str(n+et.N).str(r+et.N).i16(0).i16(e.length),e.forEach((r,n)=>{if(null===r)return et.i32(0xffffffff);a=i[n],e[n]=r=a in t.serializers?t.serializers[a](r):""+r,s=et.i,et.inc(4).str(r).i32(et.i-s-4,s)}),et.i16(0),et.end()}(e.parameters,e.statement.types,e.statement.name,e.cursorName),e.cursorFn?e8("",e.cursorRows):eo])}function e_(e,t){return(eP=eP?Buffer.concat([eP,e]):Buffer.from(e),t||eP.length>=1024)?eL(t):(null===ey&&(ey=setImmediate(eL)),!0)}function eL(e){let t=j.write(eP,e);return null!==ey&&clearImmediate(ey),eP=ey=null,t}async function eD(){if(e_(el),!await new Promise(e=>j.once("data",t=>e(83===t[0])))&&"prefer"===a)return eV();j.removeAllListeners(),(j=J.connect({socket:j,servername:R.isIP(j.host)?void 0:j.host,..."require"===a||"allow"===a||"prefer"===a?{rejectUnauthorized:!1}:"verify-full"===a?{}:"object"==typeof a?a:{}})).on("secureConnect",eV),j.on("error",eX),j.on("close",eJ),j.on("drain",ek)}function ek(){eC||r(eA)}function ez(i){if(!ew||(ew.push(i),!((U-=i.length)>0)))for(D=ew?Buffer.concat(ew,ee-U):0===D.length?i:Buffer.concat([D,i],D.length+i.length);D.length>4;){if((ee=D.readUInt32BE(1))>=D.length){U=ee-D.length,ew=[D];break}try{(function(i,n=i[0]){(68===n?function(e){let t,i,r,n=7,s=eC.isRaw?Array(eC.statement.columns.length):{};for(let a=0;a<eC.statement.columns.length;a++)i=eC.statement.columns[a],t=e.readInt32BE(n),n+=4,r=-1===t?null:!0===eC.isRaw?e.subarray(n,n+=t):void 0===i.parser?e.toString("utf8",n,n+=t):!0===i.parser.array?i.parser(e.toString("utf8",n+1,n+=t)):i.parser(e.toString("utf8",n,n+=t)),eC.isRaw?s[a]=!0===eC.isRaw?r:y.value.from?y.value.from(r,i):r:s[i.name]=y.value.from?y.value.from(r,i):r;eC.forEachFn?eC.forEachFn(y.row.from?y.row.from(s):s,E):E[er++]=y.row.from?y.row.from(s):s}:100===n?function(e){eN&&(eN.push(e.subarray(5))||j.pause())}:65===n?function(e){if(!v)return;let t=9;for(;0!==e[t++];);v(e.toString("utf8",9,t-1),e.toString("utf8",t,e.length-1))}:83===n?function(e){let[i,r]=e.toString("utf8",5,e.length-1).split(et.N);z[i]=r,t.parameters[i]!==r&&(t.parameters[i]=r,S&&S(i,r))}:90===n?function(i){if(eC&&eC.options.simple&&eC.resolve(ev||E),eC=ev=null,E=new Y,O.cancel(),eS){if($)if(z.in_hot_standby&&z.default_transaction_read_only){var n,s;if(n=$,s=z,"read-write"===n&&"on"===s.default_transaction_read_only||"read-only"===n&&"off"===s.default_transaction_read_only||"primary"===n&&"on"===s.in_hot_standby||"standby"===n&&"off"===s.in_hot_standby||"prefer-standby"===n&&"off"===s.in_hot_standby&&t.host[K])return eR()}else{let e=new u([`
      show transaction_read_only;
      select pg_catalog.pg_is_in_recovery()
    `],[],ej,null,{simple:!0});e.resolve=([[e],[t]])=>{z.default_transaction_read_only=e.transaction_read_only,z.in_hot_standby=t.pg_is_in_recovery?"on":"off"},e.execute();return}return k?(eS.reserve&&(eS=null),e2()):void(eS&&!eS.reserve&&ej(eS),t.shared.retries=K=0,eS=null)}for(;x.length&&(eC=x.shift())&&(eC.active=!0,eC.cancelled);)e(t).cancel(eC.state,eC.cancelled.resolve,eC.cancelled.reject);eC||(eA.reserved?eA.reserved.release||73!==i[5]?eA.reserved():eT?eR():(eA.reserved=null,r(eA)):eT?eR():r(eA))}:67===n?function(e){er=0;for(let t=e.length-1;t>0;t--)if(32===e[t]&&e[t+1]<58&&null===E.count&&(E.count=+e.toString("utf8",t+1,e.length-1)),e[t-1]>=65){E.command=e.toString("utf8",5,t),E.state=q;break}return(eq&&(eq(),eq=null),"BEGIN"!==E.command||1===l||eA.reserved)?eC.options.simple?eW():void(eC.cursorFn&&(E.count&&eC.cursorFn(E),e_(es)),eC.resolve(E)):eU(h.generic("UNSAFE_TRANSACTION","Only use sql.begin, sql.reserved or max: 1"))}:50===n?eW:49===n?function(){eC.parsing=!1}:116===n?function(e){let t=e.readUInt16BE(5);for(let i=0;i<t;++i)eC.statement.types[i]||(eC.statement.types[i]=e.readUInt32BE(7+4*i));eC.prepare&&(Q[eC.signature]=eC.statement),eC.describeFirst&&!eC.onlyDescribe&&(e_(eE(eC)),eC.describeFirst=!1)}:84===n?function(e){let t;E.command&&((ev=ev||[E]).push(E=new Y),E.count=null,eC.statement.columns=null);let i=e.readUInt16BE(5),r=7;eC.statement.columns=Array(i);for(let n=0;n<i;++n){for(t=r;0!==e[r++];);let i=e.readUInt32BE(r),s=e.readUInt16BE(r+4),a=e.readUInt32BE(r+6);eC.statement.columns[n]={name:y.column.from?y.column.from(e.toString("utf8",t,r-1)):e.toString("utf8",t,r-1),parser:m[a],table:i,number:s,type:a},r+=18}if(E.statement=eC.statement,eC.onlyDescribe)return eC.resolve(eC.statement),e_(es)}:82===n?eG:110===n?function(){if(E.statement=eC.statement,E.statement.columns=[],eC.onlyDescribe)return eC.resolve(eC.statement),e_(es)}:75===n?function(e){q.pid=e.readUInt32BE(5),q.secret=e.readUInt32BE(9)}:69===n?function(e){var t,i;eC&&(eC.cursorFn||eC.describeFirst)&&e_(es);let r=h.postgres(ed(e));eC&&eC.retried?eU(eC.retried):eC&&eC.prepared&&eh.has(r.routine)?(t=eC,i=r,delete Q[t.signature],t.retried=i,ej(t)):eU(r)}:115===n?e5:51===n?function(){E.count&&eC.cursorFn(E),eC.resolve(E)}:71===n?function(){eN=new G.Writable({autoDestroy:!0,write(e,t,i){j.write(et().d().raw(e).end(),i)},destroy(e,t){t(e),j.write(et().f().str(e+et.N).end()),eN=null},final(e){j.write(et().c().end()),eq=e}}),eC.resolve(eN)}:78===n?function(e){w?w(ed(e)):console.log(ed(e))}:72===n?function(){eN=new G.Readable({read(){j.resume()}}),eC.resolve(eN)}:99===n?function(){eN&&eN.push(null),eN=null}:73===n?function(){}:86===n?function(){eU(h.notSupported("FunctionCallResponse"))}:118===n?function(){eU(h.notSupported("NegotiateProtocolVersion"))}:87===n?function(){eN=new G.Duplex({autoDestroy:!0,read(){j.resume()},write(e,t,i){j.write(et().d().raw(e).end(),i)},destroy(e,t){t(e),j.write(et().f().str(e+et.N).end()),eN=null},final(e){j.write(et().c().end()),eq=e}}),eC.resolve(eN)}:function(e){console.error("Postgres.js : Unknown Message:",e[0])})(i)})(D.subarray(0,ee+1))}catch(e){eC&&(eC.cursorFn||eC.describeFirst)&&e_(es),eU(e)}D=D.subarray(ee+1),U=0,ew=null}}async function eQ(){if(eb=!1,z={},j||(j=await eI()),j){if(O.start(),t.socket)return a?eD():eV();if(j.on("connect",a?eD:eV),t.path)return j.connect(t.path);j.ssl=a,j.connect(d[M],f[M]),j.host=f[M],j.port=d[M],M=(M+1)%d.length}}function eF(){setTimeout(eQ,X?X+ei-H.performance.now():0)}function eV(){try{Q={},k=t.fetch_types,F=Math.random().toString(36).slice(2),V=1,I.start(),j.on("data",ez),N&&j.setKeepAlive&&j.setKeepAlive(!0,1e3*N);let e=B||et().inc(4).i16(3).z(2).str(Object.entries(Object.assign({user:c,database:p,client_encoding:"UTF8"},t.connection)).filter(([,e])=>e).map(([e,t])=>e+et.N+t).join(et.N)).z(2).end(0);e_(e)}catch(e){eX(e)}}function eX(e){if(eA.queue!==i.connecting||!t.host[K+1])for(eU(e);x.length;)eM(x.shift(),e)}function eU(e){eN&&(eN.destroy(e),eN=null),eC&&eM(eC,e),eS&&(eM(eS,e),eS=null)}function eM(e,i){if(e.reserve)return e.reject(i);i&&"object"==typeof i||(i=Error(i)),"query"in i||"parameters"in i||Object.defineProperties(i,{stack:{value:i.stack+e.origin.replace(/.*\n/,"\n"),enumerable:t.debug},query:{value:e.string,enumerable:t.debug},parameters:{value:e.parameters,enumerable:t.debug},args:{value:e.args,enumerable:t.debug},types:{value:e.statement&&e.statement.types,enumerable:t.debug}}),e.reject(i)}function eK(){return eT||(eA.reserved||n(eA),eA.reserved||eS||eC||0!==x.length?eT=new Promise(e=>e$=e):(eR(),new Promise(e=>j&&"closed"!==j.readyState?j.once("close",e):e())))}function eR(){eb=!0,(eN||eC||eS||x.length)&&eX(h.connection("CONNECTION_DESTROYED",t)),clearImmediate(ey),j&&(j.removeListener("data",ez),j.removeListener("connect",eV),"open"===j.readyState&&j.end(et().X().end())),e$&&(e$(),eT=e$=null)}async function eJ(e){if(D=Buffer.alloc(0),U=0,ew=null,clearImmediate(ey),j.removeListener("data",ez),j.removeListener("connect",eV),A.cancel(),I.cancel(),O.cancel(),j.removeAllListeners(),j=null,eS)return eF();!e&&(eC||x.length)&&eX(h.connection("CONNECTION_CLOSED",t,j)),X=H.performance.now(),e&&t.shared.retries++,ei=("function"==typeof P?P(t.shared.retries):P)*1e3,s(eA,h.connection("CONNECTION_CLOSED",t,j))}function eW(){E.statement||(E.statement=eC.statement),E.columns=eC.statement.columns}async function eG(e,t=e.readUInt32BE(5)){(3===t?eH:5===t?eY:10===t?eZ:11===t?e0:12===t?function(e){e.toString("utf8",9).split(et.N,1)[0].slice(2)!==ef&&(eU(h.generic("SASL_SIGNATURE_MISMATCH","The server did not return the correct signature")),j.destroy())}:0!==t?function(e,t){console.error("Postgres.js : Unknown Auth:",t)}:ec)(e,t)}async function eH(){let e=await e1();e_(et().p().str(e).z(1).end())}async function eY(e){let t="md5"+await ep(Buffer.concat([Buffer.from(await ep(await e1()+c)),e.subarray(9)]));e_(et().p().str(t).z(1).end())}async function eZ(){ex=(await W.randomBytes(18)).toString("base64"),et().p().str("SCRAM-SHA-256"+et.N);let e=et.i;e_(et.inc(4).str("n,,n=*,r="+ex).i32(et.i-e-4,e).end())}async function e0(e){var t;let i=e.toString("utf8",9).split(",").reduce((e,t)=>(e[t[0]]=t.slice(2),e),{}),r=await W.pbkdf2Sync(await e1(),Buffer.from(i.s,"base64"),parseInt(i.i),32,"sha256"),n=await em(r,"Client Key"),s="n=*,r="+ex+",r="+i.r+",s="+i.s+",i="+i.i+",c=biws,r="+i.r;ef=(await em(await em(r,"Server Key"),s)).toString("base64");let a="c=biws,r="+i.r+",p="+(function(e,t){let i=Math.max(e.length,t.length),r=Buffer.allocUnsafe(i);for(let n=0;n<i;n++)r[n]=e[n]^t[n];return r})(n,Buffer.from(await em(await (t=n,W.createHash("sha256").update(t).digest()),s))).toString("base64");e_(et().p().str(a).end())}function e1(){return Promise.resolve("function"==typeof t.pass?t.pass():t.pass)}async function e2(){k=!1,(await new u([`
      select b.oid, b.typarray
      from pg_catalog.pg_type a
      left join pg_catalog.pg_type b on b.oid = a.typelem
      where a.typcategory = 'A'
      group by b.oid, b.typarray
      order by b.oid
    `],[],ej)).forEach(({oid:e,typarray:i})=>(function(e,i){if(t.parsers[i]&&t.serializers[i])return;let r=t.parsers[e];t.shared.typeArrayMap[e]=i,t.parsers[i]=e=>(L.i=L.last=0,function e(t,i,r,n){let s=[],a=1020===n?";":",";for(;t.i<i.length;t.i++){if(t.char=i[t.i],t.quoted)"\\"===t.char?t.str+=i[++t.i]:'"'===t.char?(s.push(r?r(t.str):t.str),t.str="",t.quoted='"'===i[t.i+1],t.last=t.i+2):t.str+=t.char;else if('"'===t.char)t.quoted=!0;else if("{"===t.char)t.last=++t.i,s.push(e(t,i,r,n));else if("}"===t.char){t.quoted=!1,t.last<t.i&&s.push(r?r(i.slice(t.last,t.i)):i.slice(t.last,t.i)),t.last=t.i+1;break}else t.char===a&&"}"!==t.p&&'"'!==t.p&&(s.push(r?r(i.slice(t.last,t.i)):i.slice(t.last,t.i)),t.last=t.i+1);t.p=t.char}return t.last<t.i&&s.push(r?r(i.slice(t.last,t.i+1)):i.slice(t.last,t.i+1)),s}(L,e,r,i)),t.parsers[i].array=!0,t.serializers[i]=r=>_(r,t.serializers[e],t,i)})(e,i))}async function e5(){try{let e=await Promise.resolve(eC.cursorFn(E));er=0,e===o?e_(function(e=""){return Buffer.concat([et().C().str("P").str(e+et.N).end(),et().S().end()])}(eC.portal)):(E=new Y,e_(e8("",eC.cursorRows)))}catch(e){e_(es),eC.reject(e)}}function e3(e,t,i,r=""){return et().P().str(r+et.N).str(e+et.N).i16(t.length),t.forEach((e,t)=>et.i32(i[t]||0)),et.end()}function e8(e="",t=0){return Buffer.concat([et().E().str(e+et.N).i32(t).end(),ea])}},en=1,es=et().S().end(),ea=et().H().end(),el=et().i32(8).i32(0x4d2162f).end(8),eo=Buffer.concat([et().E().str(et.N).i32(0).end(),es]),eu=et().D().str("S").str(et.N).end(),ec=()=>{},eh=new Set(["FetchPreparedStatement","RevalidateCachedQuery","transformAssignedExpr"]),ef={83:"severity_local",86:"severity",67:"code",77:"message",68:"detail",72:"hint",80:"position",112:"internal_position",113:"internal_query",87:"where",115:"schema_name",116:"table_name",99:"column_name",100:"data type_name",110:"constraint_name",70:"file",76:"line",82:"routine"};function ed(e){let t={},i=5;for(let r=5;r<e.length-1;r++)0===e[r]&&(t[ef[e[i]]]=e.toString("utf8",i+1,r),i=r+1);return t}function ep(e){return W.createHash("md5").update(e).digest("hex")}function em(e,t){return W.createHmac("sha256",e).update(t).digest()}function eg(e,t){let i;if(!(t="function"==typeof t?t():t))return{cancel:ec,start:ec};return{cancel(){i&&(clearTimeout(i),i=null)},start(){i&&clearTimeout(i),i=setTimeout(r,1e3*t,arguments)}};function r(t){e.apply(null,t),i=null}}let ey=()=>{};function eb(e,t,i,r){let n,s,a,l=r.raw?Array(t.length):{};for(let o=0;o<t.length;o++)n=e[i++],s=t[o],a=110===n?null:117===n?void 0:void 0===s.parser?e.toString("utf8",i+4,i+=4+e.readUInt32BE(i)):!0===s.parser.array?s.parser(e.toString("utf8",i+5,i+=4+e.readUInt32BE(i))):s.parser(e.toString("utf8",i+4,i+=4+e.readUInt32BE(i))),r.raw?l[o]=!0===r.raw?a:r.value.from?r.value.from(a,s):a:l[s.name]=r.value.from?r.value.from(a,s):a;return{i:i,row:r.row.from?r.row.from(l):l}}function ew(e,t,i=393216){return new Promise(async(r,n)=>{await e.begin(async e=>{let n;t||([{oid:t}]=await e`select lo_creat(-1) as oid`);let[{fd:s}]=await e`select lo_open(${t}, ${i}) as fd`,a={writable:o,readable:l,close:()=>e`select lo_close(${s})`.then(n),tell:()=>e`select lo_tell64(${s})`,read:t=>e`select loread(${s}, ${t}) as data`,write:t=>e`select lowrite(${s}, ${t})`,truncate:t=>e`select lo_truncate64(${s}, ${t})`,seek:(t,i=0)=>e`select lo_lseek64(${s}, ${t}, ${i})`,size:()=>e`
          select
            lo_lseek64(${s}, location, 0) as position,
            seek.size
          from (
            select
              lo_lseek64($1, 0, 2) as size,
              tell.location
            from (select lo_tell64($1) as location) tell
          ) seek
        `};return r(a),new Promise(async e=>n=e);async function l({highWaterMark:e=16384,start:t=0,end:i=1/0}={}){let r=i-t;return t&&await a.seek(t),new G.Readable({highWaterMark:e,async read(e){let t=e>r?e-r:e;r-=e;let[{data:i}]=await a.read(t);this.push(i),i.length<e&&this.push(null)}})}async function o({highWaterMark:e=16384,start:t=0}={}){return t&&await a.seek(t),new G.Writable({highWaterMark:e,write(e,t,i){a.write(e).then(()=>i(),i)}})}}).catch(n)})}Object.assign(eS,{PostgresError:c,toPascal:k,pascal:M,toCamel:D,camel:U,toKebab:z,kebab:K,fromPascal:F,fromCamel:Q,fromKebab:V,BigInt:{to:20,from:[20],parse:e=>BigInt(e),serialize:e=>e.toString()}});let ev=eS;function eS(e,t){let i=function(e,t){var i;if(e&&e.shared)return e;let n=process.env,s=(e&&"string"!=typeof e?e:t)||{},{url:a,multihost:l}=function(e){if(!e||"string"!=typeof e)return{url:{searchParams:new Map}};let t=e;t=decodeURIComponent((t=t.slice(t.indexOf("://")+3).split(/[?/]/)[0]).slice(t.indexOf("@")+1));let i=new URL(e.replace(t,t.split(",")[0]));return{url:{username:decodeURIComponent(i.username),password:decodeURIComponent(i.password),host:i.host,hostname:i.hostname,port:i.port,pathname:i.pathname,searchParams:i.searchParams},multihost:t.indexOf(",")>-1&&t}}(e),o=[...a.searchParams].reduce((e,[t,i])=>(e[t]=i,e),{}),u=s.hostname||s.host||l||a.hostname||n.PGHOST||"localhost",c=s.port||a.port||n.PGPORT||5432,h=s.user||s.username||a.username||n.PGUSERNAME||n.PGUSER||function(){try{return r.userInfo().username}catch(e){return process.env.USERNAME||process.env.USER||process.env.LOGNAME}}();s.no_prepare&&(s.prepare=!1),o.sslmode&&(o.ssl=o.sslmode,delete o.sslmode),"timeout"in s&&(console.log("The timeout option is deprecated, use idle_timeout instead"),s.idle_timeout=s.timeout),"system"===o.sslrootcert&&(o.ssl="verify-full");let f=["idle_timeout","connect_timeout","max_lifetime","max_pipeline","backoff","keep_alive"],d={max:10,ssl:!1,idle_timeout:null,connect_timeout:30,max_lifetime:eN,max_pipeline:100,backoff:eT,keep_alive:60,prepare:!0,debug:!1,fetch_types:!0,publications:"alltables",target_session_attrs:null};return{host:Array.isArray(u)?u:u.split(",").map(e=>e.split(":")[0]),port:Array.isArray(c)?c:u.split(",").map(e=>parseInt(e.split(":")[1]||c)),path:s.path||u.indexOf("/")>-1&&u+"/.s.PGSQL."+c,database:s.database||s.db||(a.pathname||"").slice(1)||n.PGDATABASE||h,user:h,pass:s.pass||s.password||a.password||n.PGPASSWORD||"",...Object.entries(d).reduce((e,[t,i])=>{let r=t in s?s[t]:t in o?"disable"!==o[t]&&"false"!==o[t]&&o[t]:n["PG"+t.toUpperCase()]||i;return e[t]="string"==typeof r&&f.includes(t)?+r:r,e},{}),connection:{application_name:n.PGAPPNAME||"postgres.js",...s.connection,...Object.entries(o).reduce((e,[t,i])=>(t in d||(e[t]=i),e),{})},types:s.types||{},target_session_attrs:function(e,t,i){let r=e.target_session_attrs||t.searchParams.get("target_session_attrs")||i.PGTARGETSESSIONATTRS;if(!r||["read-write","read-only","primary","standby","prefer-standby"].includes(r))return r;throw Error("target_session_attrs "+r+" is not supported")}(s,a,n),onnotice:s.onnotice,onnotify:s.onnotify,onclose:s.onclose,onparameter:s.onparameter,socket:s.socket,transform:{undefined:(i=s.transform||{undefined:void 0}).undefined,column:{from:"function"==typeof i.column?i.column:i.column&&i.column.from,to:i.column&&i.column.to},value:{from:"function"==typeof i.value?i.value:i.value&&i.value.from,to:i.value&&i.value.to},row:{from:"function"==typeof i.row?i.row:i.row&&i.row.from,to:i.row&&i.row.to}},parameters:{},shared:{retries:0,typeArrayMap:{}},...q(s.types)}}(e,t),s=i.no_subscribe||function(e,t){let i=new Map,r="postgresjs_"+Math.random().toString(36).slice(2),n={},s,a,l=!1,o=h.sql=e({...t,transform:{column:{},value:{},row:{}},max:1,fetch_types:!1,idle_timeout:null,max_lifetime:null,connection:{...t.connection,replication:"database"},onclose:async function(){l||(a=null,n.pid=n.secret=void 0,f(await d(o,r,t.publications)),i.forEach(e=>e.forEach(({onsubscribe:e})=>e())))},no_subscribe:!0}),u=o.end,c=o.close;return o.end=async()=>(l=!0,a&&await new Promise(e=>(a.once("close",e),a.end())),u()),o.close=async()=>(a&&await new Promise(e=>(a.once("close",e),a.end())),c()),h;async function h(e,l,u=ey,c=ey){e=function(e){let t=e.match(/^(\*|insert|update|delete)?:?([^.]+?\.?[^=]+)?=?(.+)?/i)||[];if(!t)throw Error("Malformed subscribe pattern: "+e);let[,i,r,n]=t;return(i||"*")+(r?":"+(-1===r.indexOf(".")?"public."+r:r):"")+(n?"="+n:"")}(e),s||(s=d(o,r,t.publications));let p={fn:l,onsubscribe:u},m=i.has(e)?i.get(e).add(p):i.set(e,new Set([p])).get(e),g=()=>{m.delete(p),0===m.size&&i.delete(e)};return s.then(e=>(f(e),u(),a&&a.on("error",c),{unsubscribe:g,state:n,sql:o}))}function f(e){a=e.stream,n.pid=e.state.pid,n.secret=e.state.secret}async function d(e,i,r){if(!r)throw Error("Missing publication names");let n=await e.unsafe(`CREATE_REPLICATION_SLOT ${i} TEMPORARY LOGICAL pgoutput NOEXPORT_SNAPSHOT`),[s]=n,a=await e.unsafe(`START_REPLICATION SLOT ${i} LOGICAL ${s.consistent_point} (proto_version '1', publication_names '${r}')`).writable(),l={lsn:Buffer.concat(s.consistent_point.split("/").map(e=>Buffer.from(("00000000"+e).slice(-8),"hex")))};return a.on("data",function(i){119===i[0]?function(e,t,i,r,n){Object.entries({R:e=>{let r=1,s=t[e.readUInt32BE(r)]={schema:e.toString("utf8",r+=4,r=e.indexOf(0,r))||"pg_catalog",table:e.toString("utf8",r+1,r=e.indexOf(0,r+1)),columns:Array(e.readUInt16BE(r+=2)),keys:[]};r+=2;let a=0,l;for(;r<e.length;)(l=s.columns[a++]={key:e[r++],name:n.column.from?n.column.from(e.toString("utf8",r,r=e.indexOf(0,r))):e.toString("utf8",r,r=e.indexOf(0,r)),type:e.readUInt32BE(r+=1),parser:i[e.readUInt32BE(r)],atttypmod:e.readUInt32BE(r+=4)}).key&&s.keys.push(l),r+=4},Y:()=>{},O:()=>{},B:e=>{var i;i=e.readBigInt64BE(9),t.date=new Date(Date.UTC(2e3,0,1)+Number(i/BigInt(1e3))),t.lsn=e.subarray(1,9)},I:e=>{let i=1,s=t[e.readUInt32BE(i)],{row:a}=eb(e,s.columns,i+=7,n);r(a,{command:"insert",relation:s})},D:e=>{let i=1,s=t[e.readUInt32BE(i)],a=75===e[i+=4];r(a||79===e[i]?eb(e,s.columns,i+=3,n).row:null,{command:"delete",relation:s,key:a})},U:e=>{let i=1,s=t[e.readUInt32BE(i)],a=75===e[i+=4],l=a||79===e[i]?eb(e,s.columns,i+=3,n):null;l&&(i=l.i);let{row:o}=eb(e,s.columns,i+3,n);r(o,{command:"update",relation:s,key:a,old:l&&l.row})},T:()=>{},C:()=>{}}).reduce((e,[t,i])=>(e[t.charCodeAt(0)]=i,e),{})[e[0]](e)}(i.subarray(25),l,e.options.parsers,o,t.transform):107===i[0]&&i[17]&&(l.lsn=i.subarray(1,9),function(){let e=Buffer.alloc(34);e[0]=114,e.fill(l.lsn,1),e.writeBigInt64BE(BigInt(Date.now()-Date.UTC(2e3,0,1))*BigInt(1e3),25),a.write(e)}())}),a.on("error",function(e){console.error("Unexpected error during logical streaming - reconnecting",e)}),a.on("close",e.close),{stream:a,state:n.state};function o(e,t){let i=t.relation.schema+"."+t.relation.table;p("*",e,t),p("*:"+i,e,t),t.relation.keys.length&&p("*:"+i+"="+t.relation.keys.map(t=>e[t.name]),e,t),p(t.command,e,t),p(t.command+":"+i,e,t),t.relation.keys.length&&p(t.command+":"+i+"="+t.relation.keys.map(t=>e[t.name]),e,t)}}function p(e,t,r){i.has(e)&&i.get(e).forEach(({fn:i})=>i(t,r,e))}}(eS,{...i}),a=!1,l=Z(),f=Z(),g=Z(),y=Z(),b=Z(),w=Z(),v=Z(),S=Z(),T={connecting:f,reserved:g,closed:y,ended:b,open:w,busy:v,full:S},N=[...Array(i.max)].map(()=>er(i,T,{onopen:F,onend:Q,onclose:V})),P=$(function(e){return a?e.reject(h.connection("CONNECTION_ENDED",i,i)):w.length?E(w.shift(),e):y.length?z(y.shift(),e):void(v.length?E(v.shift(),e):l.push(e))});return Object.assign(P,{get parameters(){return i.parameters},largeObject:ew.bind(null,P),subscribe:s,CLOSE:o,END:o,PostgresError:c,options:i,reserve:A,listen:x,begin:I,close:D,end:L}),P;function $(e){return e.debug=i.debug,Object.entries(i.types).reduce((e,[t,i])=>(e[t]=e=>new p(e,i.to),e),t),Object.assign(r,{types:t,typed:t,unsafe:function(t,i=[],r={}){return 2!=arguments.length||Array.isArray(i)||(r=i,i=[]),new u([t],i,e,_,{prepare:!1,...r,simple:"simple"in r?r.simple:0===i.length})},notify:C,array:function e(t,r){return Array.isArray(t)?new p(t,r||(t.length?j(t)||25:0),i.shared.typeArrayMap):e(Array.from(arguments))},json:B,file:function(t,i=[],r={}){return 2!=arguments.length||Array.isArray(i)||(r=i,i=[]),new u([],i,i=>{n.readFile(t,"utf8",(t,r)=>{if(t)return i.reject(t);i.strings=[r],e(i)})},_,{...r,simple:"simple"in r?r.simple:0===i.length})}}),r;function t(e,t){return new p(e,t)}function r(t,...n){return t&&Array.isArray(t.raw)?new u(t,n,e,_):"string"!=typeof t||n.length?new m(t,n):new d(i.transform.column.to?i.transform.column.to(t):t)}}async function x(e,t,r){let n={fn:t,onlisten:r},s=x.sql||(x.sql=eS({...i,max:1,idle_timeout:null,max_lifetime:null,fetch_types:!1,onclose(){Object.entries(x.channels).forEach(([e,{listeners:t}])=>{delete x.channels[e],Promise.all(t.map(t=>x(e,t.fn,t.onlisten).catch(()=>{})))})},onnotify(e,t){e in x.channels&&x.channels[e].listeners.forEach(e=>e.fn(t))}})),a=x.channels||(x.channels={});if(e in a){a[e].listeners.push(n);let t=await a[e].result;return n.onlisten&&n.onlisten(),{state:t.state,unlisten:o}}a[e]={result:s`listen ${s.unsafe('"'+e.replace(/"/g,'""')+'"')}`,listeners:[n]};let l=await a[e].result;return n.onlisten&&n.onlisten(),{state:l.state,unlisten:o};async function o(){if(e in a!=!1&&(a[e].listeners=a[e].listeners.filter(e=>e!==n),!a[e].listeners.length))return delete a[e],s`unlisten ${s.unsafe('"'+e.replace(/"/g,'""')+'"')}`}}async function C(e,t){return await P`select pg_notify(${e}, ${""+t})`}async function A(){let e=Z(),t=w.length?w.shift():await new Promise((e,t)=>{let i={reserve:e,reject:t};l.push(i),y.length&&z(y.shift(),i)});O(t,g),t.reserved=()=>e.length?t.execute(e.shift()):O(t,g),t.reserved.release=!0;let i=$(function(i){t.queue===S?e.push(i):t.execute(i)||O(t,S)});return i.release=()=>{t.reserved=null,F(t)},i}async function I(e,t){t||(t=e,e="");let i=Z(),r=0,n,s=null;try{return await P.unsafe("begin "+e.replace(/[^a-z ]/ig,""),[],{onexecute:function(e){n=e,O(e,g),e.reserved=()=>i.length?e.execute(i.shift()):O(e,g)}}).execute(),await Promise.race([a(n,t),new Promise((e,t)=>n.onclose=t)])}catch(e){throw e}async function a(e,t,n){let l,o,u=$(function(t){t.catch(e=>l||(l=e)),e.queue===S?i.push(t):e.execute(t)||O(e,S)});u.savepoint=function t(i,n){return i&&Array.isArray(i.raw)?t(e=>e.apply(e,arguments)):(1==arguments.length&&(n=i,i=null),a(e,n,"s"+r+++(i?"_"+i:"")))},u.prepare=e=>s=e.replace(/[^a-z0-9$-_. ]/gi),n&&await u`savepoint ${u(n)}`;try{if(o=await new Promise((e,i)=>{let r=t(u);Promise.resolve(Array.isArray(r)?Promise.all(r):r).then(e,i)}),l)throw l}catch(e){throw await (n?u`rollback to ${u(n)}`:u`rollback`),e instanceof c&&"25P02"===e.code&&l||e}return n||(s?await u`prepare transaction '${u.unsafe(s)}'`:await u`commit`),o}}function O(e,t){return e.queue.remove(e),t.push(e),e.queue=t,t===w?e.idleTimer.start():e.idleTimer.cancel(),e}function B(e){return new p(e,3802)}function E(e,t){return e.execute(t)?O(e,v):O(e,S)}function _(e){return new Promise((t,r)=>{e.state?e.active?er(i).cancel(e.state,t,r):e.cancelled={resolve:t,reject:r}:(l.remove(e),e.cancelled=!0,e.reject(h.generic("57014","canceling statement due to user request")),t())})}async function L({timeout:e=null}={}){let t;return a||(await 1,a=Promise.race([new Promise(i=>null!==e&&(t=setTimeout(k,1e3*e,i))),Promise.all(N.map(e=>e.end()).concat(x.sql?x.sql.end({timeout:0}):[],s.sql?s.sql.end({timeout:0}):[]))]).then(()=>clearTimeout(t)))}async function D(){await Promise.all(N.map(e=>e.end()))}async function k(e){for(await Promise.all(N.map(e=>e.terminate()));l.length;)l.shift().reject(h.connection("CONNECTION_DESTROYED",i));e()}function z(e,t){return O(e,f),e.connect(t),e}function Q(e){O(e,b)}function F(e){if(0===l.length)return O(e,w);let t=Math.ceil(l.length/(f.length+1)),i=!0;for(;i&&l.length&&t-- >0;){let t=l.shift();if(t.reserve)return t.reserve(e);i=e.execute(t)}i?O(e,v):O(e,S)}function V(e,t){O(e,y),e.reserved=null,e.onclose&&(e.onclose(t),e.onclose=null),i.onclose&&i.onclose(e.id),l.length&&z(e,l.shift())}}function eT(e){return(.5+Math.random()/2)*Math.min(3**e/100,20)}function eN(){return 60*(30+30*Math.random())}},40329:(e,t,i)=>{i.d(t,{dL:()=>l,uR:()=>o});var r=i(21153),n=i(77952),s=i(68052);class a extends s.pe{static [r.i]="PgUUIDBuilder";constructor(e){super(e,"string","PgUUID")}defaultRandom(){return this.default((0,n.ll)`gen_random_uuid()`)}build(e){return new l(e,this.config)}}class l extends s.Kl{static [r.i]="PgUUID";getSQLType(){return"uuid"}}function o(e){return new a(e??"")}},43216:(e,t,i)=>{i.d(t,{V:()=>n});var r=i(21153);class n{constructor(e,t){this.table=e,this.config=t,this.name=t.name,this.keyAsName=t.keyAsName,this.notNull=t.notNull,this.default=t.default,this.defaultFn=t.defaultFn,this.onUpdateFn=t.onUpdateFn,this.hasDefault=t.hasDefault,this.primary=t.primaryKey,this.isUnique=t.isUnique,this.uniqueName=t.uniqueName,this.uniqueType=t.uniqueType,this.dataType=t.dataType,this.columnType=t.columnType,this.generated=t.generated,this.generatedIdentity=t.generatedIdentity}static [r.i]="Column";name;keyAsName;primary;notNull;default;defaultFn;onUpdateFn;hasDefault;isUnique;uniqueName;uniqueType;dataType;columnType;enumValues=void 0;generated=void 0;generatedIdentity=void 0;config;mapFromDriverValue(e){return e}mapToDriverValue(e){return e}shouldDisableInsert(){return void 0!==this.config.generated&&"byDefault"!==this.config.generated.type}}},44106:(e,t,i)=>{i.d(t,{p:()=>s});var r=i(21153),n=i(68052);class s extends n.pe{static [r.i]="PgIntColumnBaseBuilder";generatedAlwaysAsIdentity(e){if(e){let{name:t,...i}=e;this.config.generatedIdentity={type:"always",sequenceName:t,sequenceOptions:i}}else this.config.generatedIdentity={type:"always"};return this.config.hasDefault=!0,this.config.notNull=!0,this}generatedByDefaultAsIdentity(e){if(e){let{name:t,...i}=e;this.config.generatedIdentity={type:"byDefault",sequenceName:t,sequenceOptions:i}}else this.config.generatedIdentity={type:"byDefault"};return this.config.hasDefault=!0,this.config.notNull=!0,this}}},49571:(e,t,i)=>{i.d(t,{E:()=>r});let r=Symbol.for("drizzle:Name")},50844:(e,t,i)=>{i.d(t,{Pq:()=>l,iX:()=>a});var r=i(21153),n=i(68052);class s extends n.pe{static [r.i]="PgJsonBuilder";constructor(e){super(e,"json","PgJson")}build(e){return new a(e,this.config)}}class a extends n.Kl{static [r.i]="PgJson";constructor(e,t){super(e,t)}getSQLType(){return"json"}mapToDriverValue(e){return JSON.stringify(e)}mapFromDriverValue(e){if("string"==typeof e)try{return JSON.parse(e)}catch{}return e}}function l(e){return new s(e??"")}},52539:(e,t,i)=>{i.d(t,{Xd:()=>o,kB:()=>u});var r=i(21153),n=i(88965),s=i(68052),a=i(16731);class l extends a.u{constructor(e,t,i){super(e,"string","PgTime"),this.withTimezone=t,this.precision=i,this.config.withTimezone=t,this.config.precision=i}static [r.i]="PgTimeBuilder";build(e){return new o(e,this.config)}}class o extends s.Kl{static [r.i]="PgTime";withTimezone;precision;constructor(e,t){super(e,t),this.withTimezone=t.withTimezone,this.precision=t.precision}getSQLType(){let e=void 0===this.precision?"":`(${this.precision})`;return`time${e}${this.withTimezone?" with time zone":""}`}}function u(e,t={}){let{name:i,config:r}=(0,n.Ll)(e,t);return new l(i,r.withTimezone??!1,r.precision)}},55278:(e,t,i)=>{i.d(t,{f:()=>ex});var r=i(40078),n=i(21153);class s{static [n.i]="ConsoleLogWriter";write(e){console.log(e)}}class a{static [n.i]="DefaultLogger";writer;constructor(e){this.writer=e?.writer??new s}logQuery(e,t){let i=t.map(e=>{try{return JSON.stringify(e)}catch{return String(e)}}),r=i.length?` -- params: [${i.join(", ")}]`:"";this.writer.write(`Query: ${e}${r}`)}}class l{static [n.i]="NoopLogger";logQuery(){}}var o=i(43216),u=i(77952),c=i(11064),h=i(20573);class f{constructor(e){this.table=e}static [n.i]="ColumnAliasProxyHandler";get(e,t){return"table"===t?this.table:e[t]}}class d{constructor(e,t){this.alias=e,this.replaceOriginalName=t}static [n.i]="TableAliasProxyHandler";get(e,t){if(t===c.XI.Symbol.IsAlias)return!0;if(t===c.XI.Symbol.Name||this.replaceOriginalName&&t===c.XI.Symbol.OriginalName)return this.alias;if(t===h.n)return{...e[h.n],name:this.alias,isAlias:!0};if(t===c.XI.Symbol.Columns){let t=e[c.XI.Symbol.Columns];if(!t)return t;let i={};return Object.keys(t).map(r=>{i[r]=new Proxy(t[r],new f(new Proxy(e,this)))}),i}let i=e[t];return(0,n.is)(i,o.V)?new Proxy(i,new f(new Proxy(e,this))):i}}class p{constructor(e){this.alias=e}static [n.i]=null;get(e,t){return"sourceTable"===t?m(e.sourceTable,this.alias):e[t]}}function m(e,t){return new Proxy(e,new d(t,!1))}function g(e,t){return new Proxy(e,new f(new Proxy(e.table,new d(t,!1))))}function y(e,t){return new u.Xs.Aliased(b(e.sql,t),e.fieldAlias)}function b(e,t){return u.ll.join(e.queryChunks.map(e=>(0,n.is)(e,o.V)?g(e,t):(0,n.is)(e,u.Xs)?b(e,t):(0,n.is)(e,u.Xs.Aliased)?y(e,t):e))}function w(e){return(e.replace(/['\u2019]/g,"").match(/[\da-z]+|[A-Z]+(?![a-z])|[A-Z][\da-z]+/g)??[]).map(e=>e.toLowerCase()).join("_")}function v(e){return(e.replace(/['\u2019]/g,"").match(/[\da-z]+|[A-Z]+(?![a-z])|[A-Z][\da-z]+/g)??[]).reduce((e,t,i)=>e+(0===i?t.toLowerCase():`${t[0].toUpperCase()}${t.slice(1)}`),"")}function S(e){return e}class T{static [n.i]="CasingCache";cache={};cachedTables={};convert;constructor(e){this.convert="snake_case"===e?w:"camelCase"===e?v:S}getColumnCasing(e){if(!e.keyAsName)return e.name;let t=e.table[c.XI.Symbol.Schema]??"public",i=e.table[c.XI.Symbol.OriginalName],r=`${t}.${i}.${e.name}`;return this.cache[r]||this.cacheTable(e.table),this.cache[r]}cacheTable(e){let t=e[c.XI.Symbol.Schema]??"public",i=e[c.XI.Symbol.OriginalName],r=`${t}.${i}`;if(!this.cachedTables[r]){for(let t of Object.values(e[c.XI.Symbol.Columns])){let e=`${r}.${t.name}`;this.cache[e]=this.convert(t.name)}this.cachedTables[r]=!0}}clearCache(){this.cache={},this.cachedTables={}}}class N extends Error{static [n.i]="DrizzleError";constructor({message:e,cause:t}){super(e),this.name="DrizzleError",this.cause=t}}class P extends N{static [n.i]="TransactionRollbackError";constructor(){super({message:"Rollback"})}}var $=i(68052),x=i(12),C=i(50844),q=i(19439),A=i(52539),I=i(94396),O=i(4666),j=i(40329),B=i(10992),E=i(22509),_=i(88589),L=i(81740),D=i(88965);class k extends u.Ss{static [n.i]="PgViewBase"}class z{static [n.i]="PgDialect";casing;constructor(e){this.casing=new T(e?.casing)}async migrate(e,t,i){let r="string"==typeof i?"__drizzle_migrations":i.migrationsTable??"__drizzle_migrations",n="string"==typeof i?"drizzle":i.migrationsSchema??"drizzle",s=(0,u.ll)`
			CREATE TABLE IF NOT EXISTS ${u.ll.identifier(n)}.${u.ll.identifier(r)} (
				id SERIAL PRIMARY KEY,
				hash text NOT NULL,
				created_at bigint
			)
		`;await t.execute((0,u.ll)`CREATE SCHEMA IF NOT EXISTS ${u.ll.identifier(n)}`),await t.execute(s);let a=(await t.all((0,u.ll)`select id, hash, created_at from ${u.ll.identifier(n)}.${u.ll.identifier(r)} order by created_at desc limit 1`))[0];await t.transaction(async t=>{for await(let i of e)if(!a||Number(a.created_at)<i.folderMillis){for(let e of i.sql)await t.execute(u.ll.raw(e));await t.execute((0,u.ll)`insert into ${u.ll.identifier(n)}.${u.ll.identifier(r)} ("hash", "created_at") values(${i.hash}, ${i.folderMillis})`)}})}escapeName(e){return`"${e}"`}escapeParam(e){return`$${e+1}`}escapeString(e){return`'${e.replace(/'/g,"''")}'`}buildWithCTE(e){if(!e?.length)return;let t=[(0,u.ll)`with `];for(let[i,r]of e.entries())t.push((0,u.ll)`${u.ll.identifier(r._.alias)} as (${r._.sql})`),i<e.length-1&&t.push((0,u.ll)`, `);return t.push((0,u.ll)` `),u.ll.join(t)}buildDeleteQuery({table:e,where:t,returning:i,withList:r}){let n=this.buildWithCTE(r),s=i?(0,u.ll)` returning ${this.buildSelection(i,{isSingleTable:!0})}`:void 0,a=t?(0,u.ll)` where ${t}`:void 0;return(0,u.ll)`${n}delete from ${e}${a}${s}`}buildUpdateSet(e,t){let i=e[c.XI.Symbol.Columns],r=Object.keys(i).filter(e=>void 0!==t[e]||i[e]?.onUpdateFn!==void 0),n=r.length;return u.ll.join(r.flatMap((e,r)=>{let s=i[e],a=t[e]??u.ll.param(s.onUpdateFn(),s),l=(0,u.ll)`${u.ll.identifier(this.casing.getColumnCasing(s))} = ${a}`;return r<n-1?[l,u.ll.raw(", ")]:[l]}))}buildUpdateQuery({table:e,set:t,where:i,returning:r,withList:n,from:s,joins:a}){let l=this.buildWithCTE(n),o=e[B.mu.Symbol.Name],c=e[B.mu.Symbol.Schema],h=e[B.mu.Symbol.OriginalName],f=o===h?void 0:o,d=(0,u.ll)`${c?(0,u.ll)`${u.ll.identifier(c)}.`:void 0}${u.ll.identifier(h)}${f&&(0,u.ll)` ${u.ll.identifier(f)}`}`,p=this.buildUpdateSet(e,t),m=s&&u.ll.join([u.ll.raw(" from "),this.buildFromTable(s)]),g=this.buildJoins(a),y=r?(0,u.ll)` returning ${this.buildSelection(r,{isSingleTable:!s})}`:void 0,b=i?(0,u.ll)` where ${i}`:void 0;return(0,u.ll)`${l}update ${d} set ${p}${m}${g}${b}${y}`}buildSelection(e,{isSingleTable:t=!1}={}){let i=e.length,r=e.flatMap(({field:e},r)=>{let s=[];if((0,n.is)(e,u.Xs.Aliased)&&e.isSelectionField)s.push(u.ll.identifier(e.fieldAlias));else if((0,n.is)(e,u.Xs.Aliased)||(0,n.is)(e,u.Xs)){let i=(0,n.is)(e,u.Xs.Aliased)?e.sql:e;t?s.push(new u.Xs(i.queryChunks.map(e=>(0,n.is)(e,$.Kl)?u.ll.identifier(this.casing.getColumnCasing(e)):e))):s.push(i),(0,n.is)(e,u.Xs.Aliased)&&s.push((0,u.ll)` as ${u.ll.identifier(e.fieldAlias)}`)}else(0,n.is)(e,o.V)&&(t?s.push(u.ll.identifier(this.casing.getColumnCasing(e))):s.push(e));return r<i-1&&s.push((0,u.ll)`, `),s});return u.ll.join(r)}buildJoins(e){if(!e||0===e.length)return;let t=[];for(let[i,r]of e.entries()){0===i&&t.push((0,u.ll)` `);let s=r.table,a=r.lateral?(0,u.ll)` lateral`:void 0,l=r.on?(0,u.ll)` on ${r.on}`:void 0;if((0,n.is)(s,B.mu)){let e=s[B.mu.Symbol.Name],i=s[B.mu.Symbol.Schema],n=s[B.mu.Symbol.OriginalName],o=e===n?void 0:r.alias;t.push((0,u.ll)`${u.ll.raw(r.joinType)} join${a} ${i?(0,u.ll)`${u.ll.identifier(i)}.`:void 0}${u.ll.identifier(n)}${o&&(0,u.ll)` ${u.ll.identifier(o)}`}${l}`)}else if((0,n.is)(s,u.Ss)){let e=s[h.n].name,i=s[h.n].schema,n=s[h.n].originalName,o=e===n?void 0:r.alias;t.push((0,u.ll)`${u.ll.raw(r.joinType)} join${a} ${i?(0,u.ll)`${u.ll.identifier(i)}.`:void 0}${u.ll.identifier(n)}${o&&(0,u.ll)` ${u.ll.identifier(o)}`}${l}`)}else t.push((0,u.ll)`${u.ll.raw(r.joinType)} join${a} ${s}${l}`);i<e.length-1&&t.push((0,u.ll)` `)}return u.ll.join(t)}buildFromTable(e){if((0,n.is)(e,c.XI)&&e[c.XI.Symbol.IsAlias]){let t=(0,u.ll)`${u.ll.identifier(e[c.XI.Symbol.OriginalName])}`;return e[c.XI.Symbol.Schema]&&(t=(0,u.ll)`${u.ll.identifier(e[c.XI.Symbol.Schema])}.${t}`),(0,u.ll)`${t} ${u.ll.identifier(e[c.XI.Symbol.Name])}`}return e}buildSelectQuery({withList:e,fields:t,fieldsFlat:i,where:r,having:s,table:a,joins:l,orderBy:f,groupBy:d,limit:p,offset:m,lockingClause:g,distinct:y,setOperators:b}){let w,v,S,T=i??(0,D.He)(t);for(let e of T){let t;if((0,n.is)(e.field,o.V)&&(0,c.Io)(e.field.table)!==((0,n.is)(a,L.n)?a._.alias:(0,n.is)(a,k)?a[h.n].name:(0,n.is)(a,u.Xs)?void 0:(0,c.Io)(a))&&(t=e.field.table,!l?.some(({alias:e})=>e===(t[c.XI.Symbol.IsAlias]?c.Io(t):t[c.XI.Symbol.BaseName])))){let t=(0,c.Io)(e.field.table);throw Error(`Your "${e.path.join("->")}" field references a column "${t}"."${e.field.name}", but the table "${t}" is not part of the query! Did you forget to join it?`)}}let N=!l||0===l.length,P=this.buildWithCTE(e);y&&(w=!0===y?(0,u.ll)` distinct`:(0,u.ll)` distinct on (${u.ll.join(y.on,(0,u.ll)`, `)})`);let $=this.buildSelection(T,{isSingleTable:N}),x=this.buildFromTable(a),C=this.buildJoins(l),q=r?(0,u.ll)` where ${r}`:void 0,A=s?(0,u.ll)` having ${s}`:void 0;f&&f.length>0&&(v=(0,u.ll)` order by ${u.ll.join(f,(0,u.ll)`, `)}`),d&&d.length>0&&(S=(0,u.ll)` group by ${u.ll.join(d,(0,u.ll)`, `)}`);let I="object"==typeof p||"number"==typeof p&&p>=0?(0,u.ll)` limit ${p}`:void 0,O=m?(0,u.ll)` offset ${m}`:void 0,j=u.ll.empty();if(g){let e=(0,u.ll)` for ${u.ll.raw(g.strength)}`;g.config.of&&e.append((0,u.ll)` of ${u.ll.join(Array.isArray(g.config.of)?g.config.of:[g.config.of],(0,u.ll)`, `)}`),g.config.noWait?e.append((0,u.ll)` nowait`):g.config.skipLocked&&e.append((0,u.ll)` skip locked`),j.append(e)}let B=(0,u.ll)`${P}select${w} ${$} from ${x}${C}${q}${S}${A}${v}${I}${O}${j}`;return b.length>0?this.buildSetOperations(B,b):B}buildSetOperations(e,t){let[i,...r]=t;if(!i)throw Error("Cannot pass undefined values to any set operator");return 0===r.length?this.buildSetOperationQuery({leftSelect:e,setOperator:i}):this.buildSetOperations(this.buildSetOperationQuery({leftSelect:e,setOperator:i}),r)}buildSetOperationQuery({leftSelect:e,setOperator:{type:t,isAll:i,rightSelect:r,limit:s,orderBy:a,offset:l}}){let o,c=(0,u.ll)`(${e.getSQL()}) `,h=(0,u.ll)`(${r.getSQL()})`;if(a&&a.length>0){let e=[];for(let t of a)if((0,n.is)(t,$.Kl))e.push(u.ll.identifier(t.name));else if((0,n.is)(t,u.Xs)){for(let e=0;e<t.queryChunks.length;e++){let i=t.queryChunks[e];(0,n.is)(i,$.Kl)&&(t.queryChunks[e]=u.ll.identifier(i.name))}e.push((0,u.ll)`${t}`)}else e.push((0,u.ll)`${t}`);o=(0,u.ll)` order by ${u.ll.join(e,(0,u.ll)`, `)} `}let f="object"==typeof s||"number"==typeof s&&s>=0?(0,u.ll)` limit ${s}`:void 0,d=u.ll.raw(`${t} ${i?"all ":""}`),p=l?(0,u.ll)` offset ${l}`:void 0;return(0,u.ll)`${c}${d}${h}${o}${f}${p}`}buildInsertQuery({table:e,values:t,onConflict:i,returning:r,withList:s,select:a,overridingSystemValue_:l}){let o=[],h=Object.entries(e[c.XI.Symbol.Columns]).filter(([e,t])=>!t.shouldDisableInsert()),f=h.map(([,e])=>u.ll.identifier(this.casing.getColumnCasing(e)));if(a)(0,n.is)(t,u.Xs)?o.push(t):o.push(t.getSQL());else for(let[e,i]of(o.push(u.ll.raw("values ")),t.entries())){let r=[];for(let[e,t]of h){let s=i[e];if(void 0===s||(0,n.is)(s,u.Iw)&&void 0===s.value)if(void 0!==t.defaultFn){let e=t.defaultFn(),i=(0,n.is)(e,u.Xs)?e:u.ll.param(e,t);r.push(i)}else if(t.default||void 0===t.onUpdateFn)r.push((0,u.ll)`default`);else{let e=t.onUpdateFn(),i=(0,n.is)(e,u.Xs)?e:u.ll.param(e,t);r.push(i)}else r.push(s)}o.push(r),e<t.length-1&&o.push((0,u.ll)`, `)}let d=this.buildWithCTE(s),p=u.ll.join(o),m=r?(0,u.ll)` returning ${this.buildSelection(r,{isSingleTable:!0})}`:void 0,g=i?(0,u.ll)` on conflict ${i}`:void 0,y=!0===l?(0,u.ll)`overriding system value `:void 0;return(0,u.ll)`${d}insert into ${e} ${f} ${y}${p}${g}${m}`}buildRefreshMaterializedViewQuery({view:e,concurrently:t,withNoData:i}){let r=t?(0,u.ll)` concurrently`:void 0,n=i?(0,u.ll)` with no data`:void 0;return(0,u.ll)`refresh materialized view${r} ${e}${n}`}prepareTyping(e){if((0,n.is)(e,x.kn)||(0,n.is)(e,C.iX))return"json";if((0,n.is)(e,q.Z5))return"decimal";if((0,n.is)(e,A.Xd))return"time";if((0,n.is)(e,I.KM)||(0,n.is)(e,I.xQ))return"timestamp";if((0,n.is)(e,O.qw)||(0,n.is)(e,O.dw))return"date";else if((0,n.is)(e,j.dL))return"uuid";else return"none"}sqlToQuery(e,t){return e.toQuery({casing:this.casing,escapeName:this.escapeName,escapeParam:this.escapeParam,escapeString:this.escapeString,prepareTyping:this.prepareTyping,invokeSource:t})}buildRelationalQueryWithoutPK({fullSchema:e,schema:t,tableNamesMap:i,table:r,tableConfig:s,queryConfig:a,tableAlias:l,nestedQueryRelation:h,joinOn:f}){let d,p=[],w,v,S=[],T,P=[];if(!0===a)p=Object.entries(s.columns).map(([e,t])=>({dbKey:t.name,tsKey:e,field:g(t,l),relationTableTsKey:void 0,isJson:!1,selection:[]}));else{let r=Object.fromEntries(Object.entries(s.columns).map(([e,t])=>[e,g(t,l)]));if(a.where){let e="function"==typeof a.where?a.where(r,(0,E.mm)()):a.where;T=e&&b(e,l)}let h=[],f=[];if(a.columns){let e=!1;for(let[t,i]of Object.entries(a.columns))void 0!==i&&t in s.columns&&(e||!0!==i||(e=!0),f.push(t));f.length>0&&(f=e?f.filter(e=>a.columns?.[e]===!0):Object.keys(s.columns).filter(e=>!f.includes(e)))}else f=Object.keys(s.columns);for(let e of f){let t=s.columns[e];h.push({tsKey:e,value:t})}let d=[];if(a.with&&(d=Object.entries(a.with).filter(e=>!!e[1]).map(([e,t])=>({tsKey:e,queryConfig:t,relation:s.relations[e]}))),a.extras)for(let[e,t]of Object.entries("function"==typeof a.extras?a.extras(r,{sql:u.ll}):a.extras))h.push({tsKey:e,value:y(t,l)});for(let{tsKey:e,value:t}of h)p.push({dbKey:(0,n.is)(t,u.Xs.Aliased)?t.fieldAlias:s.columns[e].name,tsKey:e,field:(0,n.is)(t,o.V)?g(t,l):t,relationTableTsKey:void 0,isJson:!1,selection:[]});let m="function"==typeof a.orderBy?a.orderBy(r,(0,E.rl)()):a.orderBy??[];for(let{tsKey:r,queryConfig:s,relation:h}of(Array.isArray(m)||(m=[m]),S=m.map(e=>(0,n.is)(e,o.V)?g(e,l):b(e,l)),w=a.limit,v=a.offset,d)){let a=(0,E.W0)(t,i,h),o=i[(0,c.Lf)(h.referencedTable)],f=`${l}_${r}`,d=(0,_.Uo)(...a.fields.map((e,t)=>(0,_.eq)(g(a.references[t],f),g(e,l)))),m=this.buildRelationalQueryWithoutPK({fullSchema:e,schema:t,tableNamesMap:i,table:e[o],tableConfig:t[o],queryConfig:(0,n.is)(h,E.pD)?!0===s?{limit:1}:{...s,limit:1}:s,tableAlias:f,joinOn:d,nestedQueryRelation:h}),y=(0,u.ll)`${u.ll.identifier(f)}.${u.ll.identifier("data")}`.as(r);P.push({on:(0,u.ll)`true`,table:new L.n(m.sql,{},f),alias:f,joinType:"left",lateral:!0}),p.push({dbKey:r,tsKey:r,field:y,relationTableTsKey:o,isJson:!0,selection:m.selection})}}if(0===p.length)throw new N({message:`No fields selected for table "${s.tsName}" ("${l}")`});if(T=(0,_.Uo)(f,T),h){let e=(0,u.ll)`json_build_array(${u.ll.join(p.map(({field:e,tsKey:t,isJson:i})=>i?(0,u.ll)`${u.ll.identifier(`${l}_${t}`)}.${u.ll.identifier("data")}`:(0,n.is)(e,u.Xs.Aliased)?e.sql:e),(0,u.ll)`, `)})`;(0,n.is)(h,E.iv)&&(e=(0,u.ll)`coalesce(json_agg(${e}${S.length>0?(0,u.ll)` order by ${u.ll.join(S,(0,u.ll)`, `)}`:void 0}), '[]'::json)`);let t=[{dbKey:"data",tsKey:"data",field:e.as("data"),isJson:!0,relationTableTsKey:s.tsName,selection:p}];void 0!==w||void 0!==v||S.length>0?(d=this.buildSelectQuery({table:m(r,l),fields:{},fieldsFlat:[{path:[],field:u.ll.raw("*")}],where:T,limit:w,offset:v,orderBy:S,setOperators:[]}),T=void 0,w=void 0,v=void 0,S=[]):d=m(r,l),d=this.buildSelectQuery({table:(0,n.is)(d,B.mu)?d:new L.n(d,{},l),fields:{},fieldsFlat:t.map(({field:e})=>({path:[],field:(0,n.is)(e,o.V)?g(e,l):e})),joins:P,where:T,limit:w,offset:v,orderBy:S,setOperators:[]})}else d=this.buildSelectQuery({table:m(r,l),fields:{},fieldsFlat:p.map(({field:e})=>({path:[],field:(0,n.is)(e,o.V)?g(e,l):e})),joins:P,where:T,limit:w,offset:v,orderBy:S,setOperators:[]});return{tableTsKey:s.tsName,sql:d,selection:p}}}class Q{static [n.i]="SelectionProxyHandler";config;constructor(e){this.config={...e}}get(e,t){if("_"===t)return{...e._,selectedFields:new Proxy(e._.selectedFields,this)};if(t===h.n)return{...e[h.n],selectedFields:new Proxy(e[h.n].selectedFields,this)};if("symbol"==typeof t)return e[t];let i=((0,n.is)(e,L.n)?e._.selectedFields:(0,n.is)(e,u.Ss)?e[h.n].selectedFields:e)[t];if((0,n.is)(i,u.Xs.Aliased)){if("sql"===this.config.sqlAliasedBehavior&&!i.isSelectionField)return i.sql;let e=i.clone();return e.isSelectionField=!0,e}if((0,n.is)(i,u.Xs)){if("sql"===this.config.sqlBehavior)return i;throw Error(`You tried to reference "${t}" field from a subquery, which is a raw SQL field, but it doesn't have an alias declared. Please add an alias to the field using ".as('alias')" method.`)}return(0,n.is)(i,o.V)?this.config.alias?new Proxy(i,new f(new Proxy(i.table,new d(this.config.alias,this.config.replaceOriginalName??!1)))):i:"object"!=typeof i||null===i?i:new Proxy(i,new Q(this.config))}}class F{static [n.i]="TypedQueryBuilder";getSelectedFields(){return this._.selectedFields}}class V{static [n.i]="QueryPromise";[Symbol.toStringTag]="QueryPromise";catch(e){return this.then(void 0,e)}finally(e){return this.then(t=>(e?.(),t),t=>{throw e?.(),t})}then(e,t){return this.execute().then(e,t)}}var X=i(64088);function U(e){return(0,n.is)(e,B.mu)?[e[c.Sj]?`${e[c.Sj]}.${e[c.XI.Symbol.BaseName]}`:e[c.XI.Symbol.BaseName]]:(0,n.is)(e,L.n)?e._.usedTables??[]:(0,n.is)(e,u.Xs)?e.usedTables??[]:[]}class M{static [n.i]="PgSelectBuilder";fields;session;dialect;withList=[];distinct;constructor(e){this.fields=e.fields,this.session=e.session,this.dialect=e.dialect,e.withList&&(this.withList=e.withList),this.distinct=e.distinct}authToken;setToken(e){return this.authToken=e,this}from(e){let t,i=!!this.fields;return t=this.fields?this.fields:(0,n.is)(e,L.n)?Object.fromEntries(Object.keys(e._.selectedFields).map(t=>[t,e[t]])):(0,n.is)(e,k)?e[h.n].selectedFields:(0,n.is)(e,u.Xs)?{}:(0,D.YD)(e),new R({table:e,fields:t,isPartialSelect:i,session:this.session,dialect:this.dialect,withList:this.withList,distinct:this.distinct}).setToken(this.authToken)}}class K extends F{static [n.i]="PgSelectQueryBuilder";_;config;joinsNotNullableMap;tableName;isPartialSelect;session;dialect;cacheConfig=void 0;usedTables=new Set;constructor({table:e,fields:t,isPartialSelect:i,session:r,dialect:n,withList:s,distinct:a}){for(let l of(super(),this.config={withList:s,table:e,fields:{...t},distinct:a,setOperators:[]},this.isPartialSelect=i,this.session=r,this.dialect=n,this._={selectedFields:t,config:this.config},this.tableName=(0,D.zN)(e),this.joinsNotNullableMap="string"==typeof this.tableName?{[this.tableName]:!0}:{},U(e)))this.usedTables.add(l)}getUsedTables(){return[...this.usedTables]}createJoin(e,t){return(i,r)=>{let s=this.tableName,a=(0,D.zN)(i);for(let e of U(i))this.usedTables.add(e);if("string"==typeof a&&this.config.joins?.some(e=>e.alias===a))throw Error(`Alias "${a}" is already used in this query`);if(!this.isPartialSelect&&(1===Object.keys(this.joinsNotNullableMap).length&&"string"==typeof s&&(this.config.fields={[s]:this.config.fields}),"string"==typeof a&&!(0,n.is)(i,u.Xs))){let e=(0,n.is)(i,L.n)?i._.selectedFields:(0,n.is)(i,u.Ss)?i[h.n].selectedFields:i[c.XI.Symbol.Columns];this.config.fields[a]=e}if("function"==typeof r&&(r=r(new Proxy(this.config.fields,new Q({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))),this.config.joins||(this.config.joins=[]),this.config.joins.push({on:r,table:i,joinType:e,alias:a,lateral:t}),"string"==typeof a)switch(e){case"left":this.joinsNotNullableMap[a]=!1;break;case"right":this.joinsNotNullableMap=Object.fromEntries(Object.entries(this.joinsNotNullableMap).map(([e])=>[e,!1])),this.joinsNotNullableMap[a]=!0;break;case"cross":case"inner":this.joinsNotNullableMap[a]=!0;break;case"full":this.joinsNotNullableMap=Object.fromEntries(Object.entries(this.joinsNotNullableMap).map(([e])=>[e,!1])),this.joinsNotNullableMap[a]=!1}return this}}leftJoin=this.createJoin("left",!1);leftJoinLateral=this.createJoin("left",!0);rightJoin=this.createJoin("right",!1);innerJoin=this.createJoin("inner",!1);innerJoinLateral=this.createJoin("inner",!0);fullJoin=this.createJoin("full",!1);crossJoin=this.createJoin("cross",!1);crossJoinLateral=this.createJoin("cross",!0);createSetOperator(e,t){return i=>{let r="function"==typeof i?i(W()):i;if(!(0,D.DV)(this.getSelectedFields(),r.getSelectedFields()))throw Error("Set operator error (union / intersect / except): selected fields are not the same or are in a different order");return this.config.setOperators.push({type:e,isAll:t,rightSelect:r}),this}}union=this.createSetOperator("union",!1);unionAll=this.createSetOperator("union",!0);intersect=this.createSetOperator("intersect",!1);intersectAll=this.createSetOperator("intersect",!0);except=this.createSetOperator("except",!1);exceptAll=this.createSetOperator("except",!0);addSetOperators(e){return this.config.setOperators.push(...e),this}where(e){return"function"==typeof e&&(e=e(new Proxy(this.config.fields,new Q({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))),this.config.where=e,this}having(e){return"function"==typeof e&&(e=e(new Proxy(this.config.fields,new Q({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))),this.config.having=e,this}groupBy(...e){if("function"==typeof e[0]){let t=e[0](new Proxy(this.config.fields,new Q({sqlAliasedBehavior:"alias",sqlBehavior:"sql"})));this.config.groupBy=Array.isArray(t)?t:[t]}else this.config.groupBy=e;return this}orderBy(...e){if("function"==typeof e[0]){let t=e[0](new Proxy(this.config.fields,new Q({sqlAliasedBehavior:"alias",sqlBehavior:"sql"}))),i=Array.isArray(t)?t:[t];this.config.setOperators.length>0?this.config.setOperators.at(-1).orderBy=i:this.config.orderBy=i}else this.config.setOperators.length>0?this.config.setOperators.at(-1).orderBy=e:this.config.orderBy=e;return this}limit(e){return this.config.setOperators.length>0?this.config.setOperators.at(-1).limit=e:this.config.limit=e,this}offset(e){return this.config.setOperators.length>0?this.config.setOperators.at(-1).offset=e:this.config.offset=e,this}for(e,t={}){return this.config.lockingClause={strength:e,config:t},this}getSQL(){return this.dialect.buildSelectQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}as(e){let t=[];if(t.push(...U(this.config.table)),this.config.joins)for(let e of this.config.joins)t.push(...U(e.table));return new Proxy(new L.n(this.getSQL(),this.config.fields,e,!1,[...new Set(t)]),new Q({alias:e,sqlAliasedBehavior:"alias",sqlBehavior:"error"}))}getSelectedFields(){return new Proxy(this.config.fields,new Q({alias:this.tableName,sqlAliasedBehavior:"alias",sqlBehavior:"error"}))}$dynamic(){return this}$withCache(e){return this.cacheConfig=void 0===e?{config:{},enable:!0,autoInvalidate:!0}:!1===e?{enable:!1}:{enable:!0,autoInvalidate:!0,...e},this}}class R extends K{static [n.i]="PgSelect";_prepare(e){let{session:t,config:i,dialect:r,joinsNotNullableMap:n,authToken:s,cacheConfig:a,usedTables:l}=this;if(!t)throw Error("Cannot execute a query on a query builder. Please use a database instance instead.");let{fields:o}=i;return X.k.startActiveSpan("drizzle.prepareQuery",()=>{let i=(0,D.He)(o),u=t.prepareQuery(r.sqlToQuery(this.getSQL()),i,e,!0,void 0,{type:"select",tables:[...l]},a);return u.joinsNotNullableMap=n,u.setToken(s)})}prepare(e){return this._prepare(e)}authToken;setToken(e){return this.authToken=e,this}execute=e=>X.k.startActiveSpan("drizzle.operation",()=>this._prepare().execute(e,this.authToken))}function J(e,t){return(i,r,...n)=>{let s=[r,...n].map(i=>({type:e,isAll:t,rightSelect:i}));for(let e of s)if(!(0,D.DV)(i.getSelectedFields(),e.rightSelect.getSelectedFields()))throw Error("Set operator error (union / intersect / except): selected fields are not the same or are in a different order");return i.addSetOperators(s)}}(0,D.XJ)(R,[V]);let W=()=>({union:G,unionAll:H,intersect:Y,intersectAll:Z,except:ee,exceptAll:et}),G=J("union",!1),H=J("union",!0),Y=J("intersect",!1),Z=J("intersect",!0),ee=J("except",!1),et=J("except",!0);class ei{static [n.i]="PgQueryBuilder";dialect;dialectConfig;constructor(e){this.dialect=(0,n.is)(e,z)?e:void 0,this.dialectConfig=(0,n.is)(e,z)?void 0:e}$with=(e,t)=>{let i=this;return{as:r=>("function"==typeof r&&(r=r(i)),new Proxy(new L.J(r.getSQL(),t??("getSelectedFields"in r?r.getSelectedFields()??{}:{}),e,!0),new Q({alias:e,sqlAliasedBehavior:"alias",sqlBehavior:"error"})))}};with(...e){let t=this;return{select:function(i){return new M({fields:i??void 0,session:void 0,dialect:t.getDialect(),withList:e})},selectDistinct:function(e){return new M({fields:e??void 0,session:void 0,dialect:t.getDialect(),distinct:!0})},selectDistinctOn:function(e,i){return new M({fields:i??void 0,session:void 0,dialect:t.getDialect(),distinct:{on:e}})}}}select(e){return new M({fields:e??void 0,session:void 0,dialect:this.getDialect()})}selectDistinct(e){return new M({fields:e??void 0,session:void 0,dialect:this.getDialect(),distinct:!0})}selectDistinctOn(e,t){return new M({fields:t??void 0,session:void 0,dialect:this.getDialect(),distinct:{on:e}})}getDialect(){return this.dialect||(this.dialect=new z(this.dialectConfig)),this.dialect}}class er{constructor(e,t,i,r){this.table=e,this.session=t,this.dialect=i,this.withList=r}static [n.i]="PgUpdateBuilder";authToken;setToken(e){return this.authToken=e,this}set(e){return new en(this.table,(0,D.q)(this.table,e),this.session,this.dialect,this.withList).setToken(this.authToken)}}class en extends V{constructor(e,t,i,r,n){super(),this.session=i,this.dialect=r,this.config={set:t,table:e,withList:n,joins:[]},this.tableName=(0,D.zN)(e),this.joinsNotNullableMap="string"==typeof this.tableName?{[this.tableName]:!0}:{}}static [n.i]="PgUpdate";config;tableName;joinsNotNullableMap;cacheConfig;from(e){let t=(0,D.zN)(e);return"string"==typeof t&&(this.joinsNotNullableMap[t]=!0),this.config.from=e,this}getTableLikeFields(e){return(0,n.is)(e,B.mu)?e[c.XI.Symbol.Columns]:(0,n.is)(e,L.n)?e._.selectedFields:e[h.n].selectedFields}createJoin(e){return(t,i)=>{let r=(0,D.zN)(t);if("string"==typeof r&&this.config.joins.some(e=>e.alias===r))throw Error(`Alias "${r}" is already used in this query`);if("function"==typeof i){let e=this.config.from&&!(0,n.is)(this.config.from,u.Xs)?this.getTableLikeFields(this.config.from):void 0;i=i(new Proxy(this.config.table[c.XI.Symbol.Columns],new Q({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})),e&&new Proxy(e,new Q({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))}if(this.config.joins.push({on:i,table:t,joinType:e,alias:r}),"string"==typeof r)switch(e){case"left":this.joinsNotNullableMap[r]=!1;break;case"right":this.joinsNotNullableMap=Object.fromEntries(Object.entries(this.joinsNotNullableMap).map(([e])=>[e,!1])),this.joinsNotNullableMap[r]=!0;break;case"inner":this.joinsNotNullableMap[r]=!0;break;case"full":this.joinsNotNullableMap=Object.fromEntries(Object.entries(this.joinsNotNullableMap).map(([e])=>[e,!1])),this.joinsNotNullableMap[r]=!1}return this}}leftJoin=this.createJoin("left");rightJoin=this.createJoin("right");innerJoin=this.createJoin("inner");fullJoin=this.createJoin("full");where(e){return this.config.where=e,this}returning(e){if(!e&&(e=Object.assign({},this.config.table[c.XI.Symbol.Columns]),this.config.from)){let t=(0,D.zN)(this.config.from);if("string"==typeof t&&this.config.from&&!(0,n.is)(this.config.from,u.Xs)){let i=this.getTableLikeFields(this.config.from);e[t]=i}for(let t of this.config.joins){let i=(0,D.zN)(t.table);if("string"==typeof i&&!(0,n.is)(t.table,u.Xs)){let r=this.getTableLikeFields(t.table);e[i]=r}}}return this.config.returningFields=e,this.config.returning=(0,D.He)(e),this}getSQL(){return this.dialect.buildUpdateQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}_prepare(e){let t=this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()),this.config.returning,e,!0,void 0,{type:"insert",tables:U(this.config.table)},this.cacheConfig);return t.joinsNotNullableMap=this.joinsNotNullableMap,t}prepare(e){return this._prepare(e)}authToken;setToken(e){return this.authToken=e,this}execute=e=>this._prepare().execute(e,this.authToken);getSelectedFields(){return this.config.returningFields?new Proxy(this.config.returningFields,new Q({alias:(0,c.Io)(this.config.table),sqlAliasedBehavior:"alias",sqlBehavior:"error"})):void 0}$dynamic(){return this}}class es{constructor(e,t,i,r,n){this.table=e,this.session=t,this.dialect=i,this.withList=r,this.overridingSystemValue_=n}static [n.i]="PgInsertBuilder";authToken;setToken(e){return this.authToken=e,this}overridingSystemValue(){return this.overridingSystemValue_=!0,this}values(e){if(0===(e=Array.isArray(e)?e:[e]).length)throw Error("values() must be called with at least one value");let t=e.map(e=>{let t={},i=this.table[c.XI.Symbol.Columns];for(let r of Object.keys(e)){let s=e[r];t[r]=(0,n.is)(s,u.Xs)?s:new u.Iw(s,i[r])}return t});return new ea(this.table,t,this.session,this.dialect,this.withList,!1,this.overridingSystemValue_).setToken(this.authToken)}select(e){let t="function"==typeof e?e(new ei):e;if(!(0,n.is)(t,u.Xs)&&!(0,D.DV)(this.table[c.e],t._.selectedFields))throw Error("Insert select error: selected fields are not the same or are in a different order compared to the table definition");return new ea(this.table,t,this.session,this.dialect,this.withList,!0)}}class ea extends V{constructor(e,t,i,r,n,s,a){super(),this.session=i,this.dialect=r,this.config={table:e,values:t,withList:n,select:s,overridingSystemValue_:a}}static [n.i]="PgInsert";config;cacheConfig;returning(e=this.config.table[c.XI.Symbol.Columns]){return this.config.returningFields=e,this.config.returning=(0,D.He)(e),this}onConflictDoNothing(e={}){if(void 0===e.target)this.config.onConflict=(0,u.ll)`do nothing`;else{let t="";t=Array.isArray(e.target)?e.target.map(e=>this.dialect.escapeName(this.dialect.casing.getColumnCasing(e))).join(","):this.dialect.escapeName(this.dialect.casing.getColumnCasing(e.target));let i=e.where?(0,u.ll)` where ${e.where}`:void 0;this.config.onConflict=(0,u.ll)`(${u.ll.raw(t)})${i} do nothing`}return this}onConflictDoUpdate(e){if(e.where&&(e.targetWhere||e.setWhere))throw Error('You cannot use both "where" and "targetWhere"/"setWhere" at the same time - "where" is deprecated, use "targetWhere" or "setWhere" instead.');let t=e.where?(0,u.ll)` where ${e.where}`:void 0,i=e.targetWhere?(0,u.ll)` where ${e.targetWhere}`:void 0,r=e.setWhere?(0,u.ll)` where ${e.setWhere}`:void 0,n=this.dialect.buildUpdateSet(this.config.table,(0,D.q)(this.config.table,e.set)),s="";return s=Array.isArray(e.target)?e.target.map(e=>this.dialect.escapeName(this.dialect.casing.getColumnCasing(e))).join(","):this.dialect.escapeName(this.dialect.casing.getColumnCasing(e.target)),this.config.onConflict=(0,u.ll)`(${u.ll.raw(s)})${i} do update set ${n}${t}${r}`,this}getSQL(){return this.dialect.buildInsertQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}_prepare(e){return X.k.startActiveSpan("drizzle.prepareQuery",()=>this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()),this.config.returning,e,!0,void 0,{type:"insert",tables:U(this.config.table)},this.cacheConfig))}prepare(e){return this._prepare(e)}authToken;setToken(e){return this.authToken=e,this}execute=e=>X.k.startActiveSpan("drizzle.operation",()=>this._prepare().execute(e,this.authToken));getSelectedFields(){return this.config.returningFields?new Proxy(this.config.returningFields,new Q({alias:(0,c.Io)(this.config.table),sqlAliasedBehavior:"alias",sqlBehavior:"error"})):void 0}$dynamic(){return this}}class el extends V{constructor(e,t,i,r){super(),this.session=t,this.dialect=i,this.config={table:e,withList:r}}static [n.i]="PgDelete";config;cacheConfig;where(e){return this.config.where=e,this}returning(e=this.config.table[c.XI.Symbol.Columns]){return this.config.returningFields=e,this.config.returning=(0,D.He)(e),this}getSQL(){return this.dialect.buildDeleteQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}_prepare(e){return X.k.startActiveSpan("drizzle.prepareQuery",()=>this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()),this.config.returning,e,!0,void 0,{type:"delete",tables:U(this.config.table)},this.cacheConfig))}prepare(e){return this._prepare(e)}authToken;setToken(e){return this.authToken=e,this}execute=e=>X.k.startActiveSpan("drizzle.operation",()=>this._prepare().execute(e,this.authToken));getSelectedFields(){return this.config.returningFields?new Proxy(this.config.returningFields,new Q({alias:(0,c.Io)(this.config.table),sqlAliasedBehavior:"alias",sqlBehavior:"error"})):void 0}$dynamic(){return this}}class eo extends u.Xs{constructor(e){super(eo.buildEmbeddedCount(e.source,e.filters).queryChunks),this.params=e,this.mapWith(Number),this.session=e.session,this.sql=eo.buildCount(e.source,e.filters)}sql;token;static [n.i]="PgCountBuilder";[Symbol.toStringTag]="PgCountBuilder";session;static buildEmbeddedCount(e,t){return(0,u.ll)`(select count(*) from ${e}${u.ll.raw(" where ").if(t)}${t})`}static buildCount(e,t){return(0,u.ll)`select count(*) as count from ${e}${u.ll.raw(" where ").if(t)}${t};`}setToken(e){return this.token=e,this}then(e,t){return Promise.resolve(this.session.count(this.sql,this.token)).then(e,t)}catch(e){return this.then(void 0,e)}finally(e){return this.then(t=>(e?.(),t),t=>{throw e?.(),t})}}class eu{constructor(e,t,i,r,n,s,a){this.fullSchema=e,this.schema=t,this.tableNamesMap=i,this.table=r,this.tableConfig=n,this.dialect=s,this.session=a}static [n.i]="PgRelationalQueryBuilder";findMany(e){return new ec(this.fullSchema,this.schema,this.tableNamesMap,this.table,this.tableConfig,this.dialect,this.session,e||{},"many")}findFirst(e){return new ec(this.fullSchema,this.schema,this.tableNamesMap,this.table,this.tableConfig,this.dialect,this.session,e?{...e,limit:1}:{limit:1},"first")}}class ec extends V{constructor(e,t,i,r,n,s,a,l,o){super(),this.fullSchema=e,this.schema=t,this.tableNamesMap=i,this.table=r,this.tableConfig=n,this.dialect=s,this.session=a,this.config=l,this.mode=o}static [n.i]="PgRelationalQuery";_prepare(e){return X.k.startActiveSpan("drizzle.prepareQuery",()=>{let{query:t,builtQuery:i}=this._toSQL();return this.session.prepareQuery(i,void 0,e,!0,(e,i)=>{let r=e.map(e=>(0,E.I$)(this.schema,this.tableConfig,e,t.selection,i));return"first"===this.mode?r[0]:r})})}prepare(e){return this._prepare(e)}_getQuery(){return this.dialect.buildRelationalQueryWithoutPK({fullSchema:this.fullSchema,schema:this.schema,tableNamesMap:this.tableNamesMap,table:this.table,tableConfig:this.tableConfig,queryConfig:this.config,tableAlias:this.tableConfig.tsName})}getSQL(){return this._getQuery().sql}_toSQL(){let e=this._getQuery(),t=this.dialect.sqlToQuery(e.sql);return{query:e,builtQuery:t}}toSQL(){return this._toSQL().builtQuery}authToken;setToken(e){return this.authToken=e,this}execute(){return X.k.startActiveSpan("drizzle.operation",()=>this._prepare().execute(void 0,this.authToken))}}class eh extends V{constructor(e,t,i,r){super(),this.execute=e,this.sql=t,this.query=i,this.mapBatchResult=r}static [n.i]="PgRaw";getSQL(){return this.sql}getQuery(){return this.query}mapResult(e,t){return t?this.mapBatchResult(e):e}_prepare(){return this}isResponseInArrayMode(){return!1}}class ef extends V{constructor(e,t,i){super(),this.session=t,this.dialect=i,this.config={view:e}}static [n.i]="PgRefreshMaterializedView";config;concurrently(){if(void 0!==this.config.withNoData)throw Error("Cannot use concurrently and withNoData together");return this.config.concurrently=!0,this}withNoData(){if(void 0!==this.config.concurrently)throw Error("Cannot use concurrently and withNoData together");return this.config.withNoData=!0,this}getSQL(){return this.dialect.buildRefreshMaterializedViewQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}_prepare(e){return X.k.startActiveSpan("drizzle.prepareQuery",()=>this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()),void 0,e,!0))}prepare(e){return this._prepare(e)}authToken;setToken(e){return this.authToken=e,this}execute=e=>X.k.startActiveSpan("drizzle.operation",()=>this._prepare().execute(e,this.authToken))}class ed{constructor(e,t,i){if(this.dialect=e,this.session=t,this._=i?{schema:i.schema,fullSchema:i.fullSchema,tableNamesMap:i.tableNamesMap,session:t}:{schema:void 0,fullSchema:{},tableNamesMap:{},session:t},this.query={},this._.schema)for(let[r,n]of Object.entries(this._.schema))this.query[r]=new eu(i.fullSchema,this._.schema,this._.tableNamesMap,i.fullSchema[r],n,e,t);this.$cache={invalidate:async e=>{}}}static [n.i]="PgDatabase";query;$with=(e,t)=>{let i=this;return{as:r=>("function"==typeof r&&(r=r(new ei(i.dialect))),new Proxy(new L.J(r.getSQL(),t??("getSelectedFields"in r?r.getSelectedFields()??{}:{}),e,!0),new Q({alias:e,sqlAliasedBehavior:"alias",sqlBehavior:"error"})))}};$count(e,t){return new eo({source:e,filters:t,session:this.session})}$cache;with(...e){let t=this;return{select:function(i){return new M({fields:i??void 0,session:t.session,dialect:t.dialect,withList:e})},selectDistinct:function(i){return new M({fields:i??void 0,session:t.session,dialect:t.dialect,withList:e,distinct:!0})},selectDistinctOn:function(i,r){return new M({fields:r??void 0,session:t.session,dialect:t.dialect,withList:e,distinct:{on:i}})},update:function(i){return new er(i,t.session,t.dialect,e)},insert:function(i){return new es(i,t.session,t.dialect,e)},delete:function(i){return new el(i,t.session,t.dialect,e)}}}select(e){return new M({fields:e??void 0,session:this.session,dialect:this.dialect})}selectDistinct(e){return new M({fields:e??void 0,session:this.session,dialect:this.dialect,distinct:!0})}selectDistinctOn(e,t){return new M({fields:t??void 0,session:this.session,dialect:this.dialect,distinct:{on:e}})}update(e){return new er(e,this.session,this.dialect)}insert(e){return new es(e,this.session,this.dialect)}delete(e){return new el(e,this.session,this.dialect)}refreshMaterializedView(e){return new ef(e,this.session,this.dialect)}authToken;execute(e){let t="string"==typeof e?u.ll.raw(e):e.getSQL(),i=this.dialect.sqlToQuery(t),r=this.session.prepareQuery(i,void 0,void 0,!1);return new eh(()=>r.execute(void 0,this.authToken),t,i,e=>r.mapResult(e,!0))}transaction(e,t){return this.session.transaction(e,t)}}class ep{static [n.i]="Cache"}class em extends ep{strategy(){return"all"}static [n.i]="NoopCache";async get(e){}async put(e,t,i,r){}async onMutate(e){}}async function eg(e,t){let i=`${e}-${JSON.stringify(t)}`,r=new TextEncoder().encode(i);return[...new Uint8Array(await crypto.subtle.digest("SHA-256",r))].map(e=>e.toString(16).padStart(2,"0")).join("")}class ey extends Error{constructor(e,t,i){super(`Failed query: ${e}
params: ${t}`),this.query=e,this.params=t,this.cause=i,Error.captureStackTrace(this,ey),i&&(this.cause=i)}}class eb{constructor(e,t,i,r){this.query=e,this.cache=t,this.queryMetadata=i,this.cacheConfig=r,t&&"all"===t.strategy()&&void 0===r&&(this.cacheConfig={enable:!0,autoInvalidate:!0}),this.cacheConfig?.enable||(this.cacheConfig=void 0)}authToken;getQuery(){return this.query}mapResult(e,t){return e}setToken(e){return this.authToken=e,this}static [n.i]="PgPreparedQuery";joinsNotNullableMap;async queryWithCache(e,t,i){if(void 0===this.cache||(0,n.is)(this.cache,em)||void 0===this.queryMetadata)try{return await i()}catch(i){throw new ey(e,t,i)}if(this.cacheConfig&&!this.cacheConfig.enable)try{return await i()}catch(i){throw new ey(e,t,i)}if(("insert"===this.queryMetadata.type||"update"===this.queryMetadata.type||"delete"===this.queryMetadata.type)&&this.queryMetadata.tables.length>0)try{let[e]=await Promise.all([i(),this.cache.onMutate({tables:this.queryMetadata.tables})]);return e}catch(i){throw new ey(e,t,i)}if(!this.cacheConfig)try{return await i()}catch(i){throw new ey(e,t,i)}if("select"===this.queryMetadata.type){let r=await this.cache.get(this.cacheConfig.tag??await eg(e,t),this.queryMetadata.tables,void 0!==this.cacheConfig.tag,this.cacheConfig.autoInvalidate);if(void 0===r){let r;try{r=await i()}catch(i){throw new ey(e,t,i)}return await this.cache.put(this.cacheConfig.tag??await eg(e,t),r,this.cacheConfig.autoInvalidate?this.queryMetadata.tables:[],void 0!==this.cacheConfig.tag,this.cacheConfig.config),r}return r}try{return await i()}catch(i){throw new ey(e,t,i)}}}class ew{constructor(e){this.dialect=e}static [n.i]="PgSession";execute(e,t){return X.k.startActiveSpan("drizzle.operation",()=>X.k.startActiveSpan("drizzle.prepareQuery",()=>this.prepareQuery(this.dialect.sqlToQuery(e),void 0,void 0,!1)).setToken(t).execute(void 0,t))}all(e){return this.prepareQuery(this.dialect.sqlToQuery(e),void 0,void 0,!1).all()}async count(e,t){return Number((await this.execute(e,t))[0].count)}}class ev extends ed{constructor(e,t,i,r=0){super(e,t,i),this.schema=i,this.nestedIndex=r}static [n.i]="PgTransaction";rollback(){throw new P}getTransactionConfigSQL(e){let t=[];return e.isolationLevel&&t.push(`isolation level ${e.isolationLevel}`),e.accessMode&&t.push(e.accessMode),"boolean"==typeof e.deferrable&&t.push(e.deferrable?"deferrable":"not deferrable"),u.ll.raw(t.join(" "))}setTransaction(e){return this.session.execute((0,u.ll)`set transaction ${this.getTransactionConfigSQL(e)}`)}}class eS extends eb{constructor(e,t,i,r,n,s,a,l,o,u){super({sql:t,params:i},n,s,a),this.client=e,this.queryString=t,this.params=i,this.logger=r,this.fields=l,this._isResponseInArrayMode=o,this.customResultMapper=u}static [n.i]="PostgresJsPreparedQuery";async execute(e={}){return X.k.startActiveSpan("drizzle.execute",async t=>{let i=(0,u.Ct)(this.params,e);t?.setAttributes({"drizzle.query.text":this.queryString,"drizzle.query.params":JSON.stringify(i)}),this.logger.logQuery(this.queryString,i);let{fields:r,queryString:n,client:s,joinsNotNullableMap:a,customResultMapper:l}=this;if(!r&&!l)return X.k.startActiveSpan("drizzle.driver.execute",()=>this.queryWithCache(n,i,async()=>await s.unsafe(n,i)));let o=await X.k.startActiveSpan("drizzle.driver.execute",()=>(t?.setAttributes({"drizzle.query.text":n,"drizzle.query.params":JSON.stringify(i)}),this.queryWithCache(n,i,async()=>await s.unsafe(n,i).values())));return X.k.startActiveSpan("drizzle.mapResponse",()=>l?l(o):o.map(e=>(0,D.a6)(r,e,a)))})}all(e={}){return X.k.startActiveSpan("drizzle.execute",async t=>{let i=(0,u.Ct)(this.params,e);return t?.setAttributes({"drizzle.query.text":this.queryString,"drizzle.query.params":JSON.stringify(i)}),this.logger.logQuery(this.queryString,i),X.k.startActiveSpan("drizzle.driver.execute",()=>(t?.setAttributes({"drizzle.query.text":this.queryString,"drizzle.query.params":JSON.stringify(i)}),this.queryWithCache(this.queryString,i,async()=>this.client.unsafe(this.queryString,i))))})}isResponseInArrayMode(){return this._isResponseInArrayMode}}class eT extends ew{constructor(e,t,i,r={}){super(t),this.client=e,this.schema=i,this.options=r,this.logger=r.logger??new l,this.cache=r.cache??new em}static [n.i]="PostgresJsSession";logger;cache;prepareQuery(e,t,i,r,n,s,a){return new eS(this.client,e.sql,e.params,this.logger,this.cache,s,a,t,r,n)}query(e,t){return this.logger.logQuery(e,t),this.client.unsafe(e,t).values()}queryObjects(e,t){return this.client.unsafe(e,t)}transaction(e,t){return this.client.begin(async i=>{let r=new eT(i,this.dialect,this.schema,this.options),n=new eN(this.dialect,r,this.schema);return t&&await n.setTransaction(t),e(n)})}}class eN extends ev{constructor(e,t,i,r=0){super(e,t,i,r),this.session=t}static [n.i]="PostgresJsTransaction";transaction(e){return this.session.client.savepoint(t=>{let i=new eT(t,this.dialect,this.schema,this.session.options);return e(new eN(this.dialect,i,this.schema))})}}class eP extends ed{static [n.i]="PostgresJsDatabase"}function e$(e,t={}){let i,r,n=e=>e;for(let t of["1184","1082","1083","1114","1182","1185","1115","1231"])e.options.parsers[t]=n,e.options.serializers[t]=n;e.options.serializers["114"]=n,e.options.serializers["3802"]=n;let s=new z({casing:t.casing});if(!0===t.logger?i=new a:!1!==t.logger&&(i=t.logger),t.schema){let e=(0,E._k)(t.schema,E.DZ);r={fullSchema:t.schema,schema:e.tables,tableNamesMap:e.tableNamesMap}}let l=new eT(e,s,r,{logger:i,cache:t.cache}),o=new eP(s,l,r);return o.$client=e,o.$cache=t.cache,o.$cache&&(o.$cache.invalidate=t.cache?.onMutate),o}function ex(...e){if("string"==typeof e[0])return e$((0,r.A)(e[0]),e[1]);if((0,D.Lq)(e[0])){let{connection:t,client:i,...n}=e[0];if(i)return e$(i,n);if("object"==typeof t&&void 0!==t.url){let{url:e,...i}=t;return e$((0,r.A)(e,i),n)}return e$((0,r.A)(t),n)}return e$(e[0],e[1])}(ex||(ex={})).mock=function(e){return e$({options:{parsers:{},serializers:{}}},e)}},64088:(e,t,i)=>{let r,n;i.d(t,{k:()=>a});var s=i(22954);let a={startActiveSpan:(e,t)=>r?(n||(n=r.trace.getTracer("drizzle-orm","0.44.1")),(0,s.i)((i,r)=>r.startActiveSpan(e,e=>{try{return t(e)}catch(t){throw e.setStatus({code:i.SpanStatusCode.ERROR,message:t instanceof Error?t.message:"Unknown error"}),t}finally{e.end()}}),r,n)):t()}},68052:(e,t,i)=>{i.d(t,{ae:()=>b,Kl:()=>g,pe:()=>m});var r=i(21153);class n{static [r.i]="ColumnBuilder";config;constructor(e,t,i){this.config={name:e,keyAsName:""===e,notNull:!1,default:void 0,hasDefault:!1,primaryKey:!1,isUnique:!1,uniqueName:void 0,uniqueType:void 0,dataType:t,columnType:i,generated:void 0}}$type(){return this}notNull(){return this.config.notNull=!0,this}default(e){return this.config.default=e,this.config.hasDefault=!0,this}$defaultFn(e){return this.config.defaultFn=e,this.config.hasDefault=!0,this}$default=this.$defaultFn;$onUpdateFn(e){return this.config.onUpdateFn=e,this.config.hasDefault=!0,this}$onUpdate=this.$onUpdateFn;primaryKey(){return this.config.primaryKey=!0,this.config.notNull=!0,this}setName(e){""===this.config.name&&(this.config.name=e)}}var s=i(43216),a=i(49571);class l{static [r.i]="PgForeignKeyBuilder";reference;_onUpdate="no action";_onDelete="no action";constructor(e,t){this.reference=()=>{let{name:t,columns:i,foreignColumns:r}=e();return{name:t,columns:i,foreignTable:r[0].table,foreignColumns:r}},t&&(this._onUpdate=t.onUpdate,this._onDelete=t.onDelete)}onUpdate(e){return this._onUpdate=void 0===e?"no action":e,this}onDelete(e){return this._onDelete=void 0===e?"no action":e,this}build(e){return new o(e,this)}}class o{constructor(e,t){this.table=e,this.reference=t.reference,this.onUpdate=t._onUpdate,this.onDelete=t._onDelete}static [r.i]="PgForeignKey";reference;onUpdate;onDelete;getName(){let{name:e,columns:t,foreignColumns:i}=this.reference(),r=t.map(e=>e.name),n=i.map(e=>e.name),s=[this.table[a.E],...r,i[0].table[a.E],...n];return e??`${s.join("_")}_fk`}}var u=i(22954);function c(e,t){return`${e[a.E]}_${t.join("_")}_unique`}class h{constructor(e,t){this.name=t,this.columns=e}static [r.i]=null;columns;nullsNotDistinctConfig=!1;nullsNotDistinct(){return this.nullsNotDistinctConfig=!0,this}build(e){return new d(e,this.columns,this.nullsNotDistinctConfig,this.name)}}class f{static [r.i]=null;name;constructor(e){this.name=e}on(...e){return new h(e,this.name)}}class d{constructor(e,t,i,r){this.table=e,this.columns=t,this.name=r??c(this.table,this.columns.map(e=>e.name)),this.nullsNotDistinct=i}static [r.i]=null;columns;name;nullsNotDistinct=!1;getName(){return this.name}}function p(e,t,i){for(let r=t;r<e.length;r++){let n=e[r];if("\\"===n){r++;continue}if('"'===n)return[e.slice(t,r).replace(/\\/g,""),r+1];if(!i&&(","===n||"}"===n))return[e.slice(t,r).replace(/\\/g,""),r]}return[e.slice(t).replace(/\\/g,""),e.length]}class m extends n{foreignKeyConfigs=[];static [r.i]="PgColumnBuilder";array(e){return new w(this.config.name,this,e)}references(e,t={}){return this.foreignKeyConfigs.push({ref:e,actions:t}),this}unique(e,t){return this.config.isUnique=!0,this.config.uniqueName=e,this.config.uniqueType=t?.nulls,this}generatedAlwaysAs(e){return this.config.generated={as:e,type:"always",mode:"stored"},this}buildForeignKeys(e,t){return this.foreignKeyConfigs.map(({ref:i,actions:r})=>(0,u.i)((i,r)=>{let n=new l(()=>({columns:[e],foreignColumns:[i()]}));return r.onUpdate&&n.onUpdate(r.onUpdate),r.onDelete&&n.onDelete(r.onDelete),n.build(t)},i,r))}buildExtraConfigColumn(e){return new y(e,this.config)}}class g extends s.V{constructor(e,t){t.uniqueName||(t.uniqueName=c(e,[t.name])),super(e,t),this.table=e}static [r.i]="PgColumn"}class y extends g{static [r.i]="ExtraConfigColumn";getSQLType(){return this.getSQLType()}indexConfig={order:this.config.order??"asc",nulls:this.config.nulls??"last",opClass:this.config.opClass};defaultConfig={order:"asc",nulls:"last",opClass:void 0};asc(){return this.indexConfig.order="asc",this}desc(){return this.indexConfig.order="desc",this}nullsFirst(){return this.indexConfig.nulls="first",this}nullsLast(){return this.indexConfig.nulls="last",this}op(e){return this.indexConfig.opClass=e,this}}class b{static [r.i]="IndexedColumn";constructor(e,t,i,r){this.name=e,this.keyAsName=t,this.type=i,this.indexConfig=r}name;keyAsName;type;indexConfig}class w extends m{static [r.i]="PgArrayBuilder";constructor(e,t,i){super(e,"array","PgArray"),this.config.baseBuilder=t,this.config.size=i}build(e){let t=this.config.baseBuilder.build(e);return new v(e,this.config,t)}}class v extends g{constructor(e,t,i,r){super(e,t),this.baseColumn=i,this.range=r,this.size=t.size}size;static [r.i]="PgArray";getSQLType(){return`${this.baseColumn.getSQLType()}[${"number"==typeof this.size?this.size:""}]`}mapFromDriverValue(e){return"string"==typeof e&&(e=function(e){let[t]=function e(t,i=0){let r=[],n=i,s=!1;for(;n<t.length;){let a=t[n];if(","===a){(s||n===i)&&r.push(""),s=!0,n++;continue}if(s=!1,"\\"===a){n+=2;continue}if('"'===a){let[e,i]=p(t,n+1,!0);r.push(e),n=i;continue}if("}"===a)return[r,n+1];if("{"===a){let[i,s]=e(t,n+1);r.push(i),n=s;continue}let[l,o]=p(t,n,!1);r.push(l),n=o}return[r,n]}(e,1);return t}(e)),e.map(e=>this.baseColumn.mapFromDriverValue(e))}mapToDriverValue(e,t=!1){let i=e.map(e=>null===e?null:(0,r.is)(this.baseColumn,v)?this.baseColumn.mapToDriverValue(e,!0):this.baseColumn.mapToDriverValue(e));return t?i:function e(t){return`{${t.map(t=>Array.isArray(t)?e(t):"string"==typeof t?`"${t.replace(/\\/g,"\\\\").replace(/"/g,'\\"')}"`:`${t}`).join(",")}}`}(i)}}},77952:(e,t,i)=>{i.d(t,{Iw:()=>N,Or:()=>$,Xs:()=>b,DJ:()=>y,Ss:()=>q,Ct:()=>x,eG:()=>v,qt:()=>g,ll:()=>P});var r=i(21153),n=i(68052);class s extends n.pe{static [r.i]="PgEnumObjectColumnBuilder";constructor(e,t){super(e,"string","PgEnumObjectColumn"),this.config.enum=t}build(e){return new a(e,this.config)}}class a extends n.Kl{static [r.i]="PgEnumObjectColumn";enum;enumValues=this.config.enum.enumValues;constructor(e,t){super(e,t),this.enum=t.enum}getSQLType(){return this.enum.enumName}}let l=Symbol.for("drizzle:isPgEnum");class o extends n.pe{static [r.i]="PgEnumColumnBuilder";constructor(e,t){super(e,"string","PgEnumColumn"),this.config.enum=t}build(e){return new u(e,this.config)}}class u extends n.Kl{static [r.i]="PgEnumColumn";enum=this.config.enum;enumValues=this.config.enum.enumValues;constructor(e,t){super(e,t),this.enum=t.enum}getSQLType(){return this.enum.enumName}}var c=i(81740),h=i(64088),f=i(20573),d=i(43216),p=i(11064);class m{static [r.i]=null}function g(e){return null!=e&&"function"==typeof e.getSQL}class y{static [r.i]="StringChunk";value;constructor(e){this.value=Array.isArray(e)?e:[e]}getSQL(){return new b([this])}}class b{constructor(e){for(let t of(this.queryChunks=e,e))if((0,r.is)(t,p.XI)){let e=t[p.XI.Symbol.Schema];this.usedTables.push(void 0===e?t[p.XI.Symbol.Name]:e+"."+t[p.XI.Symbol.Name])}}static [r.i]="SQL";decoder=S;shouldInlineParams=!1;usedTables=[];append(e){return this.queryChunks.push(...e.queryChunks),this}toQuery(e){return h.k.startActiveSpan("drizzle.buildSQL",t=>{let i=this.buildQueryFromSourceParams(this.queryChunks,e);return t?.setAttributes({"drizzle.query.text":i.sql,"drizzle.query.params":JSON.stringify(i.params)}),i})}buildQueryFromSourceParams(e,t){let i=Object.assign({},t,{inlineParams:t.inlineParams||this.shouldInlineParams,paramStartIndex:t.paramStartIndex||{value:0}}),{casing:n,escapeName:s,escapeParam:a,prepareTyping:o,inlineParams:u,paramStartIndex:h}=i;var m=e.map(e=>{if((0,r.is)(e,y))return{sql:e.value.join(""),params:[]};if((0,r.is)(e,w))return{sql:s(e.value),params:[]};if(void 0===e)return{sql:"",params:[]};if(Array.isArray(e)){let t=[new y("(")];for(let[i,r]of e.entries())t.push(r),i<e.length-1&&t.push(new y(", "));return t.push(new y(")")),this.buildQueryFromSourceParams(t,i)}if((0,r.is)(e,b))return this.buildQueryFromSourceParams(e.queryChunks,{...i,inlineParams:u||e.shouldInlineParams});if((0,r.is)(e,p.XI)){let t=e[p.XI.Symbol.Schema],i=e[p.XI.Symbol.Name];return{sql:void 0===t||e[p.HE]?s(i):s(t)+"."+s(i),params:[]}}if((0,r.is)(e,d.V)){let i=n.getColumnCasing(e);if("indexes"===t.invokeSource)return{sql:s(i),params:[]};let r=e.table[p.XI.Symbol.Schema];return{sql:e.table[p.HE]||void 0===r?s(e.table[p.XI.Symbol.Name])+"."+s(i):s(r)+"."+s(e.table[p.XI.Symbol.Name])+"."+s(i),params:[]}}if((0,r.is)(e,q)){let t=e[f.n].schema,i=e[f.n].name;return{sql:void 0===t||e[f.n].isAlias?s(i):s(t)+"."+s(i),params:[]}}if((0,r.is)(e,N)){if((0,r.is)(e.value,$))return{sql:a(h.value++,e),params:[e],typings:["none"]};let t=null===e.value?null:e.encoder.mapToDriverValue(e.value);if((0,r.is)(t,b))return this.buildQueryFromSourceParams([t],i);if(u)return{sql:this.mapInlineParam(t,i),params:[]};let n=["none"];return o&&(n=[o(e.encoder)]),{sql:a(h.value++,t),params:[t],typings:n}}return(0,r.is)(e,$)?{sql:a(h.value++,e),params:[e],typings:["none"]}:(0,r.is)(e,b.Aliased)&&void 0!==e.fieldAlias?{sql:s(e.fieldAlias),params:[]}:(0,r.is)(e,c.n)?e._.isWith?{sql:s(e._.alias),params:[]}:this.buildQueryFromSourceParams([new y("("),e._.sql,new y(") "),new w(e._.alias)],i):e&&"function"==typeof e&&l in e&&!0===e[l]?e.schema?{sql:s(e.schema)+"."+s(e.enumName),params:[]}:{sql:s(e.enumName),params:[]}:g(e)?e.shouldOmitSQLParens?.()?this.buildQueryFromSourceParams([e.getSQL()],i):this.buildQueryFromSourceParams([new y("("),e.getSQL(),new y(")")],i):u?{sql:this.mapInlineParam(e,i),params:[]}:{sql:a(h.value++,e),params:[e],typings:["none"]}});let v={sql:"",params:[]};for(let e of m)v.sql+=e.sql,v.params.push(...e.params),e.typings?.length&&(v.typings||(v.typings=[]),v.typings.push(...e.typings));return v}mapInlineParam(e,{escapeString:t}){if(null===e)return"null";if("number"==typeof e||"boolean"==typeof e)return e.toString();if("string"==typeof e)return t(e);if("object"==typeof e){let i=e.toString();return"[object Object]"===i?t(JSON.stringify(e)):t(i)}throw Error("Unexpected param value: "+e)}getSQL(){return this}as(e){return void 0===e?this:new b.Aliased(this,e)}mapWith(e){return this.decoder="function"==typeof e?{mapFromDriverValue:e}:e,this}inlineParams(){return this.shouldInlineParams=!0,this}if(e){return e?this:void 0}}class w{constructor(e){this.value=e}static [r.i]="Name";brand;getSQL(){return new b([this])}}function v(e){return"object"==typeof e&&null!==e&&"mapToDriverValue"in e&&"function"==typeof e.mapToDriverValue}let S={mapFromDriverValue:e=>e},T={mapToDriverValue:e=>e};({...S,...T});class N{constructor(e,t=T){this.value=e,this.encoder=t}static [r.i]="Param";brand;getSQL(){return new b([this])}}function P(e,...t){let i=[];for(let[r,n]of((t.length>0||e.length>0&&""!==e[0])&&i.push(new y(e[0])),t.entries()))i.push(n,new y(e[r+1]));return new b(i)}(e=>{e.empty=function(){return new b([])},e.fromList=function(e){return new b(e)},e.raw=function(e){return new b([new y(e)])},e.join=function(e,t){let i=[];for(let[r,n]of e.entries())r>0&&void 0!==t&&i.push(t),i.push(n);return new b(i)},e.identifier=function(e){return new w(e)},e.placeholder=function(e){return new $(e)},e.param=function(e,t){return new N(e,t)}})(P||(P={})),(e=>{class t{constructor(e,t){this.sql=e,this.fieldAlias=t}static [r.i]="SQL.Aliased";isSelectionField=!1;getSQL(){return this.sql}clone(){return new t(this.sql,this.fieldAlias)}}e.Aliased=t})(b||(b={}));class ${constructor(e){this.name=e}static [r.i]="Placeholder";getSQL(){return new b([this])}}function x(e,t){return e.map(e=>{if((0,r.is)(e,$)){if(!(e.name in t))throw Error(`No value for placeholder "${e.name}" was provided`);return t[e.name]}if((0,r.is)(e,N)&&(0,r.is)(e.value,$)){if(!(e.value.name in t))throw Error(`No value for placeholder "${e.value.name}" was provided`);return e.encoder.mapToDriverValue(t[e.value.name])}return e})}let C=Symbol.for("drizzle:IsDrizzleView");class q{static [r.i]="View";[f.n];[C]=!0;constructor({name:e,schema:t,selectedFields:i,query:r}){this[f.n]={name:e,originalName:e,schema:t,selectedFields:i,query:r,isExisting:!r,isAlias:!1}}getSQL(){return new b([this])}}d.V.prototype.getSQL=function(){return new b([this])},p.XI.prototype.getSQL=function(){return new b([this])},c.n.prototype.getSQL=function(){return new b([this])}},81740:(e,t,i)=>{i.d(t,{J:()=>s,n:()=>n});var r=i(21153);class n{static [r.i]="Subquery";constructor(e,t,i,r=!1,n=[]){this._={brand:"Subquery",sql:e,selectedFields:t,alias:i,isWith:r,usedTables:n}}}class s extends n{static [r.i]="WithSubquery"}},88589:(e,t,i)=>{i.d(t,{AU:()=>f,B3:()=>C,KJ:()=>T,KL:()=>b,Pe:()=>v,RK:()=>x,RO:()=>p,RV:()=>y,Tq:()=>N,Uo:()=>c,eq:()=>o,gt:()=>d,kZ:()=>w,lt:()=>m,mj:()=>$,ne:()=>u,o8:()=>P,or:()=>h,q1:()=>q,t2:()=>S,wJ:()=>g});var r=i(43216),n=i(21153),s=i(11064),a=i(77952);function l(e,t){return!(0,a.eG)(t)||(0,a.qt)(e)||(0,n.is)(e,a.Iw)||(0,n.is)(e,a.Or)||(0,n.is)(e,r.V)||(0,n.is)(e,s.XI)||(0,n.is)(e,a.Ss)?e:new a.Iw(e,t)}let o=(e,t)=>(0,a.ll)`${e} = ${l(t,e)}`,u=(e,t)=>(0,a.ll)`${e} <> ${l(t,e)}`;function c(...e){let t=e.filter(e=>void 0!==e);if(0!==t.length)return new a.Xs(1===t.length?t:[new a.DJ("("),a.ll.join(t,new a.DJ(" and ")),new a.DJ(")")])}function h(...e){let t=e.filter(e=>void 0!==e);if(0!==t.length)return new a.Xs(1===t.length?t:[new a.DJ("("),a.ll.join(t,new a.DJ(" or ")),new a.DJ(")")])}function f(e){return(0,a.ll)`not ${e}`}let d=(e,t)=>(0,a.ll)`${e} > ${l(t,e)}`,p=(e,t)=>(0,a.ll)`${e} >= ${l(t,e)}`,m=(e,t)=>(0,a.ll)`${e} < ${l(t,e)}`,g=(e,t)=>(0,a.ll)`${e} <= ${l(t,e)}`;function y(e,t){return Array.isArray(t)?0===t.length?(0,a.ll)`false`:(0,a.ll)`${e} in ${t.map(t=>l(t,e))}`:(0,a.ll)`${e} in ${l(t,e)}`}function b(e,t){return Array.isArray(t)?0===t.length?(0,a.ll)`true`:(0,a.ll)`${e} not in ${t.map(t=>l(t,e))}`:(0,a.ll)`${e} not in ${l(t,e)}`}function w(e){return(0,a.ll)`${e} is null`}function v(e){return(0,a.ll)`${e} is not null`}function S(e){return(0,a.ll)`exists ${e}`}function T(e){return(0,a.ll)`not exists ${e}`}function N(e,t,i){return(0,a.ll)`${e} between ${l(t,e)} and ${l(i,e)}`}function P(e,t,i){return(0,a.ll)`${e} not between ${l(t,e)} and ${l(i,e)}`}function $(e,t){return(0,a.ll)`${e} like ${t}`}function x(e,t){return(0,a.ll)`${e} not like ${t}`}function C(e,t){return(0,a.ll)`${e} ilike ${t}`}function q(e,t){return(0,a.ll)`${e} not ilike ${t}`}},88965:(e,t,i)=>{i.d(t,{DV:()=>c,He:()=>function e(t,i){return Object.entries(t).reduce((t,[a,o])=>{if("string"!=typeof a)return t;let u=i?[...i,a]:[a];return(0,n.is)(o,r.V)||(0,n.is)(o,s.Xs)||(0,n.is)(o,s.Xs.Aliased)?t.push({path:u,field:o}):(0,n.is)(o,l.XI)?t.push(...e(o[l.XI.Symbol.Columns],u)):t.push(...e(o,u)),t},[])},Ll:()=>m,Lq:()=>g,XJ:()=>f,YD:()=>d,a6:()=>u,q:()=>h,zN:()=>p});var r=i(43216),n=i(21153),s=i(77952),a=i(81740),l=i(11064),o=i(20573);function u(e,t,i){let a={},o=e.reduce((e,{path:o,field:u},c)=>{let h;h=(0,n.is)(u,r.V)?u:(0,n.is)(u,s.Xs)?u.decoder:u.sql.decoder;let f=e;for(let[e,s]of o.entries())if(e<o.length-1)s in f||(f[s]={}),f=f[s];else{let e=t[c],d=f[s]=null===e?null:h.mapFromDriverValue(e);if(i&&(0,n.is)(u,r.V)&&2===o.length){let e=o[0];e in a?"string"==typeof a[e]&&a[e]!==(0,l.Io)(u.table)&&(a[e]=!1):a[e]=null===d&&(0,l.Io)(u.table)}}return e},{});if(i&&Object.keys(a).length>0)for(let[e,t]of Object.entries(a))"string"!=typeof t||i[t]||(o[e]=null);return o}function c(e,t){let i=Object.keys(e),r=Object.keys(t);if(i.length!==r.length)return!1;for(let[e,t]of i.entries())if(t!==r[e])return!1;return!0}function h(e,t){let i=Object.entries(t).filter(([,e])=>void 0!==e).map(([t,i])=>(0,n.is)(i,s.Xs)||(0,n.is)(i,r.V)?[t,i]:[t,new s.Iw(i,e[l.XI.Symbol.Columns][t])]);if(0===i.length)throw Error("No values to set");return Object.fromEntries(i)}function f(e,t){for(let i of t)for(let t of Object.getOwnPropertyNames(i.prototype))"constructor"!==t&&Object.defineProperty(e.prototype,t,Object.getOwnPropertyDescriptor(i.prototype,t)||Object.create(null))}function d(e){return e[l.XI.Symbol.Columns]}function p(e){return(0,n.is)(e,a.n)?e._.alias:(0,n.is)(e,s.Ss)?e[o.n].name:(0,n.is)(e,s.Xs)?void 0:e[l.XI.Symbol.IsAlias]?e[l.XI.Symbol.Name]:e[l.XI.Symbol.BaseName]}function m(e,t){return{name:"string"==typeof e&&e.length>0?e:"",config:"object"==typeof e?e:t}}function g(e){if("object"!=typeof e||null===e||"Object"!==e.constructor.name)return!1;if("logger"in e){let t=typeof e.logger;return"boolean"===t||"object"===t&&"function"==typeof e.logger.logQuery||"undefined"===t}if("schema"in e){let t=typeof e.schema;return"object"===t||"undefined"===t}if("casing"in e){let t=typeof e.casing;return"string"===t||"undefined"===t}if("mode"in e)return"default"===e.mode&&"planetscale"===e.mode&&void 0===e.mode;if("connection"in e){let t=typeof e.connection;return"string"===t||"object"===t||"undefined"===t}if("client"in e){let t=typeof e.client;return"object"===t||"function"===t||"undefined"===t}return 0===Object.keys(e).length}},94396:(e,t,i)=>{i.d(t,{KM:()=>o,vE:()=>h,xQ:()=>c});var r=i(21153),n=i(88965),s=i(68052),a=i(16731);class l extends a.u{static [r.i]="PgTimestampBuilder";constructor(e,t,i){super(e,"date","PgTimestamp"),this.config.withTimezone=t,this.config.precision=i}build(e){return new o(e,this.config)}}class o extends s.Kl{static [r.i]="PgTimestamp";withTimezone;precision;constructor(e,t){super(e,t),this.withTimezone=t.withTimezone,this.precision=t.precision}getSQLType(){let e=void 0===this.precision?"":` (${this.precision})`;return`timestamp${e}${this.withTimezone?" with time zone":""}`}mapFromDriverValue=e=>new Date(this.withTimezone?e:e+"+0000");mapToDriverValue=e=>e.toISOString()}class u extends a.u{static [r.i]="PgTimestampStringBuilder";constructor(e,t,i){super(e,"string","PgTimestampString"),this.config.withTimezone=t,this.config.precision=i}build(e){return new c(e,this.config)}}class c extends s.Kl{static [r.i]="PgTimestampString";withTimezone;precision;constructor(e,t){super(e,t),this.withTimezone=t.withTimezone,this.precision=t.precision}getSQLType(){let e=void 0===this.precision?"":`(${this.precision})`;return`timestamp${e}${this.withTimezone?" with time zone":""}`}}function h(e,t={}){let{name:i,config:r}=(0,n.Ll)(e,t);return r?.mode==="string"?new u(i,r.withTimezone??!1,r.precision):new l(i,r?.withTimezone??!1,r?.precision)}},98741:(e,t,i)=>{i.d(t,{Qq:()=>o});var r=i(21153),n=i(88965),s=i(68052);class a extends s.pe{static [r.i]="PgTextBuilder";constructor(e,t){super(e,"string","PgText"),this.config.enumValues=t.enum}build(e){return new l(e,this.config)}}class l extends s.Kl{static [r.i]="PgText";enumValues=this.config.enumValues;getSQLType(){return"text"}}function o(e,t={}){let{name:i,config:r}=(0,n.Ll)(e,t);return new a(i,r)}},98894:(e,t,i)=>{i.d(t,{x:()=>l});var r=i(21153),n=i(68052);class s extends n.pe{static [r.i]="PgRealBuilder";constructor(e,t){super(e,"number","PgReal"),this.config.length=t}build(e){return new a(e,this.config)}}class a extends n.Kl{static [r.i]="PgReal";constructor(e,t){super(e,t)}getSQLType(){return"real"}mapFromDriverValue=e=>"string"==typeof e?Number.parseFloat(e):e}function l(e){return new s(e??"")}}};