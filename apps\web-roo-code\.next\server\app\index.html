<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/c16eb50558afbc00.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-c8e217c742f0746a.js"/><script src="/_next/static/chunks/206c0df1-50f53d661b5e5bf5.js" async=""></script><script src="/_next/static/chunks/550-cc2f0cda2d11cae3.js" async=""></script><script src="/_next/static/chunks/main-app-f1892ae8c4b297c8.js" async=""></script><script src="/_next/static/chunks/aba955ec-9833060e0c799abe.js" async=""></script><script src="/_next/static/chunks/e5c963a3-f3448fec3e69041a.js" async=""></script><script src="/_next/static/chunks/88e8e2ac-7a1619a6e98006b4.js" async=""></script><script src="/_next/static/chunks/2ae967de-afd0847aa04941df.js" async=""></script><script src="/_next/static/chunks/dd0ec533-85265a862baf3739.js" async=""></script><script src="/_next/static/chunks/5d6f4545-30a8b6f9fd887cd0.js" async=""></script><script src="/_next/static/chunks/604-37da8378c60f8591.js" async=""></script><script src="/_next/static/chunks/156-01d88aff08204544.js" async=""></script><script src="/_next/static/chunks/657-9a922aaadb9c4bb2.js" async=""></script><script src="/_next/static/chunks/954-09ef0ac74eac9fe3.js" async=""></script><script src="/_next/static/chunks/554-3f07505b777a310d.js" async=""></script><script src="/_next/static/chunks/app/layout-70c3bb938d5edbac.js" async=""></script><script src="/_next/static/chunks/6b00a407-f2a69e7fba2ff1d5.js" async=""></script><script src="/_next/static/chunks/944-7e8cad43dbfe6c27.js" async=""></script><script src="/_next/static/chunks/957-6b88dac7433a3763.js" async=""></script><script src="/_next/static/chunks/app/page-fbf1f0c2d5ddc3ca.js" async=""></script><link rel="preload" href="https://www.googletagmanager.com/gtag/js?id=AW-17391954825" as="script"/><title>Roo Code – Your AI-Powered Dev Team in VS Code</title><meta name="description" content="Roo Code puts an entire AI dev team right in your editor, outpacing closed tools with deep project-wide context, multi-step agentic coding, and unmatched developer-centric flexibility."/><link rel="canonical" href="https://roocode.com"/><link rel="icon" href="/favicon.ico"/><link rel="icon" href="/favicon-16x16.png" sizes="16x16" type="image/png"/><link rel="icon" href="/favicon-32x32.png" sizes="32x32" type="image/png"/><link rel="apple-touch-icon" href="/apple-touch-icon.png"/><link rel="android-chrome-192x192" href="/android-chrome-192x192.png" sizes="192x192" type="image/png"/><link rel="android-chrome-512x512" href="/android-chrome-512x512.png" sizes="512x512" type="image/png"/><link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/gh/devicons/devicon@latest/devicon.min.css"/><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__className_e8ce0c"><div itemScope="" itemType="https://schema.org/WebSite"><link itemProp="url" href="https://roocode.com"/><meta itemProp="name" content="Roo Code"/></div><!--$!--><template data-dgst="BAILOUT_TO_CLIENT_SIDE_RENDERING"></template><!--/$--><script>((e,t,r,n,i,o,a,s)=>{let l=document.documentElement,u=["light","dark"];function c(t){var r;(Array.isArray(e)?e:[e]).forEach(e=>{let r="class"===e,n=r&&o?i.map(e=>o[e]||e):i;r?(l.classList.remove(...n),l.classList.add(o&&o[t]?o[t]:t)):l.setAttribute(e,t)}),r=t,s&&u.includes(r)&&(l.style.colorScheme=r)}if(n)c(n);else try{let e=localStorage.getItem(t)||r,n=a&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;c(n)}catch(e){}})("class","theme","dark",null,["light","dark"],null,false,true)</script><div class="flex min-h-screen flex-col bg-background text-foreground"><header class="sticky top-0 z-50 border-b border-border bg-background/80 backdrop-blur-md"><div class="container mx-auto flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8"><div class="flex items-center"><a class="flex items-center" href="/"><img alt="Roo Code Logo" loading="lazy" width="120" height="40" decoding="async" data-nimg="1" class="h-8 w-auto" style="color:transparent" src="/Roo-Code-Logo-Horiz-white.svg"/></a></div><nav class="hidden text-sm font-medium md:flex md:items-center md:space-x-3 xl:space-x-8"><button class="text-muted-foreground transition-transform duration-200 hover:scale-105 hover:text-foreground max-lg:hidden">Features</button><button class="text-muted-foreground transition-transform duration-200 hover:scale-105 hover:text-foreground max-lg:hidden">Testimonials</button><button class="text-muted-foreground transition-transform duration-200 hover:scale-105 hover:text-foreground">FAQ</button><a class="text-muted-foreground transition-transform duration-200 hover:scale-105 hover:text-foreground" href="/evals">Evals</a><a class="text-muted-foreground transition-transform duration-200 hover:scale-105 hover:text-foreground" href="/enterprise">Enterprise</a><a href="https://docs.roocode.com" target="_blank" class="text-muted-foreground transition-transform duration-200 hover:scale-105 hover:text-foreground">Docs</a><a href="https://careers.roocode.com" target="_blank" class="text-muted-foreground transition-transform duration-200 hover:scale-105 hover:text-foreground">Careers</a><div class="flex items-center rounded-full bg-gradient-to-r from-blue-400 to-cyan-400 p-0.5 text-xs"><div class="rounded-full bg-background px-2 py-1.5"><span class="text-muted-foreground border-r-2 border-foreground/50 pr-1.5">Roo Code Cloud is coming</span><a href="/cloud-waitlist" rel="noopener noreferrer" class="font-medium text-primary hover:underline pl-1.5">Sign up</a></div></div></nav><div class="hidden md:flex md:items-center md:space-x-4"><div class="flex flex-row space-x-2"><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-9 w-9" disabled=""><svg stroke="currentColor" fill="none" stroke-width="0" viewBox="0 0 15 15" class="h-4 w-4" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.5 0C7.77614 0 8 0.223858 8 0.5V2.5C8 2.77614 7.77614 3 7.5 3C7.22386 3 7 2.77614 7 2.5V0.5C7 0.223858 7.22386 0 7.5 0ZM2.1967 2.1967C2.39196 2.00144 2.70854 2.00144 2.90381 2.1967L4.31802 3.61091C4.51328 3.80617 4.51328 4.12276 4.31802 4.31802C4.12276 4.51328 3.80617 4.51328 3.61091 4.31802L2.1967 2.90381C2.00144 2.70854 2.00144 2.39196 2.1967 2.1967ZM0.5 7C0.223858 7 0 7.22386 0 7.5C0 7.77614 0.223858 8 0.5 8H2.5C2.77614 8 3 7.77614 3 7.5C3 7.22386 2.77614 7 2.5 7H0.5ZM2.1967 12.8033C2.00144 12.608 2.00144 12.2915 2.1967 12.0962L3.61091 10.682C3.80617 10.4867 4.12276 10.4867 4.31802 10.682C4.51328 10.8772 4.51328 11.1938 4.31802 11.3891L2.90381 12.8033C2.70854 12.9986 2.39196 12.9986 2.1967 12.8033ZM12.5 7C12.2239 7 12 7.22386 12 7.5C12 7.77614 12.2239 8 12.5 8H14.5C14.7761 8 15 7.77614 15 7.5C15 7.22386 14.7761 7 14.5 7H12.5ZM10.682 4.31802C10.4867 4.12276 10.4867 3.80617 10.682 3.61091L12.0962 2.1967C12.2915 2.00144 12.608 2.00144 12.8033 2.1967C12.9986 2.39196 12.9986 2.70854 12.8033 2.90381L11.3891 4.31802C11.1938 4.51328 10.8772 4.51328 10.682 4.31802ZM8 12.5C8 12.2239 7.77614 12 7.5 12C7.22386 12 7 12.2239 7 12.5V14.5C7 14.7761 7.22386 15 7.5 15C7.77614 15 8 14.7761 8 14.5V12.5ZM10.682 10.682C10.8772 10.4867 11.1938 10.4867 11.3891 10.682L12.8033 12.0962C12.9986 12.2915 12.9986 12.608 12.8033 12.8033C12.608 12.9986 12.2915 12.9986 12.0962 12.8033L10.682 11.3891C10.4867 11.1938 10.4867 10.8772 10.682 10.682ZM5.5 7.5C5.5 6.39543 6.39543 5.5 7.5 5.5C8.60457 5.5 9.5 6.39543 9.5 7.5C9.5 8.60457 8.60457 9.5 7.5 9.5C6.39543 9.5 5.5 8.60457 5.5 7.5ZM7.5 4.5C5.84315 4.5 4.5 5.84315 4.5 7.5C4.5 9.15685 5.84315 10.5 7.5 10.5C9.15685 10.5 10.5 9.15685 10.5 7.5C10.5 5.84315 9.15685 4.5 7.5 4.5Z" fill="currentColor"></path></svg></button><a target="_blank" class="hidden items-center gap-1.5 text-sm font-medium text-muted-foreground hover:text-foreground md:flex" href="https://github.com/RooCodeInc/Roo-Code"><svg stroke="currentColor" fill="none" stroke-width="0" viewBox="0 0 15 15" class="h-4 w-4" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.49933 0.25C3.49635 0.25 0.25 3.49593 0.25 7.50024C0.25 10.703 2.32715 13.4206 5.2081 14.3797C5.57084 14.446 5.70302 14.2222 5.70302 14.0299C5.70302 13.8576 5.69679 13.4019 5.69323 12.797C3.67661 13.235 3.25112 11.825 3.25112 11.825C2.92132 10.9874 2.44599 10.7644 2.44599 10.7644C1.78773 10.3149 2.49584 10.3238 2.49584 10.3238C3.22353 10.375 3.60629 11.0711 3.60629 11.0711C4.25298 12.1788 5.30335 11.8588 5.71638 11.6732C5.78225 11.205 5.96962 10.8854 6.17658 10.7043C4.56675 10.5209 2.87415 9.89918 2.87415 7.12104C2.87415 6.32925 3.15677 5.68257 3.62053 5.17563C3.54576 4.99226 3.29697 4.25521 3.69174 3.25691C3.69174 3.25691 4.30015 3.06196 5.68522 3.99973C6.26337 3.83906 6.8838 3.75895 7.50022 3.75583C8.1162 3.75895 8.73619 3.83906 9.31523 3.99973C10.6994 3.06196 11.3069 3.25691 11.3069 3.25691C11.7026 4.25521 11.4538 4.99226 11.3795 5.17563C11.8441 5.68257 12.1245 6.32925 12.1245 7.12104C12.1245 9.9063 10.4292 10.5192 8.81452 10.6985C9.07444 10.9224 9.30633 11.3648 9.30633 12.0413C9.30633 13.0102 9.29742 13.7922 9.29742 14.0299C9.29742 14.2239 9.42828 14.4496 9.79591 14.3788C12.6746 13.4179 14.75 10.7025 14.75 7.50024C14.75 3.49593 11.5036 0.25 7.49933 0.25Z" fill="currentColor"></path></svg><span>17.6k</span></a></div><a target="_blank" class="hidden items-center gap-1.5 rounded-full bg-primary px-3 py-1.5 text-sm font-medium text-primary-foreground transition-colors hover:bg-primary/90 md:flex" href="https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 16 16" class="-mr-[2px] mt-[1px] h-4 w-4" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M10.8634 13.9195C10.6568 14.0195 10.4233 14.0246 10.2185 13.9444C10.1162 13.9044 10.021 13.843 9.93997 13.7614L4.81616 9.06268L2.58433 10.7656C2.37657 10.9241 2.08597 10.9111 1.89301 10.7347L1.17719 10.0802C0.941168 9.86437 0.940898 9.49112 1.17661 9.27496L3.11213 7.5L1.17661 5.72504C0.940898 5.50888 0.941168 5.13563 1.17719 4.91982L1.89301 4.2653C2.08597 4.08887 2.37657 4.07588 2.58433 4.2344L4.81616 5.93732L9.93997 1.23855C9.97037 1.20797 10.0028 1.18023 10.0368 1.15538C10.2748 0.981429 10.5922 0.949298 10.8634 1.08048L13.5399 2.37507C13.8212 2.5111 14 2.79721 14 3.11109V8H10.752V4.53356L6.86419 7.5L10.752 10.4664V8H14V11.8889C14 12.2028 13.8211 12.4889 13.5399 12.625L10.8634 13.9195Z"></path></svg><span>Install <span class="font-black max-lg:text-xs">·</span></span><span>702.6k</span></a></div><button aria-expanded="false" class="flex items-center justify-center rounded-full p-2 transition-colors hover:bg-accent md:hidden" aria-label="Toggle mobile menu"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 20 20" aria-hidden="true" class="h-6 w-6" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path></svg></button></div><div class="absolute left-0 right-0 top-16 z-50 transform border-b border-border bg-background shadow-lg backdrop-blur-none transition-all duration-200 md:hidden pointer-events-none -translate-y-2 opacity-0"><nav class="flex flex-col py-2"><div class="mx-5 mb-2 flex items-center rounded-full bg-gradient-to-r from-blue-400 to-cyan-400 p-0.5 text-xs"><div class="flex-grow text-center rounded-full bg-background px-2 py-1.5"><span class="text-muted-foreground border-r-2 border-foreground/50 pr-3">Roo Code Cloud is coming</span><a href="/cloud-waitlist" rel="noopener noreferrer" class="font-medium text-primary hover:underline pl-3">Sign up</a></div></div><button class="w-full px-8 py-3 text-left text-sm font-medium text-foreground/80 transition-colors hover:bg-accent hover:text-foreground">Features</button><button class="w-full px-8 py-3 text-left text-sm font-medium text-foreground/80 transition-colors hover:bg-accent hover:text-foreground">Testimonials</button><button class="w-full px-8 py-3 text-left text-sm font-medium text-foreground/80 transition-colors hover:bg-accent hover:text-foreground">FAQ</button><a class="w-full px-8 py-3 text-left text-sm font-medium text-foreground/80 transition-colors hover:bg-accent hover:text-foreground" href="/evals">Evals</a><a class="w-full px-8 py-3 text-left text-sm font-medium text-foreground/80 transition-colors hover:bg-accent hover:text-foreground" href="/enterprise">Enterprise</a><a href="https://trust.roocode.com" target="_blank" rel="noopener noreferrer" class="w-full px-8 py-3 text-left text-sm font-medium text-foreground/80 transition-colors hover:bg-accent hover:text-foreground">Security</a><a href="https://docs.roocode.com" target="_blank" class="w-full px-8 py-3 text-left text-sm font-medium text-foreground/80 transition-colors hover:bg-accent hover:text-foreground">Docs</a><a href="https://careers.roocode.com" target="_blank" class="w-full px-8 py-3 text-left text-sm font-medium text-foreground/80 transition-colors hover:bg-accent hover:text-foreground">Careers</a><hr class="mx-8 my-2 border-t border-border/50"/><div class="flex items-center justify-center gap-8 px-8 py-3"><a target="_blank" class="inline-flex items-center gap-2 rounded-md p-2 text-sm font-medium text-foreground/80 transition-colors hover:bg-accent hover:text-foreground" href="https://github.com/RooCodeInc/Roo-Code"><svg stroke="currentColor" fill="none" stroke-width="0" viewBox="0 0 15 15" class="h-5 w-5" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.49933 0.25C3.49635 0.25 0.25 3.49593 0.25 7.50024C0.25 10.703 2.32715 13.4206 5.2081 14.3797C5.57084 14.446 5.70302 14.2222 5.70302 14.0299C5.70302 13.8576 5.69679 13.4019 5.69323 12.797C3.67661 13.235 3.25112 11.825 3.25112 11.825C2.92132 10.9874 2.44599 10.7644 2.44599 10.7644C1.78773 10.3149 2.49584 10.3238 2.49584 10.3238C3.22353 10.375 3.60629 11.0711 3.60629 11.0711C4.25298 12.1788 5.30335 11.8588 5.71638 11.6732C5.78225 11.205 5.96962 10.8854 6.17658 10.7043C4.56675 10.5209 2.87415 9.89918 2.87415 7.12104C2.87415 6.32925 3.15677 5.68257 3.62053 5.17563C3.54576 4.99226 3.29697 4.25521 3.69174 3.25691C3.69174 3.25691 4.30015 3.06196 5.68522 3.99973C6.26337 3.83906 6.8838 3.75895 7.50022 3.75583C8.1162 3.75895 8.73619 3.83906 9.31523 3.99973C10.6994 3.06196 11.3069 3.25691 11.3069 3.25691C11.7026 4.25521 11.4538 4.99226 11.3795 5.17563C11.8441 5.68257 12.1245 6.32925 12.1245 7.12104C12.1245 9.9063 10.4292 10.5192 8.81452 10.6985C9.07444 10.9224 9.30633 11.3648 9.30633 12.0413C9.30633 13.0102 9.29742 13.7922 9.29742 14.0299C9.29742 14.2239 9.42828 14.4496 9.79591 14.3788C12.6746 13.4179 14.75 10.7025 14.75 7.50024C14.75 3.49593 11.5036 0.25 7.49933 0.25Z" fill="currentColor"></path></svg><span>17.6k</span></a><div class="flex items-center rounded-md p-2 transition-colors hover:bg-accent"><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-9 w-9" disabled=""><svg stroke="currentColor" fill="none" stroke-width="0" viewBox="0 0 15 15" class="h-4 w-4" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.5 0C7.77614 0 8 0.223858 8 0.5V2.5C8 2.77614 7.77614 3 7.5 3C7.22386 3 7 2.77614 7 2.5V0.5C7 0.223858 7.22386 0 7.5 0ZM2.1967 2.1967C2.39196 2.00144 2.70854 2.00144 2.90381 2.1967L4.31802 3.61091C4.51328 3.80617 4.51328 4.12276 4.31802 4.31802C4.12276 4.51328 3.80617 4.51328 3.61091 4.31802L2.1967 2.90381C2.00144 2.70854 2.00144 2.39196 2.1967 2.1967ZM0.5 7C0.223858 7 0 7.22386 0 7.5C0 7.77614 0.223858 8 0.5 8H2.5C2.77614 8 3 7.77614 3 7.5C3 7.22386 2.77614 7 2.5 7H0.5ZM2.1967 12.8033C2.00144 12.608 2.00144 12.2915 2.1967 12.0962L3.61091 10.682C3.80617 10.4867 4.12276 10.4867 4.31802 10.682C4.51328 10.8772 4.51328 11.1938 4.31802 11.3891L2.90381 12.8033C2.70854 12.9986 2.39196 12.9986 2.1967 12.8033ZM12.5 7C12.2239 7 12 7.22386 12 7.5C12 7.77614 12.2239 8 12.5 8H14.5C14.7761 8 15 7.77614 15 7.5C15 7.22386 14.7761 7 14.5 7H12.5ZM10.682 4.31802C10.4867 4.12276 10.4867 3.80617 10.682 3.61091L12.0962 2.1967C12.2915 2.00144 12.608 2.00144 12.8033 2.1967C12.9986 2.39196 12.9986 2.70854 12.8033 2.90381L11.3891 4.31802C11.1938 4.51328 10.8772 4.51328 10.682 4.31802ZM8 12.5C8 12.2239 7.77614 12 7.5 12C7.22386 12 7 12.2239 7 12.5V14.5C7 14.7761 7.22386 15 7.5 15C7.77614 15 8 14.7761 8 14.5V12.5ZM10.682 10.682C10.8772 10.4867 11.1938 10.4867 11.3891 10.682L12.8033 12.0962C12.9986 12.2915 12.9986 12.608 12.8033 12.8033C12.608 12.9986 12.2915 12.9986 12.0962 12.8033L10.682 11.3891C10.4867 11.1938 10.4867 10.8772 10.682 10.682ZM5.5 7.5C5.5 6.39543 6.39543 5.5 7.5 5.5C8.60457 5.5 9.5 6.39543 9.5 7.5C9.5 8.60457 8.60457 9.5 7.5 9.5C6.39543 9.5 5.5 8.60457 5.5 7.5ZM7.5 4.5C5.84315 4.5 4.5 5.84315 4.5 7.5C4.5 9.15685 5.84315 10.5 7.5 10.5C9.15685 10.5 10.5 9.15685 10.5 7.5C10.5 5.84315 9.15685 4.5 7.5 4.5Z" fill="currentColor"></path></svg></button></div><a target="_blank" class="inline-flex items-center gap-2 rounded-md p-2 text-sm font-medium text-foreground/80 transition-colors hover:bg-accent hover:text-foreground" href="https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 16 16" class="h-5 w-5" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M10.8634 13.9195C10.6568 14.0195 10.4233 14.0246 10.2185 13.9444C10.1162 13.9044 10.021 13.843 9.93997 13.7614L4.81616 9.06268L2.58433 10.7656C2.37657 10.9241 2.08597 10.9111 1.89301 10.7347L1.17719 10.0802C0.941168 9.86437 0.940898 9.49112 1.17661 9.27496L3.11213 7.5L1.17661 5.72504C0.940898 5.50888 0.941168 5.13563 1.17719 4.91982L1.89301 4.2653C2.08597 4.08887 2.37657 4.07588 2.58433 4.2344L4.81616 5.93732L9.93997 1.23855C9.97037 1.20797 10.0028 1.18023 10.0368 1.15538C10.2748 0.981429 10.5922 0.949298 10.8634 1.08048L13.5399 2.37507C13.8212 2.5111 14 2.79721 14 3.11109V8H10.752V4.53356L6.86419 7.5L10.752 10.4664V8H14V11.8889C14 12.2028 13.8211 12.4889 13.5399 12.625L10.8634 13.9195Z"></path></svg><span>702.6k</span></a></div></nav></div></header><main class="flex-1"><section class="relative flex h-[calc(125vh-theme(spacing.16))] items-center overflow-hidden md:h-[calc(100svh-theme(spacing.16))] lg:h-[calc(100vh-theme(spacing.16))]"><canvas class="absolute inset-0 h-full w-full" style="z-index:0"></canvas><div class="container relative z-10 mx-auto px-4 sm:px-6 lg:px-8"><div class="grid gap-8 md:gap-12 lg:grid-cols-2 lg:gap-16"><div class="flex flex-col justify-center space-y-6 sm:space-y-8"><div><h1 class="text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl lg:text-6xl"><span class="block">Your</span><span class="bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent" style="opacity:0;transform:translateY(20px)">AI-Powered</span><span class="block">Dev Team, Right in Your Editor.</span></h1><p class="mt-4 max-w-md text-base text-muted-foreground sm:mt-6 sm:text-lg">Supercharge your editor with AI that<!-- --> <span class="bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent" style="opacity:0;transform:translateY(20px)">understands your codebase</span>, streamlines development, and helps you write, refactor, and debug with ease.</p></div><div class="flex flex-col space-y-3 sm:flex-row sm:space-x-4 sm:space-y-0"><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 bg-primary text-primary-foreground shadow h-10 rounded-md px-8 w-full hover:bg-gray-200 dark:bg-white dark:text-black sm:w-auto"><a href="https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline" target="_blank" class="flex w-full items-center justify-center">Install Roo Code<svg xmlns="http://www.w3.org/2000/svg" class="ml-2 h-4 w-4" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg></a></button><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-10 rounded-md px-8 w-full sm:w-auto"><a href="https://docs.roocode.com" target="_blank" class="flex w-full items-center justify-center">View Documentation</a></button></div></div><div class="relative mt-8 flex items-center justify-center lg:mt-0"><div class="absolute inset-0 flex items-center justify-center"><div class="h-[250px] w-[250px] rounded-full bg-blue-500/20 blur-[100px] sm:h-[300px] sm:w-[300px] md:h-[350px] md:w-[350px]"></div></div><div class="relative z-10 w-full max-w-[90vw] rounded-lg border border-border bg-background/50 p-2 shadow-2xl backdrop-blur-sm sm:max-w-[500px]"><div class="rounded-md bg-muted p-1.5 dark:bg-gray-900 sm:p-2"><div class="flex items-center justify-between border-b border-border px-2 py-1.5 sm:px-3 sm:py-2"><div class="flex items-center space-x-1.5"><div class="h-2.5 w-2.5 rounded-full bg-red-500 sm:h-3 sm:w-3"></div><div class="h-2.5 w-2.5 rounded-full bg-yellow-500 sm:h-3 sm:w-3"></div><div class="h-2.5 w-2.5 rounded-full bg-green-500 sm:h-3 sm:w-3"></div></div><div class="flex space-x-1"><button class="rounded px-2 py-0.5 text-xs font-medium transition-colors sm:text-sm bg-blue-500/20 text-blue-400">Code</button><button class="rounded px-2 py-0.5 text-xs font-medium transition-colors sm:text-sm text-gray-400 hover:bg-gray-800">Architect</button><button class="rounded px-2 py-0.5 text-xs font-medium transition-colors sm:text-sm text-gray-400 hover:bg-gray-800">Debug</button></div></div><div class="p-2 sm:p-4"><pre class="scrollbar-thin scrollbar-thumb-gray-700 scrollbar-track-transparent overflow-y-hidden text-xs text-muted-foreground max-lg:h-[25vh] sm:text-sm lg:max-h-[50vh]"><code class="block whitespace-pre font-mono"></code></pre></div></div></div></div></div></div></section><div id="features"><section class="relative overflow-hidden border-t border-border py-32"><div class="absolute inset-0" style="opacity:0"><div class="absolute inset-y-0 left-1/2 h-full w-full max-w-[1200px] -translate-x-1/2"><div class="absolute left-1/2 top-1/2 h-[800px] w-full -translate-x-1/2 -translate-y-1/2 rounded-[100%] bg-blue-500/10 blur-[120px]"></div></div></div><div class="container relative z-10 mx-auto px-4 sm:px-6 lg:px-8"><div class="mx-auto mb-24 max-w-3xl text-center"><div style="opacity:0;transform:translateY(20px)"><h2 class="bg-gradient-to-b from-foreground to-foreground/70 bg-clip-text text-4xl font-bold tracking-tight text-transparent sm:text-5xl">Powerful features for modern developers.</h2><p class="mt-6 text-lg text-muted-foreground">Everything you need to build faster and write better code.</p></div></div><div class="md:hidden"><div class="relative px-4"><div class="overflow-hidden"><div class="flex"><div class="flex min-w-0 flex-[0_0_100%] px-4"><div class="relative h-full min-h-[280px] rounded-2xl border border-border/50 bg-background/30 p-6 backdrop-blur-xl transition-colors duration-300 hover:border-border hover:bg-gray-900/20"><div class="mb-2 inline-flex items-center justify-center rounded-xl bg-gradient-to-r from-blue-500/5 to-cyan-500/5 p-2.5"><div class="rounded-lg bg-gradient-to-r from-blue-500/80 to-cyan-500/80 p-2.5"><div class="text-foreground/90"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 640 512" class="h-6 w-6" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M32,224H64V416H32A31.96166,31.96166,0,0,1,0,384V256A31.96166,31.96166,0,0,1,32,224Zm512-48V448a64.06328,64.06328,0,0,1-64,64H160a64.06328,64.06328,0,0,1-64-64V176a79.974,79.974,0,0,1,80-80H288V32a32,32,0,0,1,64,0V96H464A79.974,79.974,0,0,1,544,176ZM264,256a40,40,0,1,0-40,40A39.997,39.997,0,0,0,264,256Zm-8,128H192v32h64Zm96,0H288v32h64ZM456,256a40,40,0,1,0-40,40A39.997,39.997,0,0,0,456,256Zm-8,128H384v32h64ZM640,256V384a31.96166,31.96166,0,0,1-32,32H576V224h32A31.96166,31.96166,0,0,1,640,256Z"></path></svg></div></div></div><h3 class="mb-3 text-xl font-medium text-foreground/90">Your AI Dev Team in VS Code</h3><p class="leading-relaxed text-muted-foreground">Roo Code puts a team of agentic AI assistants directly in your editor, with the power to plan, write, and fix code across multiple files.</p></div></div><div class="flex min-w-0 flex-[0_0_100%] px-4"><div class="relative h-full min-h-[280px] rounded-2xl border border-border/50 bg-background/30 p-6 backdrop-blur-xl transition-colors duration-300 hover:border-border hover:bg-gray-900/20"><div class="mb-2 inline-flex items-center justify-center rounded-xl bg-gradient-to-r from-blue-500/5 to-cyan-500/5 p-2.5"><div class="rounded-lg bg-gradient-to-r from-blue-500/80 to-cyan-500/80 p-2.5"><div class="text-foreground/90"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 640 512" class="h-6 w-6" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M278.9 511.5l-61-17.7c-6.4-1.8-10-8.5-8.2-14.9L346.2 8.7c1.8-6.4 8.5-10 14.9-8.2l61 17.7c6.4 1.8 10 8.5 8.2 14.9L293.8 503.3c-1.9 6.4-8.5 10.1-14.9 8.2zm-114-112.2l43.5-46.4c4.6-4.9 4.3-12.7-.8-17.2L117 256l90.6-79.7c5.1-4.5 5.5-12.3.8-17.2l-43.5-46.4c-4.5-4.8-12.1-5.1-17-.5L3.8 247.2c-5.1 4.7-5.1 12.8 0 17.5l144.1 135.1c4.9 4.6 12.5 4.4 17-.5zm327.2.6l144.1-135.1c5.1-4.7 5.1-12.8 0-17.5L492.1 112.1c-4.8-4.5-12.4-4.3-17 .5L431.6 159c-4.6 4.9-4.3 12.7.8 17.2L523 256l-90.6 79.7c-5.1 4.5-5.5 12.3-.8 17.2l43.5 46.4c4.5 4.9 12.1 5.1 17 .6z"></path></svg></div></div></div><h3 class="mb-3 text-xl font-medium text-foreground/90">Multiple Specialized Modes</h3><p class="leading-relaxed text-muted-foreground">From coding to debugging to architecture, Roo Code has a mode for every dev scenario—just switch on the fly.</p></div></div><div class="flex min-w-0 flex-[0_0_100%] px-4"><div class="relative h-full min-h-[280px] rounded-2xl border border-border/50 bg-background/30 p-6 backdrop-blur-xl transition-colors duration-300 hover:border-border hover:bg-gray-900/20"><div class="mb-2 inline-flex items-center justify-center rounded-xl bg-gradient-to-r from-blue-500/5 to-cyan-500/5 p-2.5"><div class="rounded-lg bg-gradient-to-r from-blue-500/80 to-cyan-500/80 p-2.5"><div class="text-foreground/90"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" class="h-6 w-6" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M208 0c-29.9 0-54.7 20.5-61.8 48.2-.8 0-1.4-.2-2.2-.2-35.3 0-64 28.7-64 64 0 4.8.6 9.5 1.7 14C52.5 138 32 166.6 32 200c0 12.6 3.2 24.3 8.3 34.9C16.3 248.7 0 274.3 0 304c0 33.3 20.4 61.9 49.4 73.9-.9 4.6-1.4 9.3-1.4 14.1 0 39.8 32.2 72 72 72 4.1 0 8.1-.5 12-1.2 9.6 28.5 36.2 49.2 68 49.2 39.8 0 72-32.2 72-72V64c0-35.3-28.7-64-64-64zm368 304c0-29.7-16.3-55.3-40.3-69.1 5.2-10.6 8.3-22.3 8.3-34.9 0-33.4-20.5-62-49.7-74 1-4.5 1.7-9.2 1.7-14 0-35.3-28.7-64-64-64-.8 0-1.5.2-2.2.2C422.7 20.5 397.9 0 368 0c-35.3 0-64 28.6-64 64v376c0 39.8 32.2 72 72 72 31.8 0 58.4-20.7 68-49.2 3.9.7 7.9 1.2 12 1.2 39.8 0 72-32.2 72-72 0-4.8-.5-9.5-1.4-14.1 29-12 49.4-40.6 49.4-73.9z"></path></svg></div></div></div><h3 class="mb-3 text-xl font-medium text-foreground/90">Deep Project-wide Context</h3><p class="leading-relaxed text-muted-foreground">Roo Code reads your entire codebase, preserving valid code through diff-based edits for seamless multi-file refactors.</p></div></div><div class="flex min-w-0 flex-[0_0_100%] px-4"><div class="relative h-full min-h-[280px] rounded-2xl border border-border/50 bg-background/30 p-6 backdrop-blur-xl transition-colors duration-300 hover:border-border hover:bg-gray-900/20"><div class="mb-2 inline-flex items-center justify-center rounded-xl bg-gradient-to-r from-blue-500/5 to-cyan-500/5 p-2.5"><div class="rounded-lg bg-gradient-to-r from-blue-500/80 to-cyan-500/80 p-2.5"><div class="text-foreground/90"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" class="h-6 w-6" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M501.1 395.7L384 278.6c-23.1-23.1-57.6-27.6-85.4-13.9L192 158.1V96L64 0 0 64l96 128h62.1l106.6 106.6c-13.6 27.8-9.2 62.3 13.9 85.4l117.1 117.1c14.6 14.6 38.2 14.6 52.7 0l52.7-52.7c14.5-14.6 14.5-38.2 0-52.7zM331.7 225c28.3 0 54.9 11 74.9 31l19.4 19.4c15.8-6.9 30.8-16.5 43.8-29.5 37.1-37.1 49.7-89.3 37.9-136.7-2.2-9-13.5-12.1-20.1-5.5l-74.4 74.4-67.9-11.3L334 98.9l74.4-74.4c6.6-6.6 3.4-17.9-5.7-20.2-47.4-11.7-99.6.9-136.6 37.9-28.5 28.5-41.9 66.1-41.2 103.6l82.1 82.1c8.1-1.9 16.5-2.9 24.7-2.9zm-103.9 82l-56.7-56.7L18.7 402.8c-25 25-25 65.5 0 90.5s65.5 25 90.5 0l123.6-123.6c-7.6-19.9-9.9-41.6-5-62.7zM64 472c-13.2 0-24-10.8-24-24 0-13.3 10.7-24 24-24s24 10.7 24 24c0 13.2-10.7 24-24 24z"></path></svg></div></div></div><h3 class="mb-3 text-xl font-medium text-foreground/90">Open-Source and Model-Agnostic</h3><p class="leading-relaxed text-muted-foreground">Bring your own model or use local AI—no vendor lock-in. Roo Code is free, open, and adaptable to your needs.</p></div></div><div class="flex min-w-0 flex-[0_0_100%] px-4"><div class="relative h-full min-h-[280px] rounded-2xl border border-border/50 bg-background/30 p-6 backdrop-blur-xl transition-colors duration-300 hover:border-border hover:bg-gray-900/20"><div class="mb-2 inline-flex items-center justify-center rounded-xl bg-gradient-to-r from-blue-500/5 to-cyan-500/5 p-2.5"><div class="rounded-lg bg-gradient-to-r from-blue-500/80 to-cyan-500/80 p-2.5"><div class="text-foreground/90"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 640 512" class="h-6 w-6" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M257.981 272.971L63.638 467.314c-9.373 9.373-24.569 9.373-33.941 0L7.029 444.647c-9.357-9.357-9.375-24.522-.04-33.901L161.011 256 6.99 101.255c-9.335-9.379-9.317-24.544.04-33.901l22.667-22.667c9.373-9.373 24.569-9.373 33.941 0L257.981 239.03c9.373 9.372 9.373 24.568 0 33.941zM640 456v-32c0-13.255-10.745-24-24-24H312c-13.255 0-24 10.745-24 24v32c0 13.255 10.745 24 24 24h304c13.255 0 24-10.745 24-24z"></path></svg></div></div></div><h3 class="mb-3 text-xl font-medium text-foreground/90">Guarded Command Execution</h3><p class="leading-relaxed text-muted-foreground">Approve or deny commands as needed. Roo Code automates your dev workflow while keeping oversight firmly in your hands.</p></div></div><div class="flex min-w-0 flex-[0_0_100%] px-4"><div class="relative h-full min-h-[280px] rounded-2xl border border-border/50 bg-background/30 p-6 backdrop-blur-xl transition-colors duration-300 hover:border-border hover:bg-gray-900/20"><div class="mb-2 inline-flex items-center justify-center rounded-xl bg-gradient-to-r from-blue-500/5 to-cyan-500/5 p-2.5"><div class="rounded-lg bg-gradient-to-r from-blue-500/80 to-cyan-500/80 p-2.5"><div class="text-foreground/90"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" class="h-6 w-6" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M519.442 288.651c-41.519 0-59.5 31.593-82.058 31.593C377.409 320.244 432 144 432 144s-196.288 80-196.288-3.297c0-35.827 36.288-46.25 36.288-85.985C272 19.216 243.885 0 210.539 0c-34.654 0-66.366 18.891-66.366 56.346 0 41.364 31.711 59.277 31.711 81.75C175.885 207.719 0 166.758 0 166.758v333.237s178.635 41.047 178.635-28.662c0-22.473-40-40.107-40-81.471 0-37.456 29.25-56.346 63.577-56.346 33.673 0 61.788 19.216 61.788 54.717 0 39.735-36.288 50.158-36.288 85.985 0 60.803 129.675 25.73 181.23 25.73 0 0-34.725-120.101 25.827-120.101 35.962 0 46.423 36.152 86.308 36.152C556.712 416 576 387.99 576 354.443c0-34.199-18.962-65.792-56.558-65.792z"></path></svg></div></div></div><h3 class="mb-3 text-xl font-medium text-foreground/90">Fully Customizable</h3><p class="leading-relaxed text-muted-foreground">Create or tweak modes, define usage rules, and shape Roo Code’s behavior precisely—your code, your way.</p></div></div><div class="flex min-w-0 flex-[0_0_100%] px-4"><div class="relative h-full min-h-[280px] rounded-2xl border border-border/50 bg-background/30 p-6 backdrop-blur-xl transition-colors duration-300 hover:border-border hover:bg-gray-900/20"><div class="mb-2 inline-flex items-center justify-center rounded-xl bg-gradient-to-r from-blue-500/5 to-cyan-500/5 p-2.5"><div class="rounded-lg bg-gradient-to-r from-blue-500/80 to-cyan-500/80 p-2.5"><div class="text-foreground/90"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 496 512" class="h-6 w-6" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M336.5 160C322 70.7 287.8 8 248 8s-74 62.7-88.5 152h177zM152 256c0 22.2 1.2 43.5 3.3 64h185.3c2.1-20.5 3.3-41.8 3.3-64s-1.2-43.5-3.3-64H155.3c-2.1 20.5-3.3 41.8-3.3 64zm324.7-96c-28.6-67.9-86.5-120.4-158-141.6 24.4 33.8 41.2 84.7 50 141.6h108zM177.2 18.4C105.8 39.6 47.8 92.1 19.3 160h108c8.7-56.9 25.5-107.8 49.9-141.6zM487.4 192H372.7c2.1 21 3.3 42.5 3.3 64s-1.2 43-3.3 64h114.6c5.5-20.5 8.6-41.8 8.6-64s-3.1-43.5-8.5-64zM120 256c0-21.5 1.2-43 3.3-64H8.6C3.2 212.5 0 233.8 0 256s3.2 43.5 8.6 64h114.6c-2-21-3.2-42.5-3.2-64zm39.5 96c14.5 89.3 48.7 152 88.5 152s74-62.7 88.5-152h-177zm159.3 141.6c71.4-21.2 129.4-73.7 158-141.6h-108c-8.8 56.9-25.6 107.8-50 141.6zM19.3 352c28.6 67.9 86.5 120.4 158 141.6-24.4-33.8-41.2-84.7-50-141.6h-108z"></path></svg></div></div></div><h3 class="mb-3 text-xl font-medium text-foreground/90">Automated Browser Actions</h3><p class="leading-relaxed text-muted-foreground">Seamlessly test and verify your web app directly from VS Code—Roo Code can open a browser, run checks, and more.</p></div></div></div></div><div class="mt-6 flex items-center justify-between px-4"><div class="flex gap-2"><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 border shadow-sm hover:text-accent-foreground h-8 w-8 rounded-full border-border/50 bg-background/80 hover:bg-background"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left h-4 w-4 text-foreground/80" aria-hidden="true"><path d="m15 18-6-6 6-6"></path></svg><span class="sr-only">Previous slide</span></button><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 border shadow-sm hover:text-accent-foreground h-8 w-8 rounded-full border-border/50 bg-background/80 hover:bg-background"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right h-4 w-4 text-foreground/80" aria-hidden="true"><path d="m9 18 6-6-6-6"></path></svg><span class="sr-only">Next slide</span></button></div><div class="flex gap-2"></div></div></div></div><div class="relative mx-auto hidden max-w-[1200px] md:block" style="opacity:0"><div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 lg:gap-8"><div class="group relative lg:col-span-2 lg:translate-y-12" style="opacity:0;transform:translateY(20px)"><div class="absolute -inset-px rounded-2xl bg-gradient-to-r from-blue-500/30 via-cyan-500/30 to-purple-500/30 opacity-0 blur-sm transition-opacity duration-500 group-hover:opacity-100"></div><div class="relative h-full rounded-2xl border border-border/50 bg-background/30 p-8 backdrop-blur-xl transition-colors duration-300 hover:border-border"><div class="mb-5 inline-flex items-center justify-center rounded-xl bg-gradient-to-r from-blue-500/5 to-cyan-500/5 p-2.5"><div class="rounded-lg bg-gradient-to-r from-blue-500/80 to-cyan-500/80 p-2.5"><div class="text-foreground/90"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 640 512" class="h-6 w-6" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M32,224H64V416H32A31.96166,31.96166,0,0,1,0,384V256A31.96166,31.96166,0,0,1,32,224Zm512-48V448a64.06328,64.06328,0,0,1-64,64H160a64.06328,64.06328,0,0,1-64-64V176a79.974,79.974,0,0,1,80-80H288V32a32,32,0,0,1,64,0V96H464A79.974,79.974,0,0,1,544,176ZM264,256a40,40,0,1,0-40,40A39.997,39.997,0,0,0,264,256Zm-8,128H192v32h64Zm96,0H288v32h64ZM456,256a40,40,0,1,0-40,40A39.997,39.997,0,0,0,456,256Zm-8,128H384v32h64ZM640,256V384a31.96166,31.96166,0,0,1-32,32H576V224h32A31.96166,31.96166,0,0,1,640,256Z"></path></svg></div></div></div><h3 class="mb-3 text-xl font-medium text-foreground/90">Your AI Dev Team in VS Code</h3><p class="leading-relaxed text-muted-foreground">Roo Code puts a team of agentic AI assistants directly in your editor, with the power to plan, write, and fix code across multiple files.</p></div></div><div class="group relative  " style="opacity:0;transform:translateY(20px)"><div class="absolute -inset-px rounded-2xl bg-gradient-to-r from-blue-500/30 via-cyan-500/30 to-purple-500/30 opacity-0 blur-sm transition-opacity duration-500 group-hover:opacity-100"></div><div class="relative h-full rounded-2xl border border-border/50 bg-background/30 p-8 backdrop-blur-xl transition-colors duration-300 hover:border-border"><div class="mb-5 inline-flex items-center justify-center rounded-xl bg-gradient-to-r from-blue-500/5 to-cyan-500/5 p-2.5"><div class="rounded-lg bg-gradient-to-r from-blue-500/80 to-cyan-500/80 p-2.5"><div class="text-foreground/90"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 640 512" class="h-6 w-6" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M278.9 511.5l-61-17.7c-6.4-1.8-10-8.5-8.2-14.9L346.2 8.7c1.8-6.4 8.5-10 14.9-8.2l61 17.7c6.4 1.8 10 8.5 8.2 14.9L293.8 503.3c-1.9 6.4-8.5 10.1-14.9 8.2zm-114-112.2l43.5-46.4c4.6-4.9 4.3-12.7-.8-17.2L117 256l90.6-79.7c5.1-4.5 5.5-12.3.8-17.2l-43.5-46.4c-4.5-4.8-12.1-5.1-17-.5L3.8 247.2c-5.1 4.7-5.1 12.8 0 17.5l144.1 135.1c4.9 4.6 12.5 4.4 17-.5zm327.2.6l144.1-135.1c5.1-4.7 5.1-12.8 0-17.5L492.1 112.1c-4.8-4.5-12.4-4.3-17 .5L431.6 159c-4.6 4.9-4.3 12.7.8 17.2L523 256l-90.6 79.7c-5.1 4.5-5.5 12.3-.8 17.2l43.5 46.4c4.5 4.9 12.1 5.1 17 .6z"></path></svg></div></div></div><h3 class="mb-3 text-xl font-medium text-foreground/90">Multiple Specialized Modes</h3><p class="leading-relaxed text-muted-foreground">From coding to debugging to architecture, Roo Code has a mode for every dev scenario—just switch on the fly.</p></div></div><div class="group relative  lg:translate-y-12" style="opacity:0;transform:translateY(20px)"><div class="absolute -inset-px rounded-2xl bg-gradient-to-r from-blue-500/30 via-cyan-500/30 to-purple-500/30 opacity-0 blur-sm transition-opacity duration-500 group-hover:opacity-100"></div><div class="relative h-full rounded-2xl border border-border/50 bg-background/30 p-8 backdrop-blur-xl transition-colors duration-300 hover:border-border"><div class="mb-5 inline-flex items-center justify-center rounded-xl bg-gradient-to-r from-blue-500/5 to-cyan-500/5 p-2.5"><div class="rounded-lg bg-gradient-to-r from-blue-500/80 to-cyan-500/80 p-2.5"><div class="text-foreground/90"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" class="h-6 w-6" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M208 0c-29.9 0-54.7 20.5-61.8 48.2-.8 0-1.4-.2-2.2-.2-35.3 0-64 28.7-64 64 0 4.8.6 9.5 1.7 14C52.5 138 32 166.6 32 200c0 12.6 3.2 24.3 8.3 34.9C16.3 248.7 0 274.3 0 304c0 33.3 20.4 61.9 49.4 73.9-.9 4.6-1.4 9.3-1.4 14.1 0 39.8 32.2 72 72 72 4.1 0 8.1-.5 12-1.2 9.6 28.5 36.2 49.2 68 49.2 39.8 0 72-32.2 72-72V64c0-35.3-28.7-64-64-64zm368 304c0-29.7-16.3-55.3-40.3-69.1 5.2-10.6 8.3-22.3 8.3-34.9 0-33.4-20.5-62-49.7-74 1-4.5 1.7-9.2 1.7-14 0-35.3-28.7-64-64-64-.8 0-1.5.2-2.2.2C422.7 20.5 397.9 0 368 0c-35.3 0-64 28.6-64 64v376c0 39.8 32.2 72 72 72 31.8 0 58.4-20.7 68-49.2 3.9.7 7.9 1.2 12 1.2 39.8 0 72-32.2 72-72 0-4.8-.5-9.5-1.4-14.1 29-12 49.4-40.6 49.4-73.9z"></path></svg></div></div></div><h3 class="mb-3 text-xl font-medium text-foreground/90">Deep Project-wide Context</h3><p class="leading-relaxed text-muted-foreground">Roo Code reads your entire codebase, preserving valid code through diff-based edits for seamless multi-file refactors.</p></div></div><div class="group relative lg:col-span-2 " style="opacity:0;transform:translateY(20px)"><div class="absolute -inset-px rounded-2xl bg-gradient-to-r from-blue-500/30 via-cyan-500/30 to-purple-500/30 opacity-0 blur-sm transition-opacity duration-500 group-hover:opacity-100"></div><div class="relative h-full rounded-2xl border border-border/50 bg-background/30 p-8 backdrop-blur-xl transition-colors duration-300 hover:border-border"><div class="mb-5 inline-flex items-center justify-center rounded-xl bg-gradient-to-r from-blue-500/5 to-cyan-500/5 p-2.5"><div class="rounded-lg bg-gradient-to-r from-blue-500/80 to-cyan-500/80 p-2.5"><div class="text-foreground/90"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" class="h-6 w-6" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M501.1 395.7L384 278.6c-23.1-23.1-57.6-27.6-85.4-13.9L192 158.1V96L64 0 0 64l96 128h62.1l106.6 106.6c-13.6 27.8-9.2 62.3 13.9 85.4l117.1 117.1c14.6 14.6 38.2 14.6 52.7 0l52.7-52.7c14.5-14.6 14.5-38.2 0-52.7zM331.7 225c28.3 0 54.9 11 74.9 31l19.4 19.4c15.8-6.9 30.8-16.5 43.8-29.5 37.1-37.1 49.7-89.3 37.9-136.7-2.2-9-13.5-12.1-20.1-5.5l-74.4 74.4-67.9-11.3L334 98.9l74.4-74.4c6.6-6.6 3.4-17.9-5.7-20.2-47.4-11.7-99.6.9-136.6 37.9-28.5 28.5-41.9 66.1-41.2 103.6l82.1 82.1c8.1-1.9 16.5-2.9 24.7-2.9zm-103.9 82l-56.7-56.7L18.7 402.8c-25 25-25 65.5 0 90.5s65.5 25 90.5 0l123.6-123.6c-7.6-19.9-9.9-41.6-5-62.7zM64 472c-13.2 0-24-10.8-24-24 0-13.3 10.7-24 24-24s24 10.7 24 24c0 13.2-10.7 24-24 24z"></path></svg></div></div></div><h3 class="mb-3 text-xl font-medium text-foreground/90">Open-Source and Model-Agnostic</h3><p class="leading-relaxed text-muted-foreground">Bring your own model or use local AI—no vendor lock-in. Roo Code is free, open, and adaptable to your needs.</p></div></div><div class="group relative  lg:translate-y-12" style="opacity:0;transform:translateY(20px)"><div class="absolute -inset-px rounded-2xl bg-gradient-to-r from-blue-500/30 via-cyan-500/30 to-purple-500/30 opacity-0 blur-sm transition-opacity duration-500 group-hover:opacity-100"></div><div class="relative h-full rounded-2xl border border-border/50 bg-background/30 p-8 backdrop-blur-xl transition-colors duration-300 hover:border-border"><div class="mb-5 inline-flex items-center justify-center rounded-xl bg-gradient-to-r from-blue-500/5 to-cyan-500/5 p-2.5"><div class="rounded-lg bg-gradient-to-r from-blue-500/80 to-cyan-500/80 p-2.5"><div class="text-foreground/90"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 640 512" class="h-6 w-6" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M257.981 272.971L63.638 467.314c-9.373 9.373-24.569 9.373-33.941 0L7.029 444.647c-9.357-9.357-9.375-24.522-.04-33.901L161.011 256 6.99 101.255c-9.335-9.379-9.317-24.544.04-33.901l22.667-22.667c9.373-9.373 24.569-9.373 33.941 0L257.981 239.03c9.373 9.372 9.373 24.568 0 33.941zM640 456v-32c0-13.255-10.745-24-24-24H312c-13.255 0-24 10.745-24 24v32c0 13.255 10.745 24 24 24h304c13.255 0 24-10.745 24-24z"></path></svg></div></div></div><h3 class="mb-3 text-xl font-medium text-foreground/90">Guarded Command Execution</h3><p class="leading-relaxed text-muted-foreground">Approve or deny commands as needed. Roo Code automates your dev workflow while keeping oversight firmly in your hands.</p></div></div><div class="group relative  " style="opacity:0;transform:translateY(20px)"><div class="absolute -inset-px rounded-2xl bg-gradient-to-r from-blue-500/30 via-cyan-500/30 to-purple-500/30 opacity-0 blur-sm transition-opacity duration-500 group-hover:opacity-100"></div><div class="relative h-full rounded-2xl border border-border/50 bg-background/30 p-8 backdrop-blur-xl transition-colors duration-300 hover:border-border"><div class="mb-5 inline-flex items-center justify-center rounded-xl bg-gradient-to-r from-blue-500/5 to-cyan-500/5 p-2.5"><div class="rounded-lg bg-gradient-to-r from-blue-500/80 to-cyan-500/80 p-2.5"><div class="text-foreground/90"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" class="h-6 w-6" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M519.442 288.651c-41.519 0-59.5 31.593-82.058 31.593C377.409 320.244 432 144 432 144s-196.288 80-196.288-3.297c0-35.827 36.288-46.25 36.288-85.985C272 19.216 243.885 0 210.539 0c-34.654 0-66.366 18.891-66.366 56.346 0 41.364 31.711 59.277 31.711 81.75C175.885 207.719 0 166.758 0 166.758v333.237s178.635 41.047 178.635-28.662c0-22.473-40-40.107-40-81.471 0-37.456 29.25-56.346 63.577-56.346 33.673 0 61.788 19.216 61.788 54.717 0 39.735-36.288 50.158-36.288 85.985 0 60.803 129.675 25.73 181.23 25.73 0 0-34.725-120.101 25.827-120.101 35.962 0 46.423 36.152 86.308 36.152C556.712 416 576 387.99 576 354.443c0-34.199-18.962-65.792-56.558-65.792z"></path></svg></div></div></div><h3 class="mb-3 text-xl font-medium text-foreground/90">Fully Customizable</h3><p class="leading-relaxed text-muted-foreground">Create or tweak modes, define usage rules, and shape Roo Code’s behavior precisely—your code, your way.</p></div></div><div class="group relative  lg:translate-y-12" style="opacity:0;transform:translateY(20px)"><div class="absolute -inset-px rounded-2xl bg-gradient-to-r from-blue-500/30 via-cyan-500/30 to-purple-500/30 opacity-0 blur-sm transition-opacity duration-500 group-hover:opacity-100"></div><div class="relative h-full rounded-2xl border border-border/50 bg-background/30 p-8 backdrop-blur-xl transition-colors duration-300 hover:border-border"><div class="mb-5 inline-flex items-center justify-center rounded-xl bg-gradient-to-r from-blue-500/5 to-cyan-500/5 p-2.5"><div class="rounded-lg bg-gradient-to-r from-blue-500/80 to-cyan-500/80 p-2.5"><div class="text-foreground/90"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 496 512" class="h-6 w-6" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M336.5 160C322 70.7 287.8 8 248 8s-74 62.7-88.5 152h177zM152 256c0 22.2 1.2 43.5 3.3 64h185.3c2.1-20.5 3.3-41.8 3.3-64s-1.2-43.5-3.3-64H155.3c-2.1 20.5-3.3 41.8-3.3 64zm324.7-96c-28.6-67.9-86.5-120.4-158-141.6 24.4 33.8 41.2 84.7 50 141.6h108zM177.2 18.4C105.8 39.6 47.8 92.1 19.3 160h108c8.7-56.9 25.5-107.8 49.9-141.6zM487.4 192H372.7c2.1 21 3.3 42.5 3.3 64s-1.2 43-3.3 64h114.6c5.5-20.5 8.6-41.8 8.6-64s-3.1-43.5-8.5-64zM120 256c0-21.5 1.2-43 3.3-64H8.6C3.2 212.5 0 233.8 0 256s3.2 43.5 8.6 64h114.6c-2-21-3.2-42.5-3.2-64zm39.5 96c14.5 89.3 48.7 152 88.5 152s74-62.7 88.5-152h-177zm159.3 141.6c71.4-21.2 129.4-73.7 158-141.6h-108c-8.8 56.9-25.6 107.8-50 141.6zM19.3 352c28.6 67.9 86.5 120.4 158 141.6-24.4-33.8-41.2-84.7-50-141.6h-108z"></path></svg></div></div></div><h3 class="mb-3 text-xl font-medium text-foreground/90">Automated Browser Actions</h3><p class="leading-relaxed text-muted-foreground">Seamlessly test and verify your web app directly from VS Code—Roo Code can open a browser, run checks, and more.</p></div></div></div></div></div></section></div><div id="testimonials"><section class="relative overflow-hidden border-t border-border py-32"><div class="absolute inset-0" style="opacity:0"><div class="absolute inset-y-0 left-1/2 h-full w-full max-w-[1200px] -translate-x-1/2"><div class="absolute left-1/2 top-1/2 h-[800px] w-full -translate-x-1/2 -translate-y-1/2 rounded-[100%] bg-blue-500/10 blur-[120px]"></div></div></div><div class="container relative z-10 mx-auto px-4 sm:px-6 lg:px-8"><div class="mx-auto mb-24 max-w-3xl text-center"><div style="opacity:0;transform:translateY(20px)"><h2 class="bg-gradient-to-b from-foreground to-foreground/70 bg-clip-text text-4xl font-bold tracking-tight text-transparent sm:text-5xl">Empowering developers worldwide.</h2><p class="mt-6 text-lg text-muted-foreground">Join thousands of developers who are revolutionizing their workflow with AI-powered assistance.</p></div></div><div class="md:hidden"><div class="overflow-hidden px-4"><div class="flex"><div class="min-w-0 flex-[0_0_100%] px-4"><div class="relative py-8"><svg class="absolute left-0 top-0 h-8 w-8 text-blue-500/30" fill="currentColor" viewBox="0 0 32 32"><path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-***********-3.264 3.456-7.104 6.528-9.024L25.864 4z"></path></svg><blockquote class="mt-8"><p class="text-lg font-light italic leading-relaxed text-muted-foreground">&quot;<!-- -->Roo Code is an absolute game-changer! 🚀 It makes coding faster, easier, and more intuitive with its smart AI-powered suggestions, real-time debugging, and automation features. The seamless integration with VS Code is a huge plus, and the constant updates ensure it keeps getting better<!-- -->&quot;</p><footer class="mt-6"><div class="h-px w-12 bg-gradient-to-r from-blue-500/30 to-transparent"></div><p class="mt-4 font-medium text-foreground/90">Luca</p><p class="text-sm text-muted-foreground">Reviewer<!-- --> at <!-- -->VS Code Marketplace</p></footer></blockquote></div></div><div class="min-w-0 flex-[0_0_100%] px-4"><div class="relative py-8"><svg class="absolute left-0 top-0 h-8 w-8 text-blue-500/30" fill="currentColor" viewBox="0 0 32 32"><path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-***********-3.264 3.456-7.104 6.528-9.024L25.864 4z"></path></svg><blockquote class="mt-8"><p class="text-lg font-light italic leading-relaxed text-muted-foreground">&quot;<!-- -->Easily the best AI code editor. Roo Code has the best features and capabilities, along with the best development team. I swear, they&#x27;re the fastest to support new models and implement useful functionality whenever users mention it... simply amazing.<!-- -->&quot;</p><footer class="mt-6"><div class="h-px w-12 bg-gradient-to-r from-blue-500/30 to-transparent"></div><p class="mt-4 font-medium text-foreground/90">Taro Woollett-Chiba</p><p class="text-sm text-muted-foreground">AI Product Lead<!-- --> at <!-- -->Vendidit</p></footer></blockquote></div></div><div class="min-w-0 flex-[0_0_100%] px-4"><div class="relative py-8"><svg class="absolute left-0 top-0 h-8 w-8 text-blue-500/30" fill="currentColor" viewBox="0 0 32 32"><path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-***********-3.264 3.456-7.104 6.528-9.024L25.864 4z"></path></svg><blockquote class="mt-8"><p class="text-lg font-light italic leading-relaxed text-muted-foreground">&quot;<!-- -->Roo Code is one of the most inspiring projects I have seen for a long time. It shapes the way I think and deal with software development.<!-- -->&quot;</p><footer class="mt-6"><div class="h-px w-12 bg-gradient-to-r from-blue-500/30 to-transparent"></div><p class="mt-4 font-medium text-foreground/90">Can Nuri</p><p class="text-sm text-muted-foreground">Reviewer<!-- --> at <!-- -->VS Code Marketplace</p></footer></blockquote></div></div><div class="min-w-0 flex-[0_0_100%] px-4"><div class="relative py-8"><svg class="absolute left-0 top-0 h-8 w-8 text-blue-500/30" fill="currentColor" viewBox="0 0 32 32"><path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-***********-3.264 3.456-7.104 6.528-9.024L25.864 4z"></path></svg><blockquote class="mt-8"><p class="text-lg font-light italic leading-relaxed text-muted-foreground">&quot;<!-- -->I switched from Windsurf to Roo Code in January and honestly, it&#x27;s been a huge upgrade. Windsurf kept making mistakes and being dumb when I ask it for things. Roo just gets it. Projects that used to take a full day now wrap up before lunch. <!-- -->&quot;</p><footer class="mt-6"><div class="h-px w-12 bg-gradient-to-r from-blue-500/30 to-transparent"></div><p class="mt-4 font-medium text-foreground/90">Michael</p><p class="text-sm text-muted-foreground">Reviewer<!-- --> at <!-- -->VS Code Marketplace</p></footer></blockquote></div></div></div></div></div><div class="relative mx-auto hidden max-w-[1200px] md:block" style="opacity:0"><div class="relative grid grid-cols-1 gap-12 md:grid-cols-2"><div class="group relative md:translate-y-4" style="opacity:0;transform:translateY(20px)"><div class="absolute -inset-px rounded-2xl bg-gradient-to-r from-blue-500/30 via-cyan-500/30 to-purple-500/30 opacity-0 blur-sm transition-all duration-500 ease-out group-hover:opacity-100"></div><div class="relative h-full rounded-2xl border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-500 ease-out group-hover:border-border group-hover:bg-background/40"><div class="p-8"><div class="mb-6"><svg class="h-8 w-8 text-blue-500/20" fill="currentColor" viewBox="0 0 32 32"><path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-***********-3.264 3.456-7.104 6.528-9.024L25.864 4z"></path></svg></div><p class="relative mb-6 text-lg leading-relaxed text-muted-foreground">Roo Code is an absolute game-changer! 🚀 It makes coding faster, easier, and more intuitive with its smart AI-powered suggestions, real-time debugging, and automation features. The seamless integration with VS Code is a huge plus, and the constant updates ensure it keeps getting better</p><div class="relative"><div class="mb-4 h-px w-12 bg-gradient-to-r from-blue-500/50 to-transparent"></div><h3 class="font-medium text-foreground/90">Luca</h3><p class="text-sm text-muted-foreground">Reviewer<!-- --> at <!-- -->VS Code Marketplace</p></div></div></div></div><div class="group relative md:translate-y-12" style="opacity:0;transform:translateY(20px)"><div class="absolute -inset-px rounded-2xl bg-gradient-to-r from-blue-500/30 via-cyan-500/30 to-purple-500/30 opacity-0 blur-sm transition-all duration-500 ease-out group-hover:opacity-100"></div><div class="relative h-full rounded-2xl border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-500 ease-out group-hover:border-border group-hover:bg-background/40"><div class="p-8"><div class="mb-6"><svg class="h-8 w-8 text-blue-500/20" fill="currentColor" viewBox="0 0 32 32"><path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-***********-3.264 3.456-7.104 6.528-9.024L25.864 4z"></path></svg></div><p class="relative mb-6 text-lg leading-relaxed text-muted-foreground">Easily the best AI code editor. Roo Code has the best features and capabilities, along with the best development team. I swear, they&#x27;re the fastest to support new models and implement useful functionality whenever users mention it... simply amazing.</p><div class="relative"><div class="mb-4 h-px w-12 bg-gradient-to-r from-blue-500/50 to-transparent"></div><h3 class="font-medium text-foreground/90">Taro Woollett-Chiba</h3><p class="text-sm text-muted-foreground">AI Product Lead<!-- --> at <!-- -->Vendidit</p></div></div></div></div><div class="group relative md:translate-y-4" style="opacity:0;transform:translateY(20px)"><div class="absolute -inset-px rounded-2xl bg-gradient-to-r from-blue-500/30 via-cyan-500/30 to-purple-500/30 opacity-0 blur-sm transition-all duration-500 ease-out group-hover:opacity-100"></div><div class="relative h-full rounded-2xl border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-500 ease-out group-hover:border-border group-hover:bg-background/40"><div class="p-8"><div class="mb-6"><svg class="h-8 w-8 text-blue-500/20" fill="currentColor" viewBox="0 0 32 32"><path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-***********-3.264 3.456-7.104 6.528-9.024L25.864 4z"></path></svg></div><p class="relative mb-6 text-lg leading-relaxed text-muted-foreground">Roo Code is one of the most inspiring projects I have seen for a long time. It shapes the way I think and deal with software development.</p><div class="relative"><div class="mb-4 h-px w-12 bg-gradient-to-r from-blue-500/50 to-transparent"></div><h3 class="font-medium text-foreground/90">Can Nuri</h3><p class="text-sm text-muted-foreground">Reviewer<!-- --> at <!-- -->VS Code Marketplace</p></div></div></div></div><div class="group relative md:translate-y-12" style="opacity:0;transform:translateY(20px)"><div class="absolute -inset-px rounded-2xl bg-gradient-to-r from-blue-500/30 via-cyan-500/30 to-purple-500/30 opacity-0 blur-sm transition-all duration-500 ease-out group-hover:opacity-100"></div><div class="relative h-full rounded-2xl border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-500 ease-out group-hover:border-border group-hover:bg-background/40"><div class="p-8"><div class="mb-6"><svg class="h-8 w-8 text-blue-500/20" fill="currentColor" viewBox="0 0 32 32"><path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-***********-3.264 3.456-7.104 6.528-9.024L25.864 4z"></path></svg></div><p class="relative mb-6 text-lg leading-relaxed text-muted-foreground">I switched from Windsurf to Roo Code in January and honestly, it&#x27;s been a huge upgrade. Windsurf kept making mistakes and being dumb when I ask it for things. Roo just gets it. Projects that used to take a full day now wrap up before lunch. </p><div class="relative"><div class="mb-4 h-px w-12 bg-gradient-to-r from-blue-500/50 to-transparent"></div><h3 class="font-medium text-foreground/90">Michael</h3><p class="text-sm text-muted-foreground">Reviewer<!-- --> at <!-- -->VS Code Marketplace</p></div></div></div></div></div></div></div></section></div><div id="faq"><section id="faq-section" class="border-t border-border py-20"><div class="container mx-auto px-4 sm:px-6 lg:px-8"><div class="mx-auto mb-24 max-w-3xl text-center"><div style="opacity:0;transform:translateY(20px)"><h2 class="bg-gradient-to-b from-foreground to-foreground/70 bg-clip-text text-4xl font-bold tracking-tight text-transparent sm:text-5xl">Frequently Asked Questions</h2><p class="mt-6 text-lg text-muted-foreground">Everything you need to know about Roo Code and how it can transform your development workflow.</p></div></div><div class="mx-auto max-w-3xl"><div class="space-y-4"><div style="opacity:0;transform:translateY(20px)"><div class="group relative rounded-lg border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-300 hover:border-border"><button class="flex w-full items-center justify-between p-6 text-left" aria-expanded="false"><h3 class="text-lg font-medium text-foreground/90">What exactly is Roo Code?</h3><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-5 w-5 text-muted-foreground transition-transform duration-200" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button><div class="overflow-hidden transition-all duration-300 ease-in-out max-h-0"><div class="px-6 text-muted-foreground"><p>Roo Code is an open-source, AI-powered coding assistant that runs in VS Code. It goes beyond simple autocompletion by reading and writing across multiple files, executing commands, and adapting to your workflow—like having a whole dev team right inside your editor.</p></div></div></div></div><div style="opacity:0;transform:translateY(20px)"><div class="group relative rounded-lg border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-300 hover:border-border"><button class="flex w-full items-center justify-between p-6 text-left" aria-expanded="false"><h3 class="text-lg font-medium text-foreground/90">How does Roo Code differ from Copilot, Cursor, or Windsurf?</h3><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-5 w-5 text-muted-foreground transition-transform duration-200" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button><div class="overflow-hidden transition-all duration-300 ease-in-out max-h-0"><div class="px-6 text-muted-foreground"><p>Open &amp; Customizable: Roo Code is open-source and allows you to integrate any AI model (OpenAI, Anthropic, local LLMs, etc.). Multi-File Edits: It can read, refactor, and update multiple files at once for more holistic changes. Agentic Abilities: Roo Code can run tests, open a browser, or do deeper tasks than a typical AI autocomplete. Permission-Based: You control and approve any file changes or command executions.</p></div></div></div></div><div style="opacity:0;transform:translateY(20px)"><div class="group relative rounded-lg border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-300 hover:border-border"><button class="flex w-full items-center justify-between p-6 text-left" aria-expanded="false"><h3 class="text-lg font-medium text-foreground/90">Is Roo Code really free?</h3><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-5 w-5 text-muted-foreground transition-transform duration-200" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button><div class="overflow-hidden transition-all duration-300 ease-in-out max-h-0"><div class="px-6 text-muted-foreground"><p>Yes! Roo Code is completely free and open-source. You&#x27;ll only pay for the AI model usage if you use a paid API (like OpenAI). If you choose free or self-hosted models, there&#x27;s no cost at all.</p></div></div></div></div><div style="opacity:0;transform:translateY(20px)"><div class="group relative rounded-lg border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-300 hover:border-border"><button class="flex w-full items-center justify-between p-6 text-left" aria-expanded="false"><h3 class="text-lg font-medium text-foreground/90">Will my code stay private?</h3><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-5 w-5 text-muted-foreground transition-transform duration-200" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button><div class="overflow-hidden transition-all duration-300 ease-in-out max-h-0"><div class="px-6 text-muted-foreground"><p>Yes. Because Roo Code is an extension in your local VS Code, your code never leaves your machine unless you connect to an external AI API. Even then, you control exactly what is sent to the AI model. You can use tools like .rooignore to exclude sensitive files, and you can also run Roo Code with offline/local models for full privacy.</p></div></div></div></div><div style="opacity:0;transform:translateY(20px)"><div class="group relative rounded-lg border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-300 hover:border-border"><button class="flex w-full items-center justify-between p-6 text-left" aria-expanded="false"><h3 class="text-lg font-medium text-foreground/90">Which AI models does Roo Code support?</h3><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-5 w-5 text-muted-foreground transition-transform duration-200" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button><div class="overflow-hidden transition-all duration-300 ease-in-out max-h-0"><div class="px-6 text-muted-foreground"><p>Roo Code is model-agnostic. It works with: OpenAI models (GPT-3.5, GPT-4, etc.), Anthropic Claude, Local LLMs (through APIs or special plugins), Any other API that follows Roo Code&#x27;s Model Context Protocol (MCP).</p></div></div></div></div><div style="opacity:0;transform:translateY(20px)"><div class="group relative rounded-lg border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-300 hover:border-border"><button class="flex w-full items-center justify-between p-6 text-left" aria-expanded="false"><h3 class="text-lg font-medium text-foreground/90">Does Roo Code support my programming language?</h3><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-5 w-5 text-muted-foreground transition-transform duration-200" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button><div class="overflow-hidden transition-all duration-300 ease-in-out max-h-0"><div class="px-6 text-muted-foreground"><p>Likely yes! Roo Code supports a wide range of languages—Python, Java, C#, JavaScript/TypeScript, Go, Rust, etc. Since it leverages the AI model&#x27;s understanding, new or lesser-known languages may also work, depending on model support.</p></div></div></div></div><div style="opacity:0;transform:translateY(20px)"><div class="group relative rounded-lg border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-300 hover:border-border"><button class="flex w-full items-center justify-between p-6 text-left" aria-expanded="false"><h3 class="text-lg font-medium text-foreground/90">How do I install and get started?</h3><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-5 w-5 text-muted-foreground transition-transform duration-200" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button><div class="overflow-hidden transition-all duration-300 ease-in-out max-h-0"><div class="px-6 text-muted-foreground"><p>Install Roo Code from the VS Code Marketplace (or GitHub). Add your AI keys (OpenAI, Anthropic, or other) in the extension settings. Open the Roo panel (the rocket icon) in VS Code, and start typing commands in plain English!</p></div></div></div></div><div style="opacity:0;transform:translateY(20px)"><div class="group relative rounded-lg border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-300 hover:border-border"><button class="flex w-full items-center justify-between p-6 text-left" aria-expanded="false"><h3 class="text-lg font-medium text-foreground/90">Can it handle large, enterprise-scale projects?</h3><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-5 w-5 text-muted-foreground transition-transform duration-200" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button><div class="overflow-hidden transition-all duration-300 ease-in-out max-h-0"><div class="px-6 text-muted-foreground"><p>Absolutely. Roo Code uses efficient strategies (like partial-file analysis, summarization, or user-specified context) to handle large codebases. Enterprises especially appreciate the on-prem or self-hosted model option for compliance and security needs.</p></div></div></div></div><div style="opacity:0;transform:translateY(20px)"><div class="group relative rounded-lg border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-300 hover:border-border"><button class="flex w-full items-center justify-between p-6 text-left" aria-expanded="false"><h3 class="text-lg font-medium text-foreground/90">Is it safe for enterprise use?</h3><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-5 w-5 text-muted-foreground transition-transform duration-200" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button><div class="overflow-hidden transition-all duration-300 ease-in-out max-h-0"><div class="px-6 text-muted-foreground"><p>Yes. Roo Code was designed with enterprise in mind: Self-host AI models or choose your own provider. Permission gating on file writes and commands. Auditable: The entire code is open-source, so you know exactly how it operates.</p></div></div></div></div><div style="opacity:0;transform:translateY(20px)"><div class="group relative rounded-lg border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-300 hover:border-border"><button class="flex w-full items-center justify-between p-6 text-left" aria-expanded="false"><h3 class="text-lg font-medium text-foreground/90">Can Roo Code run commands and tests automatically?</h3><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-5 w-5 text-muted-foreground transition-transform duration-200" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button><div class="overflow-hidden transition-all duration-300 ease-in-out max-h-0"><div class="px-6 text-muted-foreground"><p>Yes! One of Roo Code&#x27;s superpowers is command execution (optional and fully permission-based). It can: Run npm install or any terminal command you grant permission for. Execute your test suites. Open a web browser for integration tests.</p></div></div></div></div><div style="opacity:0;transform:translateY(20px)"><div class="group relative rounded-lg border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-300 hover:border-border"><button class="flex w-full items-center justify-between p-6 text-left" aria-expanded="false"><h3 class="text-lg font-medium text-foreground/90">What if I just want a casual coding &#x27;vibe&#x27;?</h3><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-5 w-5 text-muted-foreground transition-transform duration-200" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button><div class="overflow-hidden transition-all duration-300 ease-in-out max-h-0"><div class="px-6 text-muted-foreground"><p>Roo Code shines for both serious enterprise development and casual &quot;vibe coding.&quot; You can ask it to quickly prototype ideas, refactor on the fly, or provide design suggestions—without a rigid, step-by-step process.</p></div></div></div></div><div style="opacity:0;transform:translateY(20px)"><div class="group relative rounded-lg border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-300 hover:border-border"><button class="flex w-full items-center justify-between p-6 text-left" aria-expanded="false"><h3 class="text-lg font-medium text-foreground/90">Can I contribute to Roo Code?</h3><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-5 w-5 text-muted-foreground transition-transform duration-200" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button><div class="overflow-hidden transition-all duration-300 ease-in-out max-h-0"><div class="px-6 text-muted-foreground"><p>Yes, please do! Roo Code is open-source on GitHub. Submit issues, suggest features, or open a pull request. There&#x27;s also an active community on Discord and Reddit if you want to share feedback or help others.</p></div></div></div></div><div style="opacity:0;transform:translateY(20px)"><div class="group relative rounded-lg border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-300 hover:border-border"><button class="flex w-full items-center justify-between p-6 text-left" aria-expanded="false"><h3 class="text-lg font-medium text-foreground/90">Where can I learn more or get help?</h3><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-5 w-5 text-muted-foreground transition-transform duration-200" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button><div class="overflow-hidden transition-all duration-300 ease-in-out max-h-0"><div class="px-6 text-muted-foreground"><p>Check out: Official Documentation for setup and advanced guides. Discord &amp; Reddit channels for community support. YouTube tutorials and blog posts from fellow developers showcasing real-world usage.</p></div></div></div></div></div></div></div></section></div><section class="relative overflow-hidden border-t border-border py-16 sm:py-24 lg:py-32"><div class="absolute inset-x-0 top-1/2 -translate-y-1/2" style="opacity:0"><div class="relative mx-auto max-w-[1200px]"><div class="absolute left-1/2 top-1/2 h-[500px] w-[700px] -translate-x-1/2 -translate-y-1/2 rounded-[100%] bg-blue-500/10 blur-[120px]"></div></div></div><div class="container relative z-10 mx-auto px-4 sm:px-6 lg:px-8"><div class="mx-auto max-w-3xl text-center"><h2 class="text-center text-xl font-semibold uppercase tracking-wider text-muted-foreground sm:text-2xl">Install Roo Code — Open &amp; Flexible</h2><p class="mt-4 text-center text-base text-muted-foreground sm:mt-6 sm:text-lg">Roo Code is open-source, model-agnostic, and developer-focused. Install from the VS Code Marketplace or the CLI in minutes, then bring your own AI model.</p><div class="mt-10 flex flex-col items-center justify-center gap-6"><a target="_blank" class="group relative inline-flex w-full items-center justify-center gap-2 rounded-xl border border-border/50 bg-background/30 px-4 py-3 text-base backdrop-blur-xl transition-all duration-300 hover:border-border hover:bg-background/40 sm:w-auto sm:gap-3 sm:px-6 sm:py-4 sm:text-lg md:text-2xl" href="https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline"><div class="absolute -inset-px rounded-xl bg-gradient-to-r from-blue-500/30 via-cyan-500/30 to-purple-500/30 opacity-0 blur-sm transition-opacity duration-500 group-hover:opacity-100"></div><div class="relative flex items-center gap-2 sm:gap-3"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 16 16" class="h-5 w-5 text-blue-400 sm:h-6 sm:w-6 md:h-8 md:w-8" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M10.8634 13.9195C10.6568 14.0195 10.4233 14.0246 10.2185 13.9444C10.1162 13.9044 10.021 13.843 9.93997 13.7614L4.81616 9.06268L2.58433 10.7656C2.37657 10.9241 2.08597 10.9111 1.89301 10.7347L1.17719 10.0802C0.941168 9.86437 0.940898 9.49112 1.17661 9.27496L3.11213 7.5L1.17661 5.72504C0.940898 5.50888 0.941168 5.13563 1.17719 4.91982L1.89301 4.2653C2.08597 4.08887 2.37657 4.07588 2.58433 4.2344L4.81616 5.93732L9.93997 1.23855C9.97037 1.20797 10.0028 1.18023 10.0368 1.15538C10.2748 0.981429 10.5922 0.949298 10.8634 1.08048L13.5399 2.37507C13.8212 2.5111 14 2.79721 14 3.11109V8H10.752V4.53356L6.86419 7.5L10.752 10.4664V8H14V11.8889C14 12.2028 13.8211 12.4889 13.5399 12.625L10.8634 13.9195Z"></path></svg><span class="flex flex-wrap items-center gap-1 sm:gap-2 md:gap-3"><span class="text-foreground/90">VSCode Marketplace</span><span class="hidden font-black text-muted-foreground sm:inline">·</span><span class="text-muted-foreground">702.6k<!-- --> Downloads</span></span></div></a><div class="group relative w-full max-w-xl"><div class="absolute -inset-px rounded-xl bg-gradient-to-r from-blue-500/30 via-cyan-500/30 to-purple-500/30 opacity-0 blur-sm transition-opacity duration-500 group-hover:opacity-100"></div><div class="relative overflow-hidden rounded-xl border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-500 ease-out group-hover:border-border group-hover:bg-background/40"><div class="border-b border-border/50 px-3 py-2 sm:px-4"><div class="text-sm text-muted-foreground">Install via CLI</div></div><div class="overflow-x-auto"><pre class="p-3 sm:p-4"><code class="whitespace-pre-wrap break-all text-sm text-foreground/90 sm:break-normal">code --install-extension RooVeterinaryInc.roo-cline</code></pre></div></div></div></div></div></div></section></main><footer class="border-t border-border bg-background"><div class="mx-auto max-w-7xl px-6 pb-6 pt-12 md:pb-8 md:pt-16 lg:px-8"><div class="xl:grid xl:grid-cols-3 xl:gap-8"><div class="space-y-8"><div class="flex items-center"><img alt="Roo Code Logo" loading="lazy" width="120" height="40" decoding="async" data-nimg="1" class="h-6 w-auto" style="color:transparent" src="/Roo-Code-Logo-Horiz-white.svg"/></div><p class="max-w-md text-sm leading-6 text-muted-foreground md:pr-16 lg:pr-32">Empowering developers to build better software faster with AI-powered tools and insights.</p><div class="flex space-x-4"><a href="https://github.com/RooCodeInc/Roo-Code" target="_blank" rel="noopener noreferrer" class="text-muted-foreground transition-colors hover:text-foreground"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 496 512" class="h-6 w-6" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6zm-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3zm44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9zM244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8zM97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1zm-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7zm32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1zm-11.4-14.7c-1.6 1-1.6 3.6 0 5.9 1.6 2.3 4.3 3.3 5.6 2.3 1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2z"></path></svg><span class="sr-only">GitHub</span></a><a href="https://discord.gg/roocode" target="_blank" rel="noopener noreferrer" class="text-muted-foreground transition-colors hover:text-foreground"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 640 512" class="h-6 w-6" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M524.531,69.836a1.5,1.5,0,0,0-.764-.7A485.065,485.065,0,0,0,404.081,32.03a1.816,1.816,0,0,0-1.923.91,337.461,337.461,0,0,0-14.9,30.6,447.848,447.848,0,0,0-134.426,0,309.541,309.541,0,0,0-15.135-30.6,1.89,1.89,0,0,0-1.924-.91A483.689,483.689,0,0,0,116.085,69.137a1.712,1.712,0,0,0-.788.676C39.068,183.651,18.186,294.69,28.43,404.354a2.016,2.016,0,0,0,.765,1.375A487.666,487.666,0,0,0,176.02,479.918a1.9,1.9,0,0,0,2.063-.676A348.2,348.2,0,0,0,208.12,430.4a1.86,1.86,0,0,0-1.019-2.588,321.173,321.173,0,0,1-45.868-21.853,1.885,1.885,0,0,1-.185-3.126c3.082-2.309,6.166-4.711,9.109-7.137a1.819,1.819,0,0,1,1.9-.256c96.229,43.917,200.41,43.917,295.5,0a1.812,1.812,0,0,1,1.924.233c2.944,2.426,6.027,4.851,9.132,7.16a1.884,1.884,0,0,1-.162,3.126,301.407,301.407,0,0,1-45.89,21.83,1.875,1.875,0,0,0-1,2.611,391.055,391.055,0,0,0,30.014,48.815,1.864,1.864,0,0,0,2.063.7A486.048,486.048,0,0,0,610.7,405.729a1.882,1.882,0,0,0,.765-1.352C623.729,277.594,590.933,167.465,524.531,69.836ZM222.491,337.58c-28.972,0-52.844-26.587-52.844-59.239S193.056,219.1,222.491,219.1c29.665,0,53.306,26.82,52.843,59.239C275.334,310.993,251.924,337.58,222.491,337.58Zm195.38,0c-28.971,0-52.843-26.587-52.843-59.239S388.437,219.1,417.871,219.1c29.667,0,53.307,26.82,52.844,59.239C470.715,310.993,447.538,337.58,417.871,337.58Z"></path></svg><span class="sr-only">Discord</span></a><a href="https://reddit.com/r/RooCode" target="_blank" rel="noopener noreferrer" class="text-muted-foreground transition-colors hover:text-foreground"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" class="h-6 w-6" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M0 256C0 114.6 114.6 0 256 0S512 114.6 512 256s-114.6 256-256 256L37.1 512c-13.7 0-20.5-16.5-10.9-26.2L75 437C28.7 390.7 0 326.7 0 256zM349.6 153.6c23.6 0 42.7-19.1 42.7-42.7s-19.1-42.7-42.7-42.7c-20.6 0-37.8 14.6-41.8 34c-34.5 3.7-61.4 33-61.4 68.4l0 .2c-37.5 1.6-71.8 12.3-99 29.1c-10.1-7.8-22.8-12.5-36.5-12.5c-33 0-59.8 26.8-59.8 59.8c0 24 14.1 44.6 34.4 54.1c2 69.4 77.6 125.2 170.6 125.2s168.7-55.9 170.6-125.3c20.2-9.6 34.1-30.2 34.1-54c0-33-26.8-59.8-59.8-59.8c-13.7 0-26.3 4.6-36.4 12.4c-27.4-17-62.1-27.7-100-29.1l0-.2c0-25.4 18.9-46.5 43.4-49.9l0 0c4.4 18.8 21.3 32.8 41.5 32.8zM177.1 246.9c16.7 0 29.5 17.6 28.5 39.3s-13.5 29.6-30.3 29.6s-31.4-8.8-30.4-30.5s15.4-38.3 32.1-38.3zm190.1 38.3c1 21.7-13.7 30.5-30.4 30.5s-29.3-7.9-30.3-29.6c-1-21.7 11.8-39.3 28.5-39.3s31.2 16.6 32.1 38.3zm-48.1 56.7c-10.3 24.6-34.6 41.9-63 41.9s-52.7-17.3-63-41.9c-1.2-2.9 .8-6.2 3.9-6.5c18.4-1.9 38.3-2.9 59.1-2.9s40.7 1 59.1 2.9c3.1 .3 5.1 3.6 3.9 6.5z"></path></svg><span class="sr-only">Reddit</span></a><a href="https://x.com/roo_code" target="_blank" rel="noopener noreferrer" class="text-muted-foreground transition-colors hover:text-foreground"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" class="h-6 w-6" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M389.2 48h70.6L305.6 224.2 487 464H345L233.7 318.6 106.5 464H35.8L200.7 275.5 26.8 48H172.4L272.9 180.9 389.2 48zM364.4 421.8h39.1L151.1 88h-42L364.4 421.8z"></path></svg><span class="sr-only">X</span></a><a href="https://www.linkedin.com/company/roo-code" target="_blank" rel="noopener noreferrer" class="text-muted-foreground transition-colors hover:text-foreground"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 448 512" class="h-6 w-6" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M416 32H31.9C14.3 32 0 46.5 0 64.3v383.4C0 465.5 14.3 480 31.9 480H416c17.6 0 32-14.5 32-32.3V64.3c0-17.8-14.4-32.3-32-32.3zM135.4 416H69V202.2h66.5V416zm-33.2-243c-21.3 0-38.5-17.3-38.5-38.5S80.9 96 102.2 96c21.2 0 38.5 17.3 38.5 38.5 0 21.3-17.2 38.5-38.5 38.5zm282.1 243h-66.4V312c0-24.8-.5-56.7-34.5-56.7-34.6 0-39.9 27-39.9 54.9V416h-66.4V202.2h63.7v29.2h.9c8.9-16.8 30.6-34.5 62.9-34.5 67.2 0 79.7 44.3 79.7 101.9V416z"></path></svg><span class="sr-only">LinkedIn</span></a><a href="https://bsky.app/profile/roocode.bsky.social" target="_blank" rel="noopener noreferrer" class="text-muted-foreground transition-colors hover:text-foreground"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" class="h-6 w-6" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M407.8 294.7c-3.3-.4-6.7-.8-10-1.3c3.4 .4 6.7 .9 10 1.3zM288 227.1C261.9 176.4 190.9 81.9 124.9 35.3C61.6-9.4 37.5-1.7 21.6 5.5C3.3 13.8 0 41.9 0 58.4S9.1 194 15 213.9c19.5 65.7 89.1 87.9 153.2 80.7c3.3-.5 6.6-.9 10-1.4c-3.3 .5-6.6 1-10 1.4C74.3 308.6-9.1 342.8 100.3 464.5C220.6 589.1 265.1 437.8 288 361.1c22.9 76.7 49.2 222.5 185.6 103.4c102.4-103.4 28.1-156-65.8-169.9c-3.3-.4-6.7-.8-10-1.3c3.4 .4 6.7 .9 10 1.3c64.1 7.1 133.6-15.1 153.2-80.7C566.9 194 576 75 576 58.4s-3.3-44.7-21.6-52.9c-15.8-7.1-40-14.9-103.2 29.8C385.1 81.9 314.1 176.4 288 227.1z"></path></svg><span class="sr-only">Bluesky</span></a><a href="https://www.tiktok.com/@roo.code" target="_blank" rel="noopener noreferrer" class="text-muted-foreground transition-colors hover:text-foreground"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 448 512" class="h-6 w-6" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M448,209.91a210.06,210.06,0,0,1-122.77-39.25V349.38A162.55,162.55,0,1,1,185,188.31V278.2a74.62,74.62,0,1,0,52.23,71.18V0l88,0a121.18,121.18,0,0,0,1.86,22.17h0A122.18,122.18,0,0,0,381,102.39a121.43,121.43,0,0,0,67,20.14Z"></path></svg><span class="sr-only">TikTok</span></a><a href="https://www.youtube.com/@RooCodeYT" target="_blank" rel="noopener noreferrer" class="text-muted-foreground transition-colors hover:text-foreground"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" class="h-6 w-6" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M549.655 124.083c-6.281-23.65-24.787-42.276-48.284-48.597C458.781 64 288 64 288 64S117.22 64 74.629 75.486c-23.497 6.322-42.003 24.947-48.284 48.597-11.412 42.867-11.412 132.305-11.412 132.305s0 89.438 11.412 132.305c6.281 23.65 24.787 41.5 48.284 47.821C117.22 448 288 448 288 448s170.78 0 213.371-11.486c23.497-6.321 42.003-24.171 48.284-47.821 11.412-42.867 11.412-132.305 11.412-132.305s0-89.438-11.412-132.305zm-317.51 213.508V175.185l142.739 81.205-142.739 81.201z"></path></svg><span class="sr-only">YouTube</span></a></div></div><div class="mt-16 grid grid-cols-2 gap-8 xl:col-span-2 xl:mt-0"><div class="md:grid md:grid-cols-2 md:gap-8"><div><h3 class="text-sm font-semibold uppercase leading-6 text-foreground">Product</h3><ul class="mt-6 space-y-4"><li><button class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Features</button></li><li><a class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground" href="/enterprise">Enterprise</a></li><li><a href="https://trust.roocode.com" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Security</a></li><li><button class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Testimonials</button></li><li><a href="https://docs.roocode.com/community" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Integrations</a></li><li><a href="https://github.com/RooCodeInc/Roo-Code/blob/main/CHANGELOG.md" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Changelog</a></li></ul></div><div class="mt-10 md:mt-0"><h3 class="text-sm font-semibold uppercase leading-6 text-foreground">Resources</h3><ul class="mt-6 space-y-4"><li><a href="https://docs.roocode.com" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Documentation</a></li><li><a href="https://docs.roocode.com/tutorial-videos" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Tutorials</a></li><li><a href="https://github.com/RooCodeInc/Roo-Code/discussions" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Community</a></li><li><a href="https://discord.gg/roocode" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Discord</a></li><li><a href="https://reddit.com/r/RooCode" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Reddit</a></li></ul></div></div><div class="md:grid md:grid-cols-2 md:gap-8"><div><h3 class="text-sm font-semibold uppercase leading-6 text-foreground">Support</h3><ul class="mt-6 space-y-4"><li><a href="https://github.com/RooCodeInc/Roo-Code/issues" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Issues</a></li><li><a href="https://github.com/RooCodeInc/Roo-Code/discussions/categories/feature-requests" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Feature Requests</a></li><li><button class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">FAQ</button></li></ul></div><div class="mt-10 md:mt-0"><h3 class="text-sm font-semibold uppercase leading-6 text-foreground">Company</h3><ul class="mt-6 space-y-4"><li><a href="mailto:<EMAIL>" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Contact</a></li><li><a href="https://careers.roocode.com" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Careers</a></li><li><a class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground" href="/terms">Terms of Service</a></li><li><div class="relative z-10"><button class="flex items-center text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground" aria-expanded="false" aria-haspopup="true"><span>Privacy <span class="max-[320px]:hidden">Policy</span></span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down ml-1 h-4 w-4 transition-transform" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></div></li></ul></div></div></div></div><div class="mt-16 flex border-t border-border pt-8 sm:mt-20 lg:mt-24"><p class="mx-auto text-sm leading-5 text-muted-foreground">© <!-- -->2025<!-- --> Roo Code. All rights reserved.</p></div></div></footer></div><script src="/_next/static/chunks/webpack-c8e217c742f0746a.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[80983,[\"858\",\"static/chunks/aba955ec-9833060e0c799abe.js\",\"840\",\"static/chunks/e5c963a3-f3448fec3e69041a.js\",\"777\",\"static/chunks/88e8e2ac-7a1619a6e98006b4.js\",\"798\",\"static/chunks/2ae967de-afd0847aa04941df.js\",\"180\",\"static/chunks/dd0ec533-85265a862baf3739.js\",\"872\",\"static/chunks/5d6f4545-30a8b6f9fd887cd0.js\",\"604\",\"static/chunks/604-37da8378c60f8591.js\",\"156\",\"static/chunks/156-01d88aff08204544.js\",\"657\",\"static/chunks/657-9a922aaadb9c4bb2.js\",\"954\",\"static/chunks/954-09ef0ac74eac9fe3.js\",\"554\",\"static/chunks/554-3f07505b777a310d.js\",\"177\",\"static/chunks/app/layout-70c3bb938d5edbac.js\"],\"\"]\n3:I[39183,[\"858\",\"static/chunks/aba955ec-9833060e0c799abe.js\",\"840\",\"static/chunks/e5c963a3-f3448fec3e69041a.js\",\"777\",\"static/chunks/88e8e2ac-7a1619a6e98006b4.js\",\"798\",\"static/chunks/2ae967de-afd0847aa04941df.js\",\"180\",\"static/chunks/dd0ec533-85265a862baf3739.js\",\"872\",\"static/chunks/5d6f4545-30a8b6f9fd887cd0.js\",\"604\",\"static/chunks/604-37da8378c60f8591.js\",\"156\",\"static/chunks/156-01d88aff08204544.js\",\"657\",\"static/chunks/657-9a922aaadb9c4bb2.js\",\"954\",\"static/chunks/954-09ef0ac74eac9fe3.js\",\"554\",\"static/chunks/554-3f07505b777a310d.js\",\"177\",\"static/chunks/app/layout-70c3bb938d5edbac.js\"],\"Providers\"]\n6:I[6445,[],\"OutletBoundary\"]\n9:I[6445,[],\"ViewportBoundary\"]\nb:I[6445,[],\"MetadataBoundary\"]\nd:I[38826,[],\"\"]\n:HL[\"/_next/static/css/c16eb50558afbc00.css\",\"style\"]\n0:{\"P\":null,\"b\":\"Hrz9dGjjQ4zbSgAqNHnYG\",\"p\":\"\",\"c\":[\"\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"__PAGE__\",{}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/c16eb50558afbc00.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"suppressHydrationWarning\":true,\"children\":[[\"$\",\"head\",null,{\"children\":[\"$\",\"link\",null,{\"rel\":\"stylesheet\",\"type\":\"text/css\",\"href\":\"https://cdn.jsdelivr.net/gh/devicons/devicon@latest/devicon.min.css\"}]}],[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[[\"$\",\"$L2\",nul"])</script><script>self.__next_f.push([1,"l,{\"src\":\"https://www.googletagmanager.com/gtag/js?id=AW-17391954825\",\"strategy\":\"afterInteractive\"}],[\"$\",\"$L2\",null,{\"id\":\"google-analytics\",\"strategy\":\"afterInteractive\",\"children\":\"\\n\\t\\t\\t\\t\\t\\twindow.dataLayer = window.dataLayer || [];\\n\\t\\t\\t\\t\\t\\tfunction gtag(){dataLayer.push(arguments);}\\n\\t\\t\\t\\t\\t\\tgtag('js', new Date());\\n\\t\\t\\t\\t\\t\\tgtag('config', 'AW-17391954825');\\n\\t\\t\\t\\t\\t\"}],[\"$\",\"div\",null,{\"itemScope\":true,\"itemType\":\"https://schema.org/WebSite\",\"children\":[[\"$\",\"link\",null,{\"itemProp\":\"url\",\"href\":\"https://roocode.com\"}],[\"$\",\"meta\",null,{\"itemProp\":\"name\",\"content\":\"Roo Code\"}]]}],[\"$\",\"$L3\",null,{\"children\":\"$L4\"}]]}]]}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[\"$L5\",\"$undefined\",null,[\"$\",\"$L6\",null,{\"children\":[\"$L7\",\"$L8\",null]}]]}],{},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"XGRIq6PYSCy8M8OUiBZCV\",{\"children\":[[\"$\",\"$L9\",null,{\"children\":\"$La\"}],null]}],[\"$\",\"$Lb\",null,{\"children\":\"$Lc\"}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$d\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"a:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\n7:null\n"])</script><script>self.__next_f.push([1,"8:null\nc:[[\"$\",\"title\",\"0\",{\"children\":\"Roo Code – Your AI-Powered Dev Team in VS Code\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Roo Code puts an entire AI dev team right in your editor, outpacing closed tools with deep project-wide context, multi-step agentic coding, and unmatched developer-centric flexibility.\"}],[\"$\",\"link\",\"2\",{\"rel\":\"canonical\",\"href\":\"https://roocode.com\"}],[\"$\",\"link\",\"3\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\"}],[\"$\",\"link\",\"4\",{\"rel\":\"icon\",\"href\":\"/favicon-16x16.png\",\"sizes\":\"16x16\",\"type\":\"image/png\"}],[\"$\",\"link\",\"5\",{\"rel\":\"icon\",\"href\":\"/favicon-32x32.png\",\"sizes\":\"32x32\",\"type\":\"image/png\"}],[\"$\",\"link\",\"6\",{\"rel\":\"apple-touch-icon\",\"href\":\"/apple-touch-icon.png\"}],[\"$\",\"link\",\"7\",{\"rel\":\"android-chrome-192x192\",\"href\":\"/android-chrome-192x192.png\",\"sizes\":\"192x192\",\"type\":\"image/png\"}],[\"$\",\"link\",\"8\",{\"rel\":\"android-chrome-512x512\",\"href\":\"/android-chrome-512x512.png\",\"sizes\":\"512x512\",\"type\":\"image/png\"}]]\n"])</script><script>self.__next_f.push([1,"e:I[33001,[\"858\",\"static/chunks/aba955ec-9833060e0c799abe.js\",\"155\",\"static/chunks/6b00a407-f2a69e7fba2ff1d5.js\",\"604\",\"static/chunks/604-37da8378c60f8591.js\",\"156\",\"static/chunks/156-01d88aff08204544.js\",\"944\",\"static/chunks/944-7e8cad43dbfe6c27.js\",\"957\",\"static/chunks/957-6b88dac7433a3763.js\",\"974\",\"static/chunks/app/page-fbf1f0c2d5ddc3ca.js\"],\"AnimatedBackground\"]\nf:I[21925,[\"858\",\"static/chunks/aba955ec-9833060e0c799abe.js\",\"155\",\"static/chunks/6b00a407-f2a69e7fba2ff1d5.js\",\"604\",\"static/chunks/604-37da8378c60f8591.js\",\"156\",\"static/chunks/156-01d88aff08204544.js\",\"944\",\"static/chunks/944-7e8cad43dbfe6c27.js\",\"957\",\"static/chunks/957-6b88dac7433a3763.js\",\"974\",\"static/chunks/app/page-fbf1f0c2d5ddc3ca.js\"],\"AnimatedText\"]\n10:I[99697,[\"858\",\"static/chunks/aba955ec-9833060e0c799abe.js\",\"155\",\"static/chunks/6b00a407-f2a69e7fba2ff1d5.js\",\"604\",\"static/chunks/604-37da8378c60f8591.js\",\"156\",\"static/chunks/156-01d88aff08204544.js\",\"944\",\"static/chunks/944-7e8cad43dbfe6c27.js\",\"957\",\"static/chunks/957-6b88dac7433a3763.js\",\"974\",\"static/chunks/app/page-fbf1f0c2d5ddc3ca.js\"],\"CodeExample\"]\n11:I[64518,[\"858\",\"static/chunks/aba955ec-9833060e0c799abe.js\",\"155\",\"static/chunks/6b00a407-f2a69e7fba2ff1d5.js\",\"604\",\"static/chunks/604-37da8378c60f8591.js\",\"156\",\"static/chunks/156-01d88aff08204544.js\",\"944\",\"static/chunks/944-7e8cad43dbfe6c27.js\",\"957\",\"static/chunks/957-6b88dac7433a3763.js\",\"974\",\"static/chunks/app/page-fbf1f0c2d5ddc3ca.js\"],\"Features\"]\n12:I[52032,[\"858\",\"static/chunks/aba955ec-9833060e0c799abe.js\",\"155\",\"static/chunks/6b00a407-f2a69e7fba2ff1d5.js\",\"604\",\"static/chunks/604-37da8378c60f8591.js\",\"156\",\"static/chunks/156-01d88aff08204544.js\",\"944\",\"static/chunks/944-7e8cad43dbfe6c27.js\",\"957\",\"static/chunks/957-6b88dac7433a3763.js\",\"974\",\"static/chunks/app/page-fbf1f0c2d5ddc3ca.js\"],\"Testimonials\"]\n13:I[7615,[\"858\",\"static/chunks/aba955ec-9833060e0c799abe.js\",\"155\",\"static/chunks/6b00a407-f2a69e7fba2ff1d5.js\",\"604\",\"static/chunks/604-37da8378c60f8591.js\",\"156\",\"static/chunks/156-01d88aff08204544.js\",\"944\",\"static/"])</script><script>self.__next_f.push([1,"chunks/944-7e8cad43dbfe6c27.js\",\"957\",\"static/chunks/957-6b88dac7433a3763.js\",\"974\",\"static/chunks/app/page-fbf1f0c2d5ddc3ca.js\"],\"FAQSection\"]\n14:I[70678,[\"858\",\"static/chunks/aba955ec-9833060e0c799abe.js\",\"155\",\"static/chunks/6b00a407-f2a69e7fba2ff1d5.js\",\"604\",\"static/chunks/604-37da8378c60f8591.js\",\"156\",\"static/chunks/156-01d88aff08204544.js\",\"944\",\"static/chunks/944-7e8cad43dbfe6c27.js\",\"957\",\"static/chunks/957-6b88dac7433a3763.js\",\"974\",\"static/chunks/app/page-fbf1f0c2d5ddc3ca.js\"],\"InstallSection\"]\n"])</script><script>self.__next_f.push([1,"5:[[\"$\",\"section\",null,{\"className\":\"relative flex h-[calc(125vh-theme(spacing.16))] items-center overflow-hidden md:h-[calc(100svh-theme(spacing.16))] lg:h-[calc(100vh-theme(spacing.16))]\",\"children\":[[\"$\",\"$Le\",null,{}],[\"$\",\"div\",null,{\"className\":\"container relative z-10 mx-auto px-4 sm:px-6 lg:px-8\",\"children\":[\"$\",\"div\",null,{\"className\":\"grid gap-8 md:gap-12 lg:grid-cols-2 lg:gap-16\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex flex-col justify-center space-y-6 sm:space-y-8\",\"children\":[[\"$\",\"div\",null,{\"children\":[[\"$\",\"h1\",null,{\"className\":\"text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl lg:text-6xl\",\"children\":[[\"$\",\"span\",null,{\"className\":\"block\",\"children\":\"Your\"}],[\"$\",\"$Lf\",null,{\"className\":\"bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent\",\"children\":\"AI-Powered\"}],[\"$\",\"span\",null,{\"className\":\"block\",\"children\":\"Dev Team, Right in Your Editor.\"}]]}],[\"$\",\"p\",null,{\"className\":\"mt-4 max-w-md text-base text-muted-foreground sm:mt-6 sm:text-lg\",\"children\":[\"Supercharge your editor with AI that\",\" \",[\"$\",\"$Lf\",null,{\"className\":\"bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent\",\"children\":\"understands your codebase\"}],\", streamlines development, and helps you write, refactor, and debug with ease.\"]}]]}],[\"$\",\"div\",null,{\"className\":\"flex flex-col space-y-3 sm:flex-row sm:space-x-4 sm:space-y-0\",\"children\":[[\"$\",\"button\",null,{\"className\":\"inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [\u0026_svg]:pointer-events-none [\u0026_svg]:size-4 [\u0026_svg]:shrink-0 bg-primary text-primary-foreground shadow h-10 rounded-md px-8 w-full hover:bg-gray-200 dark:bg-white dark:text-black sm:w-auto\",\"ref\":\"$undefined\",\"children\":[\"$\",\"a\",null,{\"href\":\"https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline\",\"target\":\"_blank\",\"className\":\"flex w-full items-center justify-center\",\"children\":[\"Install Roo Code\",[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"className\":\"ml-2 h-4 w-4\",\"viewBox\":\"0 0 20 20\",\"fill\":\"currentColor\",\"children\":[\"$\",\"path\",null,{\"fillRule\":\"evenodd\",\"d\":\"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z\",\"clipRule\":\"evenodd\"}]}]]}]}],[\"$\",\"button\",null,{\"className\":\"inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [\u0026_svg]:pointer-events-none [\u0026_svg]:size-4 [\u0026_svg]:shrink-0 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-10 rounded-md px-8 w-full sm:w-auto\",\"ref\":\"$undefined\",\"children\":[\"$\",\"a\",null,{\"href\":\"https://docs.roocode.com\",\"target\":\"_blank\",\"className\":\"flex w-full items-center justify-center\",\"children\":\"View Documentation\"}]}]]}]]}],[\"$\",\"div\",null,{\"className\":\"relative mt-8 flex items-center justify-center lg:mt-0\",\"children\":[[\"$\",\"div\",null,{\"className\":\"absolute inset-0 flex items-center justify-center\",\"children\":[\"$\",\"div\",null,{\"className\":\"h-[250px] w-[250px] rounded-full bg-blue-500/20 blur-[100px] sm:h-[300px] sm:w-[300px] md:h-[350px] md:w-[350px]\"}]}],[\"$\",\"$L10\",null,{}]]}]]}]}]]}],[\"$\",\"div\",null,{\"id\":\"features\",\"children\":[\"$\",\"$L11\",null,{}]}],[\"$\",\"div\",null,{\"id\":\"testimonials\",\"children\":[\"$\",\"$L12\",null,{}]}],[\"$\",\"div\",null,{\"id\":\"faq\",\"children\":[\"$\",\"$L13\",null,{}]}],[\"$\",\"$L14\",null,{\"downloads\":\"702.6k\"}]]\n"])</script><script>self.__next_f.push([1,"15:I[91095,[\"858\",\"static/chunks/aba955ec-9833060e0c799abe.js\",\"840\",\"static/chunks/e5c963a3-f3448fec3e69041a.js\",\"777\",\"static/chunks/88e8e2ac-7a1619a6e98006b4.js\",\"798\",\"static/chunks/2ae967de-afd0847aa04941df.js\",\"180\",\"static/chunks/dd0ec533-85265a862baf3739.js\",\"872\",\"static/chunks/5d6f4545-30a8b6f9fd887cd0.js\",\"604\",\"static/chunks/604-37da8378c60f8591.js\",\"156\",\"static/chunks/156-01d88aff08204544.js\",\"657\",\"static/chunks/657-9a922aaadb9c4bb2.js\",\"954\",\"static/chunks/954-09ef0ac74eac9fe3.js\",\"554\",\"static/chunks/554-3f07505b777a310d.js\",\"177\",\"static/chunks/app/layout-70c3bb938d5edbac.js\"],\"NavBar\"]\n16:I[95823,[],\"\"]\n17:I[20531,[],\"\"]\n18:I[29685,[\"858\",\"static/chunks/aba955ec-9833060e0c799abe.js\",\"840\",\"static/chunks/e5c963a3-f3448fec3e69041a.js\",\"777\",\"static/chunks/88e8e2ac-7a1619a6e98006b4.js\",\"798\",\"static/chunks/2ae967de-afd0847aa04941df.js\",\"180\",\"static/chunks/dd0ec533-85265a862baf3739.js\",\"872\",\"static/chunks/5d6f4545-30a8b6f9fd887cd0.js\",\"604\",\"static/chunks/604-37da8378c60f8591.js\",\"156\",\"static/chunks/156-01d88aff08204544.js\",\"657\",\"static/chunks/657-9a922aaadb9c4bb2.js\",\"954\",\"static/chunks/954-09ef0ac74eac9fe3.js\",\"554\",\"static/chunks/554-3f07505b777a310d.js\",\"177\",\"static/chunks/app/layout-70c3bb938d5edbac.js\"],\"Footer\"]\n4:[\"$\",\"div\",null,{\"className\":\"flex min-h-screen flex-col bg-background text-foreground\",\"children\":[[\"$\",\"$L15\",null,{\"stars\":\"17.6k\",\"downloads\":\"702.6k\"}],[\"$\",\"main\",null,{\"className\":\"flex-1\",\"children\":[\"$\",\"$L16\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L17\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":"])</script><script>self.__next_f.push([1,"[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}],[\"$\",\"$L18\",null,{}]]}]\n"])</script></body></html>