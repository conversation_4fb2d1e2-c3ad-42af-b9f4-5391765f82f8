(()=>{var e={};e.id=66,e.ids=[66],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9869:()=>{},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17044:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>n,metadata:()=>t});var o=s(90811);let t={title:"Terms of Service - Roo Code",description:"Terms of Service for Roo Code Cloud. Learn about our service terms, commercial conditions, and legal framework."};function n(){return(0,o.jsx)(o.Fragment,{children:(0,o.jsx)("div",{className:"container mx-auto px-4 py-12 sm:px-6 lg:px-8",children:(0,o.jsxs)("div",{className:"prose prose-lg mx-auto max-w-4xl dark:prose-invert",children:[(0,o.jsx)("h1",{className:"text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl",children:"Roo Code Cloud Terms of Service"}),(0,o.jsx)("p",{className:"text-muted-foreground",children:(0,o.jsx)("em",{children:"(Version 1.0 – Effective June 19, 2025)"})}),(0,o.jsxs)("p",{className:"lead",children:['These Terms of Service ("',(0,o.jsx)("strong",{children:"TOS"}),'") govern access to and use of the Roo Code Cloud service (the "',(0,o.jsx)("strong",{children:"Service"}),'"). They apply to:']}),(0,o.jsxs)("ul",{className:"lead",children:[(0,o.jsxs)("li",{children:[(0,o.jsx)("strong",{children:"(a)"})," every ",(0,o.jsx)("strong",{children:"Sales Order Form"})," or similar document mutually executed by Roo Code and the customer that references these TOS; ",(0,o.jsx)("strong",{children:"and"})]}),(0,o.jsxs)("li",{children:[(0,o.jsx)("strong",{children:"(b)"})," any"," ",(0,o.jsx)("strong",{children:"online plan-selection, self-service sign-up, or in-app purchase flow"})," ",'through which a customer clicks an "I Agree" (or equivalent) button to accept these TOS — such flow also being an ',(0,o.jsx)("strong",{children:'"Order Form."'})]})]}),(0,o.jsxs)("p",{children:["By ",(0,o.jsx)("strong",{children:"creating an account, clicking to accept, or using the Service"}),', the person or entity doing so ("',(0,o.jsx)("strong",{children:"Customer"}),'") agrees to be bound by these TOS, even if no separate Order Form is signed.']}),(0,o.jsxs)("p",{children:['If Roo Code and Customer later execute a Master Subscription Agreement ("',(0,o.jsx)("strong",{children:"MSA"}),'"), the MSA governs; otherwise, these TOS and the applicable Order Form together form the entire agreement (the "',(0,o.jsx)("strong",{children:"Agreement"}),'").']}),(0,o.jsx)("h2",{className:"mt-12 text-2xl font-bold",children:"1. Agreement Framework"}),(0,o.jsxs)("ol",{children:[(0,o.jsxs)("li",{children:[(0,o.jsx)("strong",{children:"Incorporation of Standard Terms."}),(0,o.jsx)("br",{}),"The"," ",(0,o.jsx)("a",{href:"https://commonpaper.com/standards/cloud-service-agreement/2.0/",target:"_blank",rel:"noopener noreferrer",className:"text-primary hover:underline",children:(0,o.jsx)("em",{children:"Common Paper Cloud Service Standard Terms v 2.0"})})," ",'(the "',(0,o.jsx)("strong",{children:"Standard Terms"}),'") are incorporated by reference. If these TOS conflict with the Standard Terms, these TOS control.']}),(0,o.jsxs)("li",{children:[(0,o.jsx)("strong",{children:"Order of Precedence."}),(0,o.jsx)("br",{}),"(a) Order Form (b) these TOS (c) Standard Terms."]})]}),(0,o.jsx)("h2",{className:"mt-12 text-2xl font-bold",children:"2. Key Commercial Terms"}),(0,o.jsx)("div",{className:"overflow-x-auto",children:(0,o.jsxs)("table",{className:"min-w-full border-collapse border border-border",children:[(0,o.jsx)("thead",{children:(0,o.jsxs)("tr",{className:"bg-muted/50",children:[(0,o.jsx)("th",{className:"border border-border px-4 py-2 text-left font-semibold",children:"Term"}),(0,o.jsx)("th",{className:"border border-border px-4 py-2 text-left font-semibold",children:"Value"})]})}),(0,o.jsxs)("tbody",{children:[(0,o.jsxs)("tr",{children:[(0,o.jsx)("td",{className:"border border-border px-4 py-2 font-medium",children:"Governing Law / Forum"}),(0,o.jsx)("td",{className:"border border-border px-4 py-2",children:"Delaware law; exclusive jurisdiction and venue in the state or federal courts located in Delaware"})]}),(0,o.jsxs)("tr",{className:"bg-muted/25",children:[(0,o.jsx)("td",{className:"border border-border px-4 py-2 font-medium",children:"Plans & Subscription Periods"}),(0,o.jsxs)("td",{className:"border border-border px-4 py-2",children:[(0,o.jsx)("em",{children:"Free Plan:"})," month-to-month.",(0,o.jsx)("br",{}),(0,o.jsx)("em",{children:"Paid Plans:"})," Monthly ",(0,o.jsx)("strong",{children:"or"})," Annual, as selected in an Order Form or the online flow."]})]}),(0,o.jsxs)("tr",{children:[(0,o.jsx)("td",{className:"border border-border px-4 py-2 font-medium",children:"Auto-Renewal & Non-Renewal Notice"}),(0,o.jsxs)("td",{className:"border border-border px-4 py-2",children:[(0,o.jsx)("em",{children:"Free Plan:"})," renews continuously until cancelled in the dashboard.",(0,o.jsx)("br",{}),(0,o.jsx)("em",{children:"Paid Plans:"})," renew for the same period unless either party gives 30 days' written notice before the current period ends."]})]}),(0,o.jsxs)("tr",{className:"bg-muted/25",children:[(0,o.jsx)("td",{className:"border border-border px-4 py-2 font-medium",children:"Fees & Usage"}),(0,o.jsxs)("td",{className:"border border-border px-4 py-2",children:[(0,o.jsx)("em",{children:"Free Plan:"})," Subscription Fee = $0.",(0,o.jsx)("br",{}),(0,o.jsx)("em",{children:"Paid Plans:"})," Fees stated in the Order Form or online checkout"," ",(0,o.jsx)("strong",{children:"plus"})," usage-based fees, calculated and invoiced monthly."]})]}),(0,o.jsxs)("tr",{children:[(0,o.jsx)("td",{className:"border border-border px-4 py-2 font-medium",children:"Payment Terms"}),(0,o.jsxs)("td",{className:"border border-border px-4 py-2",children:[(0,o.jsx)("em",{children:"Monthly paid plans:"})," credit-card charge on the billing date.",(0,o.jsx)("br",{}),(0,o.jsx)("em",{children:"Annual paid plans:"})," invoiced Net 30 (credit card optional)."]})]}),(0,o.jsxs)("tr",{className:"bg-muted/25",children:[(0,o.jsx)("td",{className:"border border-border px-4 py-2 font-medium",children:"General Liability Cap"}),(0,o.jsx)("td",{className:"border border-border px-4 py-2",children:"The greater of (i) USD 100 and (ii) 1 \xd7 Fees paid or payable in the 12 months before the event giving rise to liability."})]}),(0,o.jsxs)("tr",{children:[(0,o.jsx)("td",{className:"border border-border px-4 py-2 font-medium",children:"Increased Cap / Unlimited Claims"}),(0,o.jsx)("td",{className:"border border-border px-4 py-2",children:"None"})]}),(0,o.jsxs)("tr",{className:"bg-muted/25",children:[(0,o.jsx)("td",{className:"border border-border px-4 py-2 font-medium",children:"Trial / Pilot"}),(0,o.jsx)("td",{className:"border border-border px-4 py-2",children:"Not offered"})]}),(0,o.jsxs)("tr",{children:[(0,o.jsx)("td",{className:"border border-border px-4 py-2 font-medium",children:"Beta Features"}),(0,o.jsx)("td",{className:"border border-border px-4 py-2",children:"None – only generally available features are provided"})]}),(0,o.jsxs)("tr",{className:"bg-muted/25",children:[(0,o.jsx)("td",{className:"border border-border px-4 py-2 font-medium",children:"Security Standard"}),(0,o.jsx)("td",{className:"border border-border px-4 py-2",children:"Roo Code maintains commercially reasonable administrative, physical, and technical safeguards"})]}),(0,o.jsxs)("tr",{children:[(0,o.jsx)("td",{className:"border border-border px-4 py-2 font-medium",children:"Machine-Learning Use"}),(0,o.jsxs)("td",{className:"border border-border px-4 py-2",children:["Roo Code ",(0,o.jsx)("strong",{children:"does not"})," use Customer Content to train, fine-tune, or improve any ML or AI models"]})]}),(0,o.jsxs)("tr",{className:"bg-muted/25",children:[(0,o.jsx)("td",{className:"border border-border px-4 py-2 font-medium",children:"Data Processing Addendum (DPA)"}),(0,o.jsx)("td",{className:"border border-border px-4 py-2",children:"GDPR/CCPA-ready DPA available upon written request"})]}),(0,o.jsxs)("tr",{children:[(0,o.jsx)("td",{className:"border border-border px-4 py-2 font-medium",children:"Publicity / Logo Rights"}),(0,o.jsx)("td",{className:"border border-border px-4 py-2",children:"Roo Code may identify Customer (name & logo) in marketing materials unless Customer opts out in writing"})]})]})]})}),(0,o.jsx)("h2",{className:"mt-12 text-2xl font-bold",children:"3. Modifications to the Standard Terms"}),(0,o.jsxs)("ol",{children:[(0,o.jsxs)("li",{children:[(0,o.jsx)("strong",{children:"Section 1.6 (Machine Learning)."}),(0,o.jsx)("br",{}),'"Provider will not use Customer Content or Usage Data to train, fine-tune, or improve any machine-learning or AI model, except with Customer\'s prior written consent."']}),(0,o.jsxs)("li",{children:[(0,o.jsx)("strong",{children:"Section 3 (Security)."}),(0,o.jsx)("br",{}),'Replace "reasonable" with "commercially reasonable."']}),(0,o.jsxs)("li",{children:[(0,o.jsx)("strong",{children:"Section 4 (Fees & Payment)."}),(0,o.jsx)("br",{}),"Add usage-billing language above and delete any provision allowing unilateral fee increases."]}),(0,o.jsxs)("li",{children:[(0,o.jsx)("strong",{children:"Section 5 (Term & Termination)."}),(0,o.jsx)("br",{}),"Insert auto-renewal and free-plan language above."]}),(0,o.jsxs)("li",{children:[(0,o.jsx)("strong",{children:"Sections 7 (Trials / Betas) and any SLA references."}),(0,o.jsx)("br",{}),"Deleted – Roo Code offers no trials, pilots, betas, or SLA credits under these TOS."]}),(0,o.jsxs)("li",{children:[(0,o.jsx)("strong",{children:"Section 12.12 (Publicity)."}),(0,o.jsx)("br",{}),'As reflected in the "Publicity / Logo Rights" row above.']})]}),(0,o.jsx)("h2",{className:"mt-12 text-2xl font-bold",children:"4. Use of the Service"}),(0,o.jsx)("p",{children:"Customer may access and use the Service solely for its internal business purposes and subject to the Acceptable Use Policy in the Standard Terms."}),(0,o.jsx)("h2",{className:"mt-12 text-2xl font-bold",children:"5. Account Management & Termination"}),(0,o.jsxs)("ul",{children:[(0,o.jsxs)("li",{children:[(0,o.jsx)("strong",{children:"Self-service cancellation or downgrade."}),(0,o.jsx)("br",{}),"Customer may cancel a Free Plan immediately, or cancel/downgrade a Paid Plan effective at the end of the current billing cycle, via the web dashboard."]}),(0,o.jsx)("li",{children:"Either party may otherwise terminate the Agreement as allowed under Section 5 of the Standard Terms."})]}),(0,o.jsx)("h2",{className:"mt-12 text-2xl font-bold",children:"6. Privacy & Data"}),(0,o.jsxs)("p",{children:["Roo Code's Privacy Notice (",(0,o.jsx)("a",{href:"https://roocode.com/privacy",rel:"noopener noreferrer",className:"text-primary hover:underline",children:"https://roocode.com/privacy"}),") explains how Roo Code collects and handles personal information. If Customer requires a DPA, email"," ",(0,o.jsx)("a",{href:"mailto:<EMAIL>",className:"text-primary hover:underline",children:"<EMAIL>"}),"."]}),(0,o.jsx)("h2",{className:"mt-12 text-2xl font-bold",children:"7. Warranty Disclaimer"}),(0,o.jsxs)("p",{children:["Except as expressly stated in the Agreement, the Service is provided"," ",(0,o.jsx)("strong",{children:'"as is,"'})," and all implied warranties are disclaimed to the maximum extent allowed by law."]}),(0,o.jsx)("h2",{className:"mt-12 text-2xl font-bold",children:"8. Limitation of Liability"}),(0,o.jsx)("p",{children:"The caps in Section 2 apply to all claims under the Agreement, whether in contract, tort, or otherwise, except for Excluded Claims defined in the Standard Terms."}),(0,o.jsx)("h2",{className:"mt-12 text-2xl font-bold",children:"9. Miscellaneous"}),(0,o.jsxs)("ol",{children:[(0,o.jsxs)("li",{children:[(0,o.jsx)("strong",{children:"Assignment."}),(0,o.jsx)("br",{}),"Customer may not assign the Agreement without Roo Code's prior written consent, except to a successor in a merger or sale of substantially all assets."]}),(0,o.jsxs)("li",{children:[(0,o.jsx)("strong",{children:"Export Compliance."}),(0,o.jsx)("br",{}),"Each party will comply with all applicable export-control laws and regulations and will not export or re-export any software or technical data without the required government licences."]}),(0,o.jsxs)("li",{children:[(0,o.jsx)("strong",{children:"Entire Agreement."}),(0,o.jsx)("br",{}),"The Agreement supersedes all prior or contemporaneous agreements for the Service."]}),(0,o.jsxs)("li",{children:[(0,o.jsx)("strong",{children:"Amendments."}),(0,o.jsx)("br",{}),"Roo Code may update these TOS by posting a revised version at the same URL and emailing or in-app notifying Customer at least 30 days before changes take effect. Continued use after the effective date constitutes acceptance."]})]}),(0,o.jsx)("h2",{className:"mt-12 text-2xl font-bold",children:"10. Contact"}),(0,o.jsxs)("p",{children:[(0,o.jsx)("strong",{children:"Roo Code, Inc."}),(0,o.jsx)("br",{}),"98 Graceland Dr, San Rafael, CA 94901 USA",(0,o.jsx)("br",{}),"Email:"," ",(0,o.jsx)("a",{href:"mailto:<EMAIL>",className:"text-primary hover:underline",children:"<EMAIL>"})]})]})})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},57133:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},89605:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>a.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>h,tree:()=>l});var o=s(88253),t=s(21418),n=s(52052),a=s.n(n),d=s(75779),i={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>d[e]);s.d(r,i);let l={children:["",{children:["terms",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,17044)),"C:\\Users\\<USER>\\Desktop\\personale\\Roo-Code\\apps\\web-roo-code\\src\\app\\terms\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,36295)),"C:\\Users\\<USER>\\Desktop\\personale\\Roo-Code\\apps\\web-roo-code\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,64544,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,20441,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,93670,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Desktop\\personale\\Roo-Code\\apps\\web-roo-code\\src\\app\\terms\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},h=new o.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/terms/page",pathname:"/terms",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),o=r.X(0,[595,627,111],()=>s(89605));module.exports=o})();