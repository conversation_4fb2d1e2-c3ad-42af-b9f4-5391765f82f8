<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/c16eb50558afbc00.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-c8e217c742f0746a.js"/><script src="/_next/static/chunks/206c0df1-50f53d661b5e5bf5.js" async=""></script><script src="/_next/static/chunks/550-cc2f0cda2d11cae3.js" async=""></script><script src="/_next/static/chunks/main-app-f1892ae8c4b297c8.js" async=""></script><script src="/_next/static/chunks/aba955ec-9833060e0c799abe.js" async=""></script><script src="/_next/static/chunks/e5c963a3-f3448fec3e69041a.js" async=""></script><script src="/_next/static/chunks/88e8e2ac-7a1619a6e98006b4.js" async=""></script><script src="/_next/static/chunks/2ae967de-afd0847aa04941df.js" async=""></script><script src="/_next/static/chunks/dd0ec533-85265a862baf3739.js" async=""></script><script src="/_next/static/chunks/5d6f4545-30a8b6f9fd887cd0.js" async=""></script><script src="/_next/static/chunks/604-37da8378c60f8591.js" async=""></script><script src="/_next/static/chunks/156-01d88aff08204544.js" async=""></script><script src="/_next/static/chunks/657-9a922aaadb9c4bb2.js" async=""></script><script src="/_next/static/chunks/954-09ef0ac74eac9fe3.js" async=""></script><script src="/_next/static/chunks/554-3f07505b777a310d.js" async=""></script><script src="/_next/static/chunks/app/layout-70c3bb938d5edbac.js" async=""></script><link rel="preload" href="https://www.googletagmanager.com/gtag/js?id=AW-17391954825" as="script"/><title>Privacy Policy - Roo Code</title><meta name="description" content="Privacy policy for Roo Code Cloud and marketing website. Learn how we handle your data and protect your privacy."/><link rel="canonical" href="https://roocode.com"/><link rel="icon" href="/favicon.ico"/><link rel="icon" href="/favicon-16x16.png" sizes="16x16" type="image/png"/><link rel="icon" href="/favicon-32x32.png" sizes="32x32" type="image/png"/><link rel="apple-touch-icon" href="/apple-touch-icon.png"/><link rel="android-chrome-192x192" href="/android-chrome-192x192.png" sizes="192x192" type="image/png"/><link rel="android-chrome-512x512" href="/android-chrome-512x512.png" sizes="512x512" type="image/png"/><link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/gh/devicons/devicon@latest/devicon.min.css"/><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__className_e8ce0c"><div itemScope="" itemType="https://schema.org/WebSite"><link itemProp="url" href="https://roocode.com"/><meta itemProp="name" content="Roo Code"/></div><!--$!--><template data-dgst="BAILOUT_TO_CLIENT_SIDE_RENDERING"></template><!--/$--><script>((e,t,r,n,i,o,a,s)=>{let l=document.documentElement,u=["light","dark"];function c(t){var r;(Array.isArray(e)?e:[e]).forEach(e=>{let r="class"===e,n=r&&o?i.map(e=>o[e]||e):i;r?(l.classList.remove(...n),l.classList.add(o&&o[t]?o[t]:t)):l.setAttribute(e,t)}),r=t,s&&u.includes(r)&&(l.style.colorScheme=r)}if(n)c(n);else try{let e=localStorage.getItem(t)||r,n=a&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;c(n)}catch(e){}})("class","theme","dark",null,["light","dark"],null,false,true)</script><div class="flex min-h-screen flex-col bg-background text-foreground"><header class="sticky top-0 z-50 border-b border-border bg-background/80 backdrop-blur-md"><div class="container mx-auto flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8"><div class="flex items-center"><a class="flex items-center" href="/"><img alt="Roo Code Logo" loading="lazy" width="120" height="40" decoding="async" data-nimg="1" class="h-8 w-auto" style="color:transparent" src="/Roo-Code-Logo-Horiz-white.svg"/></a></div><nav class="hidden text-sm font-medium md:flex md:items-center md:space-x-3 xl:space-x-8"><button class="text-muted-foreground transition-transform duration-200 hover:scale-105 hover:text-foreground max-lg:hidden">Features</button><button class="text-muted-foreground transition-transform duration-200 hover:scale-105 hover:text-foreground max-lg:hidden">Testimonials</button><button class="text-muted-foreground transition-transform duration-200 hover:scale-105 hover:text-foreground">FAQ</button><a class="text-muted-foreground transition-transform duration-200 hover:scale-105 hover:text-foreground" href="/evals">Evals</a><a class="text-muted-foreground transition-transform duration-200 hover:scale-105 hover:text-foreground" href="/enterprise">Enterprise</a><a href="https://docs.roocode.com" target="_blank" class="text-muted-foreground transition-transform duration-200 hover:scale-105 hover:text-foreground">Docs</a><a href="https://careers.roocode.com" target="_blank" class="text-muted-foreground transition-transform duration-200 hover:scale-105 hover:text-foreground">Careers</a><div class="flex items-center rounded-full bg-gradient-to-r from-blue-400 to-cyan-400 p-0.5 text-xs"><div class="rounded-full bg-background px-2 py-1.5"><span class="text-muted-foreground border-r-2 border-foreground/50 pr-1.5">Roo Code Cloud is coming</span><a href="/cloud-waitlist" rel="noopener noreferrer" class="font-medium text-primary hover:underline pl-1.5">Sign up</a></div></div></nav><div class="hidden md:flex md:items-center md:space-x-4"><div class="flex flex-row space-x-2"><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-9 w-9" disabled=""><svg stroke="currentColor" fill="none" stroke-width="0" viewBox="0 0 15 15" class="h-4 w-4" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.5 0C7.77614 0 8 0.223858 8 0.5V2.5C8 2.77614 7.77614 3 7.5 3C7.22386 3 7 2.77614 7 2.5V0.5C7 0.223858 7.22386 0 7.5 0ZM2.1967 2.1967C2.39196 2.00144 2.70854 2.00144 2.90381 2.1967L4.31802 3.61091C4.51328 3.80617 4.51328 4.12276 4.31802 4.31802C4.12276 4.51328 3.80617 4.51328 3.61091 4.31802L2.1967 2.90381C2.00144 2.70854 2.00144 2.39196 2.1967 2.1967ZM0.5 7C0.223858 7 0 7.22386 0 7.5C0 7.77614 0.223858 8 0.5 8H2.5C2.77614 8 3 7.77614 3 7.5C3 7.22386 2.77614 7 2.5 7H0.5ZM2.1967 12.8033C2.00144 12.608 2.00144 12.2915 2.1967 12.0962L3.61091 10.682C3.80617 10.4867 4.12276 10.4867 4.31802 10.682C4.51328 10.8772 4.51328 11.1938 4.31802 11.3891L2.90381 12.8033C2.70854 12.9986 2.39196 12.9986 2.1967 12.8033ZM12.5 7C12.2239 7 12 7.22386 12 7.5C12 7.77614 12.2239 8 12.5 8H14.5C14.7761 8 15 7.77614 15 7.5C15 7.22386 14.7761 7 14.5 7H12.5ZM10.682 4.31802C10.4867 4.12276 10.4867 3.80617 10.682 3.61091L12.0962 2.1967C12.2915 2.00144 12.608 2.00144 12.8033 2.1967C12.9986 2.39196 12.9986 2.70854 12.8033 2.90381L11.3891 4.31802C11.1938 4.51328 10.8772 4.51328 10.682 4.31802ZM8 12.5C8 12.2239 7.77614 12 7.5 12C7.22386 12 7 12.2239 7 12.5V14.5C7 14.7761 7.22386 15 7.5 15C7.77614 15 8 14.7761 8 14.5V12.5ZM10.682 10.682C10.8772 10.4867 11.1938 10.4867 11.3891 10.682L12.8033 12.0962C12.9986 12.2915 12.9986 12.608 12.8033 12.8033C12.608 12.9986 12.2915 12.9986 12.0962 12.8033L10.682 11.3891C10.4867 11.1938 10.4867 10.8772 10.682 10.682ZM5.5 7.5C5.5 6.39543 6.39543 5.5 7.5 5.5C8.60457 5.5 9.5 6.39543 9.5 7.5C9.5 8.60457 8.60457 9.5 7.5 9.5C6.39543 9.5 5.5 8.60457 5.5 7.5ZM7.5 4.5C5.84315 4.5 4.5 5.84315 4.5 7.5C4.5 9.15685 5.84315 10.5 7.5 10.5C9.15685 10.5 10.5 9.15685 10.5 7.5C10.5 5.84315 9.15685 4.5 7.5 4.5Z" fill="currentColor"></path></svg></button><a target="_blank" class="hidden items-center gap-1.5 text-sm font-medium text-muted-foreground hover:text-foreground md:flex" href="https://github.com/RooCodeInc/Roo-Code"><svg stroke="currentColor" fill="none" stroke-width="0" viewBox="0 0 15 15" class="h-4 w-4" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.49933 0.25C3.49635 0.25 0.25 3.49593 0.25 7.50024C0.25 10.703 2.32715 13.4206 5.2081 14.3797C5.57084 14.446 5.70302 14.2222 5.70302 14.0299C5.70302 13.8576 5.69679 13.4019 5.69323 12.797C3.67661 13.235 3.25112 11.825 3.25112 11.825C2.92132 10.9874 2.44599 10.7644 2.44599 10.7644C1.78773 10.3149 2.49584 10.3238 2.49584 10.3238C3.22353 10.375 3.60629 11.0711 3.60629 11.0711C4.25298 12.1788 5.30335 11.8588 5.71638 11.6732C5.78225 11.205 5.96962 10.8854 6.17658 10.7043C4.56675 10.5209 2.87415 9.89918 2.87415 7.12104C2.87415 6.32925 3.15677 5.68257 3.62053 5.17563C3.54576 4.99226 3.29697 4.25521 3.69174 3.25691C3.69174 3.25691 4.30015 3.06196 5.68522 3.99973C6.26337 3.83906 6.8838 3.75895 7.50022 3.75583C8.1162 3.75895 8.73619 3.83906 9.31523 3.99973C10.6994 3.06196 11.3069 3.25691 11.3069 3.25691C11.7026 4.25521 11.4538 4.99226 11.3795 5.17563C11.8441 5.68257 12.1245 6.32925 12.1245 7.12104C12.1245 9.9063 10.4292 10.5192 8.81452 10.6985C9.07444 10.9224 9.30633 11.3648 9.30633 12.0413C9.30633 13.0102 9.29742 13.7922 9.29742 14.0299C9.29742 14.2239 9.42828 14.4496 9.79591 14.3788C12.6746 13.4179 14.75 10.7025 14.75 7.50024C14.75 3.49593 11.5036 0.25 7.49933 0.25Z" fill="currentColor"></path></svg><span>17.6k</span></a></div><a target="_blank" class="hidden items-center gap-1.5 rounded-full bg-primary px-3 py-1.5 text-sm font-medium text-primary-foreground transition-colors hover:bg-primary/90 md:flex" href="https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 16 16" class="-mr-[2px] mt-[1px] h-4 w-4" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M10.8634 13.9195C10.6568 14.0195 10.4233 14.0246 10.2185 13.9444C10.1162 13.9044 10.021 13.843 9.93997 13.7614L4.81616 9.06268L2.58433 10.7656C2.37657 10.9241 2.08597 10.9111 1.89301 10.7347L1.17719 10.0802C0.941168 9.86437 0.940898 9.49112 1.17661 9.27496L3.11213 7.5L1.17661 5.72504C0.940898 5.50888 0.941168 5.13563 1.17719 4.91982L1.89301 4.2653C2.08597 4.08887 2.37657 4.07588 2.58433 4.2344L4.81616 5.93732L9.93997 1.23855C9.97037 1.20797 10.0028 1.18023 10.0368 1.15538C10.2748 0.981429 10.5922 0.949298 10.8634 1.08048L13.5399 2.37507C13.8212 2.5111 14 2.79721 14 3.11109V8H10.752V4.53356L6.86419 7.5L10.752 10.4664V8H14V11.8889C14 12.2028 13.8211 12.4889 13.5399 12.625L10.8634 13.9195Z"></path></svg><span>Install <span class="font-black max-lg:text-xs">·</span></span><span>702.6k</span></a></div><button aria-expanded="false" class="flex items-center justify-center rounded-full p-2 transition-colors hover:bg-accent md:hidden" aria-label="Toggle mobile menu"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 20 20" aria-hidden="true" class="h-6 w-6" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path></svg></button></div><div class="absolute left-0 right-0 top-16 z-50 transform border-b border-border bg-background shadow-lg backdrop-blur-none transition-all duration-200 md:hidden pointer-events-none -translate-y-2 opacity-0"><nav class="flex flex-col py-2"><div class="mx-5 mb-2 flex items-center rounded-full bg-gradient-to-r from-blue-400 to-cyan-400 p-0.5 text-xs"><div class="flex-grow text-center rounded-full bg-background px-2 py-1.5"><span class="text-muted-foreground border-r-2 border-foreground/50 pr-3">Roo Code Cloud is coming</span><a href="/cloud-waitlist" rel="noopener noreferrer" class="font-medium text-primary hover:underline pl-3">Sign up</a></div></div><button class="w-full px-8 py-3 text-left text-sm font-medium text-foreground/80 transition-colors hover:bg-accent hover:text-foreground">Features</button><button class="w-full px-8 py-3 text-left text-sm font-medium text-foreground/80 transition-colors hover:bg-accent hover:text-foreground">Testimonials</button><button class="w-full px-8 py-3 text-left text-sm font-medium text-foreground/80 transition-colors hover:bg-accent hover:text-foreground">FAQ</button><a class="w-full px-8 py-3 text-left text-sm font-medium text-foreground/80 transition-colors hover:bg-accent hover:text-foreground" href="/evals">Evals</a><a class="w-full px-8 py-3 text-left text-sm font-medium text-foreground/80 transition-colors hover:bg-accent hover:text-foreground" href="/enterprise">Enterprise</a><a href="https://trust.roocode.com" target="_blank" rel="noopener noreferrer" class="w-full px-8 py-3 text-left text-sm font-medium text-foreground/80 transition-colors hover:bg-accent hover:text-foreground">Security</a><a href="https://docs.roocode.com" target="_blank" class="w-full px-8 py-3 text-left text-sm font-medium text-foreground/80 transition-colors hover:bg-accent hover:text-foreground">Docs</a><a href="https://careers.roocode.com" target="_blank" class="w-full px-8 py-3 text-left text-sm font-medium text-foreground/80 transition-colors hover:bg-accent hover:text-foreground">Careers</a><hr class="mx-8 my-2 border-t border-border/50"/><div class="flex items-center justify-center gap-8 px-8 py-3"><a target="_blank" class="inline-flex items-center gap-2 rounded-md p-2 text-sm font-medium text-foreground/80 transition-colors hover:bg-accent hover:text-foreground" href="https://github.com/RooCodeInc/Roo-Code"><svg stroke="currentColor" fill="none" stroke-width="0" viewBox="0 0 15 15" class="h-5 w-5" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.49933 0.25C3.49635 0.25 0.25 3.49593 0.25 7.50024C0.25 10.703 2.32715 13.4206 5.2081 14.3797C5.57084 14.446 5.70302 14.2222 5.70302 14.0299C5.70302 13.8576 5.69679 13.4019 5.69323 12.797C3.67661 13.235 3.25112 11.825 3.25112 11.825C2.92132 10.9874 2.44599 10.7644 2.44599 10.7644C1.78773 10.3149 2.49584 10.3238 2.49584 10.3238C3.22353 10.375 3.60629 11.0711 3.60629 11.0711C4.25298 12.1788 5.30335 11.8588 5.71638 11.6732C5.78225 11.205 5.96962 10.8854 6.17658 10.7043C4.56675 10.5209 2.87415 9.89918 2.87415 7.12104C2.87415 6.32925 3.15677 5.68257 3.62053 5.17563C3.54576 4.99226 3.29697 4.25521 3.69174 3.25691C3.69174 3.25691 4.30015 3.06196 5.68522 3.99973C6.26337 3.83906 6.8838 3.75895 7.50022 3.75583C8.1162 3.75895 8.73619 3.83906 9.31523 3.99973C10.6994 3.06196 11.3069 3.25691 11.3069 3.25691C11.7026 4.25521 11.4538 4.99226 11.3795 5.17563C11.8441 5.68257 12.1245 6.32925 12.1245 7.12104C12.1245 9.9063 10.4292 10.5192 8.81452 10.6985C9.07444 10.9224 9.30633 11.3648 9.30633 12.0413C9.30633 13.0102 9.29742 13.7922 9.29742 14.0299C9.29742 14.2239 9.42828 14.4496 9.79591 14.3788C12.6746 13.4179 14.75 10.7025 14.75 7.50024C14.75 3.49593 11.5036 0.25 7.49933 0.25Z" fill="currentColor"></path></svg><span>17.6k</span></a><div class="flex items-center rounded-md p-2 transition-colors hover:bg-accent"><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-9 w-9" disabled=""><svg stroke="currentColor" fill="none" stroke-width="0" viewBox="0 0 15 15" class="h-4 w-4" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.5 0C7.77614 0 8 0.223858 8 0.5V2.5C8 2.77614 7.77614 3 7.5 3C7.22386 3 7 2.77614 7 2.5V0.5C7 0.223858 7.22386 0 7.5 0ZM2.1967 2.1967C2.39196 2.00144 2.70854 2.00144 2.90381 2.1967L4.31802 3.61091C4.51328 3.80617 4.51328 4.12276 4.31802 4.31802C4.12276 4.51328 3.80617 4.51328 3.61091 4.31802L2.1967 2.90381C2.00144 2.70854 2.00144 2.39196 2.1967 2.1967ZM0.5 7C0.223858 7 0 7.22386 0 7.5C0 7.77614 0.223858 8 0.5 8H2.5C2.77614 8 3 7.77614 3 7.5C3 7.22386 2.77614 7 2.5 7H0.5ZM2.1967 12.8033C2.00144 12.608 2.00144 12.2915 2.1967 12.0962L3.61091 10.682C3.80617 10.4867 4.12276 10.4867 4.31802 10.682C4.51328 10.8772 4.51328 11.1938 4.31802 11.3891L2.90381 12.8033C2.70854 12.9986 2.39196 12.9986 2.1967 12.8033ZM12.5 7C12.2239 7 12 7.22386 12 7.5C12 7.77614 12.2239 8 12.5 8H14.5C14.7761 8 15 7.77614 15 7.5C15 7.22386 14.7761 7 14.5 7H12.5ZM10.682 4.31802C10.4867 4.12276 10.4867 3.80617 10.682 3.61091L12.0962 2.1967C12.2915 2.00144 12.608 2.00144 12.8033 2.1967C12.9986 2.39196 12.9986 2.70854 12.8033 2.90381L11.3891 4.31802C11.1938 4.51328 10.8772 4.51328 10.682 4.31802ZM8 12.5C8 12.2239 7.77614 12 7.5 12C7.22386 12 7 12.2239 7 12.5V14.5C7 14.7761 7.22386 15 7.5 15C7.77614 15 8 14.7761 8 14.5V12.5ZM10.682 10.682C10.8772 10.4867 11.1938 10.4867 11.3891 10.682L12.8033 12.0962C12.9986 12.2915 12.9986 12.608 12.8033 12.8033C12.608 12.9986 12.2915 12.9986 12.0962 12.8033L10.682 11.3891C10.4867 11.1938 10.4867 10.8772 10.682 10.682ZM5.5 7.5C5.5 6.39543 6.39543 5.5 7.5 5.5C8.60457 5.5 9.5 6.39543 9.5 7.5C9.5 8.60457 8.60457 9.5 7.5 9.5C6.39543 9.5 5.5 8.60457 5.5 7.5ZM7.5 4.5C5.84315 4.5 4.5 5.84315 4.5 7.5C4.5 9.15685 5.84315 10.5 7.5 10.5C9.15685 10.5 10.5 9.15685 10.5 7.5C10.5 5.84315 9.15685 4.5 7.5 4.5Z" fill="currentColor"></path></svg></button></div><a target="_blank" class="inline-flex items-center gap-2 rounded-md p-2 text-sm font-medium text-foreground/80 transition-colors hover:bg-accent hover:text-foreground" href="https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 16 16" class="h-5 w-5" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M10.8634 13.9195C10.6568 14.0195 10.4233 14.0246 10.2185 13.9444C10.1162 13.9044 10.021 13.843 9.93997 13.7614L4.81616 9.06268L2.58433 10.7656C2.37657 10.9241 2.08597 10.9111 1.89301 10.7347L1.17719 10.0802C0.941168 9.86437 0.940898 9.49112 1.17661 9.27496L3.11213 7.5L1.17661 5.72504C0.940898 5.50888 0.941168 5.13563 1.17719 4.91982L1.89301 4.2653C2.08597 4.08887 2.37657 4.07588 2.58433 4.2344L4.81616 5.93732L9.93997 1.23855C9.97037 1.20797 10.0028 1.18023 10.0368 1.15538C10.2748 0.981429 10.5922 0.949298 10.8634 1.08048L13.5399 2.37507C13.8212 2.5111 14 2.79721 14 3.11109V8H10.752V4.53356L6.86419 7.5L10.752 10.4664V8H14V11.8889C14 12.2028 13.8211 12.4889 13.5399 12.625L10.8634 13.9195Z"></path></svg><span>702.6k</span></a></div></nav></div></header><main class="flex-1"><div class="container mx-auto px-4 py-12 sm:px-6 lg:px-8"><div class="prose prose-lg mx-auto max-w-4xl dark:prose-invert"><h1 class="text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl">Roo Code Cloud Privacy Policy</h1><p class="text-muted-foreground">Last Updated: June 19, 2025</p><p class="lead">This Privacy Policy explains how Roo Code, Inc. (&quot;Roo Code,&quot; &quot;we,&quot; &quot;our,&quot; or &quot;us&quot;) collects, uses, and shares information when you:</p><ul class="lead"><li>browse any page under <strong>roocode.com</strong> (the <em>Marketing Site</em>); and/or</li><li>create an account for, sign in to, or otherwise use <strong>Roo Code Cloud</strong> at<!-- --> <strong>app.roocode.com</strong> or through the Roo Code extension while authenticated to that Cloud account (the <em>Cloud Service</em>).</li></ul><div class="my-8 rounded-lg border border-border bg-muted/50 p-6"><h3 class="mt-0 text-lg font-semibold">Extension‑Only Usage</h3><p class="mb-0">If you run the Roo Code extension <strong>without</strong> connecting to a Cloud account, your data is governed by the standalone<!-- --> <a href="https://github.com/RooCodeInc/Roo-Code/blob/main/PRIVACY.md" target="_blank" rel="noopener noreferrer" class="text-primary hover:underline">Roo Code Extension Privacy Policy</a>.</p></div><h2 class="mt-12 text-2xl font-bold">Quick Summary</h2><ul><li><strong>Your source code never transits Roo Code servers.</strong> It stays on your device and is sent <strong>directly</strong>—via a client‑to‑provider TLS connection—to the third‑party AI model you select. Roo Code never stores, inspects, or trains on your code.</li><li><strong>Prompts and chat snippets are collected by default</strong> in Roo Code Cloud so you can search and re‑use past conversations. Organization admins can disable this collection at any time.</li><li>We collect only the data needed to operate Roo Code Cloud, do <strong>not</strong> sell customer data, and do <strong>not</strong> use your content to train models.</li></ul><h2 class="mt-12 text-2xl font-bold">1. Information We Collect</h2><div class="overflow-x-auto"><table class="min-w-full border-collapse border border-border"><thead><tr class="bg-muted/50"><th class="border border-border px-4 py-2 text-left font-semibold">Category</th><th class="border border-border px-4 py-2 text-left font-semibold">Examples</th><th class="border border-border px-4 py-2 text-left font-semibold">Source</th></tr></thead><tbody><tr><td class="border border-border px-4 py-2 font-medium">Account Information</td><td class="border border-border px-4 py-2">Name, email, organization, auth tokens</td><td class="border border-border px-4 py-2">You</td></tr><tr class="bg-muted/25"><td class="border border-border px-4 py-2 font-medium">Workspace Configuration</td><td class="border border-border px-4 py-2">Org settings, allow‑lists, rules files, modes, dashboards</td><td class="border border-border px-4 py-2">You / Extension (when signed in)</td></tr><tr><td class="border border-border px-4 py-2 font-medium">Prompts, Chat Snippets &amp; Token Counts</td><td class="border border-border px-4 py-2">Text prompts, model outputs, token counts</td><td class="border border-border px-4 py-2">Extension (when signed in)</td></tr><tr class="bg-muted/25"><td class="border border-border px-4 py-2 font-medium">Usage Data</td><td class="border border-border px-4 py-2">Feature clicks, error logs, performance metrics (captured via PostHog)</td><td class="border border-border px-4 py-2">Services automatically (PostHog)</td></tr><tr><td class="border border-border px-4 py-2 font-medium">Payment Data</td><td class="border border-border px-4 py-2">Tokenized card details, billing address, invoices</td><td class="border border-border px-4 py-2">Payment processor (Stripe)</td></tr><tr class="bg-muted/25"><td class="border border-border px-4 py-2 font-medium">Marketing Data</td><td class="border border-border px-4 py-2">Cookies, IP address, browser type, page views,<!-- --> <strong>voluntary form submissions</strong> (e.g., newsletter or wait‑list sign‑ups)</td><td class="border border-border px-4 py-2">Marketing Site automatically / You</td></tr></tbody></table></div><h2 class="mt-12 text-2xl font-bold">2. How We Use Information</h2><ul><li><strong>Operate &amp; secure Roo Code Cloud</strong> (authentication, completions, abuse prevention)</li><li><strong>Provide support &amp; improve features</strong> (debugging, analytics, product decisions)</li><li><strong>Process payments &amp; manage subscriptions</strong></li><li><strong>Send product updates and roadmap communications</strong> (opt‑out available)</li></ul><h2 class="mt-12 text-2xl font-bold">3. Where Your Data Goes (And Doesn&#x27;t)</h2><div class="overflow-x-auto"><table class="min-w-full border-collapse border border-border"><thead><tr class="bg-muted/50"><th class="border border-border px-4 py-2 text-left font-semibold">Data</th><th class="border border-border px-4 py-2 text-left font-semibold">Sent To</th><th class="border border-border px-4 py-2 text-left font-semibold"><strong>Not</strong> Sent To</th></tr></thead><tbody><tr><td class="border border-border px-4 py-2 font-medium">Code &amp; files you work on</td><td class="border border-border px-4 py-2">Your chosen model provider (direct client → provider TLS)</td><td class="border border-border px-4 py-2">Roo Code servers; ad networks; model‑training pipelines</td></tr><tr class="bg-muted/25"><td class="border border-border px-4 py-2 font-medium">Prompts, chat snippets &amp; token counts (Cloud)</td><td class="border border-border px-4 py-2">Roo Code Cloud (encrypted at rest)</td><td class="border border-border px-4 py-2">Any third‑party</td></tr><tr><td class="border border-border px-4 py-2 font-medium">Workspace Configuration</td><td class="border border-border px-4 py-2">Roo Code Cloud (encrypted at rest)</td><td class="border border-border px-4 py-2">Any third-party</td></tr><tr class="bg-muted/25"><td class="border border-border px-4 py-2 font-medium">Usage &amp; Telemetry</td><td class="border border-border px-4 py-2">PostHog (self‑hosted analytics platform)</td><td class="border border-border px-4 py-2">Ad networks or data brokers</td></tr><tr><td class="border border-border px-4 py-2 font-medium">Payment Data</td><td class="border border-border px-4 py-2">Stripe (PCI‑DSS Level 1)</td><td class="border border-border px-4 py-2">Roo Code servers (we store only the Stripe customer ID)</td></tr></tbody></table></div><h2 class="mt-12 text-2xl font-bold">4. Data Retention</h2><ul><li><strong>Source Code:</strong> Never stored on Roo Code servers.</li><li><strong>Prompts &amp; Chat Snippets:</strong> Persist in your Cloud workspace until you or your organization admin deletes them or disables collection.</li><li><strong>Operational Logs &amp; Analytics:</strong> Retained only as needed to operate and secure Roo Code Cloud.</li></ul><h2 class="mt-12 text-2xl font-bold">5. Your Choices</h2><ul><li><strong>Manage cookies:</strong> You can block or delete cookies in your browser settings; some site features may not function without them.</li><li><strong>Disable prompt collection</strong> in Organization settings.</li><li><strong>Delete your Cloud account</strong> at any time from<!-- --> <strong>Security Settings</strong> inside Roo Code Cloud.</li></ul><h2 class="mt-12 text-2xl font-bold">6. Security Practices</h2><p>We use TLS for all data in transit, AES‑256 encryption at rest, least‑privilege IAM, continuous monitoring, routine penetration testing, and maintain a SOC 2 program.</p><h2 class="mt-12 text-2xl font-bold">7. Updates to This Policy</h2><p>If our privacy practices change, we will update this policy and note the new<!-- --> <strong>Last Updated</strong> date at the top. For material changes that affect Cloud workspaces, we will also email registered workspace owners before the changes take effect.</p><h2 class="mt-12 text-2xl font-bold">8. Contact Us</h2><p>Questions or concerns? Email<!-- --> <a href="mailto:<EMAIL>" class="text-primary hover:underline"><EMAIL></a>.</p></div></div></main><footer class="border-t border-border bg-background"><div class="mx-auto max-w-7xl px-6 pb-6 pt-12 md:pb-8 md:pt-16 lg:px-8"><div class="xl:grid xl:grid-cols-3 xl:gap-8"><div class="space-y-8"><div class="flex items-center"><img alt="Roo Code Logo" loading="lazy" width="120" height="40" decoding="async" data-nimg="1" class="h-6 w-auto" style="color:transparent" src="/Roo-Code-Logo-Horiz-white.svg"/></div><p class="max-w-md text-sm leading-6 text-muted-foreground md:pr-16 lg:pr-32">Empowering developers to build better software faster with AI-powered tools and insights.</p><div class="flex space-x-4"><a href="https://github.com/RooCodeInc/Roo-Code" target="_blank" rel="noopener noreferrer" class="text-muted-foreground transition-colors hover:text-foreground"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 496 512" class="h-6 w-6" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6zm-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3zm44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9zM244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8zM97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1zm-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7zm32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1zm-11.4-14.7c-1.6 1-1.6 3.6 0 5.9 1.6 2.3 4.3 3.3 5.6 2.3 1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2z"></path></svg><span class="sr-only">GitHub</span></a><a href="https://discord.gg/roocode" target="_blank" rel="noopener noreferrer" class="text-muted-foreground transition-colors hover:text-foreground"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 640 512" class="h-6 w-6" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M524.531,69.836a1.5,1.5,0,0,0-.764-.7A485.065,485.065,0,0,0,404.081,32.03a1.816,1.816,0,0,0-1.923.91,337.461,337.461,0,0,0-14.9,30.6,447.848,447.848,0,0,0-134.426,0,309.541,309.541,0,0,0-15.135-30.6,1.89,1.89,0,0,0-1.924-.91A483.689,483.689,0,0,0,116.085,69.137a1.712,1.712,0,0,0-.788.676C39.068,183.651,18.186,294.69,28.43,404.354a2.016,2.016,0,0,0,.765,1.375A487.666,487.666,0,0,0,176.02,479.918a1.9,1.9,0,0,0,2.063-.676A348.2,348.2,0,0,0,208.12,430.4a1.86,1.86,0,0,0-1.019-2.588,321.173,321.173,0,0,1-45.868-21.853,1.885,1.885,0,0,1-.185-3.126c3.082-2.309,6.166-4.711,9.109-7.137a1.819,1.819,0,0,1,1.9-.256c96.229,43.917,200.41,43.917,295.5,0a1.812,1.812,0,0,1,1.924.233c2.944,2.426,6.027,4.851,9.132,7.16a1.884,1.884,0,0,1-.162,3.126,301.407,301.407,0,0,1-45.89,21.83,1.875,1.875,0,0,0-1,2.611,391.055,391.055,0,0,0,30.014,48.815,1.864,1.864,0,0,0,2.063.7A486.048,486.048,0,0,0,610.7,405.729a1.882,1.882,0,0,0,.765-1.352C623.729,277.594,590.933,167.465,524.531,69.836ZM222.491,337.58c-28.972,0-52.844-26.587-52.844-59.239S193.056,219.1,222.491,219.1c29.665,0,53.306,26.82,52.843,59.239C275.334,310.993,251.924,337.58,222.491,337.58Zm195.38,0c-28.971,0-52.843-26.587-52.843-59.239S388.437,219.1,417.871,219.1c29.667,0,53.307,26.82,52.844,59.239C470.715,310.993,447.538,337.58,417.871,337.58Z"></path></svg><span class="sr-only">Discord</span></a><a href="https://reddit.com/r/RooCode" target="_blank" rel="noopener noreferrer" class="text-muted-foreground transition-colors hover:text-foreground"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" class="h-6 w-6" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M0 256C0 114.6 114.6 0 256 0S512 114.6 512 256s-114.6 256-256 256L37.1 512c-13.7 0-20.5-16.5-10.9-26.2L75 437C28.7 390.7 0 326.7 0 256zM349.6 153.6c23.6 0 42.7-19.1 42.7-42.7s-19.1-42.7-42.7-42.7c-20.6 0-37.8 14.6-41.8 34c-34.5 3.7-61.4 33-61.4 68.4l0 .2c-37.5 1.6-71.8 12.3-99 29.1c-10.1-7.8-22.8-12.5-36.5-12.5c-33 0-59.8 26.8-59.8 59.8c0 24 14.1 44.6 34.4 54.1c2 69.4 77.6 125.2 170.6 125.2s168.7-55.9 170.6-125.3c20.2-9.6 34.1-30.2 34.1-54c0-33-26.8-59.8-59.8-59.8c-13.7 0-26.3 4.6-36.4 12.4c-27.4-17-62.1-27.7-100-29.1l0-.2c0-25.4 18.9-46.5 43.4-49.9l0 0c4.4 18.8 21.3 32.8 41.5 32.8zM177.1 246.9c16.7 0 29.5 17.6 28.5 39.3s-13.5 29.6-30.3 29.6s-31.4-8.8-30.4-30.5s15.4-38.3 32.1-38.3zm190.1 38.3c1 21.7-13.7 30.5-30.4 30.5s-29.3-7.9-30.3-29.6c-1-21.7 11.8-39.3 28.5-39.3s31.2 16.6 32.1 38.3zm-48.1 56.7c-10.3 24.6-34.6 41.9-63 41.9s-52.7-17.3-63-41.9c-1.2-2.9 .8-6.2 3.9-6.5c18.4-1.9 38.3-2.9 59.1-2.9s40.7 1 59.1 2.9c3.1 .3 5.1 3.6 3.9 6.5z"></path></svg><span class="sr-only">Reddit</span></a><a href="https://x.com/roo_code" target="_blank" rel="noopener noreferrer" class="text-muted-foreground transition-colors hover:text-foreground"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" class="h-6 w-6" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M389.2 48h70.6L305.6 224.2 487 464H345L233.7 318.6 106.5 464H35.8L200.7 275.5 26.8 48H172.4L272.9 180.9 389.2 48zM364.4 421.8h39.1L151.1 88h-42L364.4 421.8z"></path></svg><span class="sr-only">X</span></a><a href="https://www.linkedin.com/company/roo-code" target="_blank" rel="noopener noreferrer" class="text-muted-foreground transition-colors hover:text-foreground"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 448 512" class="h-6 w-6" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M416 32H31.9C14.3 32 0 46.5 0 64.3v383.4C0 465.5 14.3 480 31.9 480H416c17.6 0 32-14.5 32-32.3V64.3c0-17.8-14.4-32.3-32-32.3zM135.4 416H69V202.2h66.5V416zm-33.2-243c-21.3 0-38.5-17.3-38.5-38.5S80.9 96 102.2 96c21.2 0 38.5 17.3 38.5 38.5 0 21.3-17.2 38.5-38.5 38.5zm282.1 243h-66.4V312c0-24.8-.5-56.7-34.5-56.7-34.6 0-39.9 27-39.9 54.9V416h-66.4V202.2h63.7v29.2h.9c8.9-16.8 30.6-34.5 62.9-34.5 67.2 0 79.7 44.3 79.7 101.9V416z"></path></svg><span class="sr-only">LinkedIn</span></a><a href="https://bsky.app/profile/roocode.bsky.social" target="_blank" rel="noopener noreferrer" class="text-muted-foreground transition-colors hover:text-foreground"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" class="h-6 w-6" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M407.8 294.7c-3.3-.4-6.7-.8-10-1.3c3.4 .4 6.7 .9 10 1.3zM288 227.1C261.9 176.4 190.9 81.9 124.9 35.3C61.6-9.4 37.5-1.7 21.6 5.5C3.3 13.8 0 41.9 0 58.4S9.1 194 15 213.9c19.5 65.7 89.1 87.9 153.2 80.7c3.3-.5 6.6-.9 10-1.4c-3.3 .5-6.6 1-10 1.4C74.3 308.6-9.1 342.8 100.3 464.5C220.6 589.1 265.1 437.8 288 361.1c22.9 76.7 49.2 222.5 185.6 103.4c102.4-103.4 28.1-156-65.8-169.9c-3.3-.4-6.7-.8-10-1.3c3.4 .4 6.7 .9 10 1.3c64.1 7.1 133.6-15.1 153.2-80.7C566.9 194 576 75 576 58.4s-3.3-44.7-21.6-52.9c-15.8-7.1-40-14.9-103.2 29.8C385.1 81.9 314.1 176.4 288 227.1z"></path></svg><span class="sr-only">Bluesky</span></a><a href="https://www.tiktok.com/@roo.code" target="_blank" rel="noopener noreferrer" class="text-muted-foreground transition-colors hover:text-foreground"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 448 512" class="h-6 w-6" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M448,209.91a210.06,210.06,0,0,1-122.77-39.25V349.38A162.55,162.55,0,1,1,185,188.31V278.2a74.62,74.62,0,1,0,52.23,71.18V0l88,0a121.18,121.18,0,0,0,1.86,22.17h0A122.18,122.18,0,0,0,381,102.39a121.43,121.43,0,0,0,67,20.14Z"></path></svg><span class="sr-only">TikTok</span></a><a href="https://www.youtube.com/@RooCodeYT" target="_blank" rel="noopener noreferrer" class="text-muted-foreground transition-colors hover:text-foreground"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" class="h-6 w-6" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M549.655 124.083c-6.281-23.65-24.787-42.276-48.284-48.597C458.781 64 288 64 288 64S117.22 64 74.629 75.486c-23.497 6.322-42.003 24.947-48.284 48.597-11.412 42.867-11.412 132.305-11.412 132.305s0 89.438 11.412 132.305c6.281 23.65 24.787 41.5 48.284 47.821C117.22 448 288 448 288 448s170.78 0 213.371-11.486c23.497-6.321 42.003-24.171 48.284-47.821 11.412-42.867 11.412-132.305 11.412-132.305s0-89.438-11.412-132.305zm-317.51 213.508V175.185l142.739 81.205-142.739 81.201z"></path></svg><span class="sr-only">YouTube</span></a></div></div><div class="mt-16 grid grid-cols-2 gap-8 xl:col-span-2 xl:mt-0"><div class="md:grid md:grid-cols-2 md:gap-8"><div><h3 class="text-sm font-semibold uppercase leading-6 text-foreground">Product</h3><ul class="mt-6 space-y-4"><li><button class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Features</button></li><li><a class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground" href="/enterprise">Enterprise</a></li><li><a href="https://trust.roocode.com" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Security</a></li><li><button class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Testimonials</button></li><li><a href="https://docs.roocode.com/community" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Integrations</a></li><li><a href="https://github.com/RooCodeInc/Roo-Code/blob/main/CHANGELOG.md" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Changelog</a></li></ul></div><div class="mt-10 md:mt-0"><h3 class="text-sm font-semibold uppercase leading-6 text-foreground">Resources</h3><ul class="mt-6 space-y-4"><li><a href="https://docs.roocode.com" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Documentation</a></li><li><a href="https://docs.roocode.com/tutorial-videos" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Tutorials</a></li><li><a href="https://github.com/RooCodeInc/Roo-Code/discussions" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Community</a></li><li><a href="https://discord.gg/roocode" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Discord</a></li><li><a href="https://reddit.com/r/RooCode" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Reddit</a></li></ul></div></div><div class="md:grid md:grid-cols-2 md:gap-8"><div><h3 class="text-sm font-semibold uppercase leading-6 text-foreground">Support</h3><ul class="mt-6 space-y-4"><li><a href="https://github.com/RooCodeInc/Roo-Code/issues" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Issues</a></li><li><a href="https://github.com/RooCodeInc/Roo-Code/discussions/categories/feature-requests" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Feature Requests</a></li><li><button class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">FAQ</button></li></ul></div><div class="mt-10 md:mt-0"><h3 class="text-sm font-semibold uppercase leading-6 text-foreground">Company</h3><ul class="mt-6 space-y-4"><li><a href="mailto:<EMAIL>" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Contact</a></li><li><a href="https://careers.roocode.com" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Careers</a></li><li><a class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground" href="/terms">Terms of Service</a></li><li><div class="relative z-10"><button class="flex items-center text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground" aria-expanded="false" aria-haspopup="true"><span>Privacy <span class="max-[320px]:hidden">Policy</span></span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down ml-1 h-4 w-4 transition-transform" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></div></li></ul></div></div></div></div><div class="mt-16 flex border-t border-border pt-8 sm:mt-20 lg:mt-24"><p class="mx-auto text-sm leading-5 text-muted-foreground">© <!-- -->2025<!-- --> Roo Code. All rights reserved.</p></div></div></footer></div><script src="/_next/static/chunks/webpack-c8e217c742f0746a.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[80983,[\"858\",\"static/chunks/aba955ec-9833060e0c799abe.js\",\"840\",\"static/chunks/e5c963a3-f3448fec3e69041a.js\",\"777\",\"static/chunks/88e8e2ac-7a1619a6e98006b4.js\",\"798\",\"static/chunks/2ae967de-afd0847aa04941df.js\",\"180\",\"static/chunks/dd0ec533-85265a862baf3739.js\",\"872\",\"static/chunks/5d6f4545-30a8b6f9fd887cd0.js\",\"604\",\"static/chunks/604-37da8378c60f8591.js\",\"156\",\"static/chunks/156-01d88aff08204544.js\",\"657\",\"static/chunks/657-9a922aaadb9c4bb2.js\",\"954\",\"static/chunks/954-09ef0ac74eac9fe3.js\",\"554\",\"static/chunks/554-3f07505b777a310d.js\",\"177\",\"static/chunks/app/layout-70c3bb938d5edbac.js\"],\"\"]\n3:I[39183,[\"858\",\"static/chunks/aba955ec-9833060e0c799abe.js\",\"840\",\"static/chunks/e5c963a3-f3448fec3e69041a.js\",\"777\",\"static/chunks/88e8e2ac-7a1619a6e98006b4.js\",\"798\",\"static/chunks/2ae967de-afd0847aa04941df.js\",\"180\",\"static/chunks/dd0ec533-85265a862baf3739.js\",\"872\",\"static/chunks/5d6f4545-30a8b6f9fd887cd0.js\",\"604\",\"static/chunks/604-37da8378c60f8591.js\",\"156\",\"static/chunks/156-01d88aff08204544.js\",\"657\",\"static/chunks/657-9a922aaadb9c4bb2.js\",\"954\",\"static/chunks/954-09ef0ac74eac9fe3.js\",\"554\",\"static/chunks/554-3f07505b777a310d.js\",\"177\",\"static/chunks/app/layout-70c3bb938d5edbac.js\"],\"Providers\"]\n5:I[95823,[],\"\"]\n6:I[20531,[],\"\"]\n7:I[6445,[],\"OutletBoundary\"]\na:I[6445,[],\"ViewportBoundary\"]\nc:I[6445,[],\"MetadataBoundary\"]\ne:I[38826,[],\"\"]\n:HL[\"/_next/static/css/c16eb50558afbc00.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"Hrz9dGjjQ4zbSgAqNHnYG\",\"p\":\"\",\"c\":[\"\",\"privacy\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"privacy\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/c16eb50558afbc00.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"suppressHydrationWarning\":true,\"children\":[[\"$\",\"head\",null,{\"children\":[\"$\",\"link\",null,{\"rel\":\"stylesheet\",\"type\":\"text/css\",\"href\":\"https://cdn.jsdelivr.net/gh/devicons/devicon@latest/devicon.min.css\"}]}],[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[[\"$\",\"$L2\",null,{\"src\":\"https://www.googletagmanager.com/gtag/js?id=AW-17391954825\",\"strategy\":\"afterInteractive\"}],[\"$\",\"$L2\",null,{\"id\":\"google-analytics\",\"strategy\":\"afterInteractive\",\"children\":\"\\n\\t\\t\\t\\t\\t\\twindow.dataLayer = window.dataLayer || [];\\n\\t\\t\\t\\t\\t\\tfunction gtag(){dataLayer.push(arguments);}\\n\\t\\t\\t\\t\\t\\tgtag('js', new Date());\\n\\t\\t\\t\\t\\t\\tgtag('config', 'AW-17391954825');\\n\\t\\t\\t\\t\\t\"}],[\"$\",\"div\",null,{\"itemScope\":true,\"itemType\":\"https://schema.org/WebSite\",\"children\":[[\"$\",\"link\",null,{\"itemProp\":\"url\",\"href\":\"https://roocode.com\"}],[\"$\",\"meta\",null,{\"itemProp\":\"name\",\"content\":\"Roo Code\"}]]}],[\"$\",\"$L3\",null,{\"children\":\"$L4\"}]]}]]}]]}],{\"children\":[\"privacy\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L5\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L6\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"div\",null,{\"className\":\"container mx-auto px-4 py-12 sm:px-6 lg:px-8\",\"children\":[\"$\",\"div\",null,{\"className\":\"prose prose-lg mx-auto max-w-4xl dark:prose-invert\",\"children\":[[\"$\",\"h1\",null,{\"className\":\"text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl\",\"children\":\"Roo Code Cloud Privacy Policy\"}],[\"$\",\"p\",null,{\"className\":\"text-muted-foreground\",\"children\":\"Last Updated: June 19, 2025\"}],[\"$\",\"p\",null,{\"className\":\"lead\",\"children\":\"This Privacy Policy explains how Roo Code, Inc. (\\\"Roo Code,\\\" \\\"we,\\\" \\\"our,\\\" or \\\"us\\\") collects, uses, and shares information when you:\"}],[\"$\",\"ul\",null,{\"className\":\"lead\",\"children\":[[\"$\",\"li\",null,{\"children\":[\"browse any page under \",[\"$\",\"strong\",null,{\"children\":\"roocode.com\"}],\" (the \",[\"$\",\"em\",null,{\"children\":\"Marketing Site\"}],\"); and/or\"]}],[\"$\",\"li\",null,{\"children\":[\"create an account for, sign in to, or otherwise use \",[\"$\",\"strong\",null,{\"children\":\"Roo Code Cloud\"}],\" at\",\" \",[\"$\",\"strong\",null,{\"children\":\"app.roocode.com\"}],\" or through the Roo Code extension while authenticated to that Cloud account (the \",[\"$\",\"em\",null,{\"children\":\"Cloud Service\"}],\").\"]}]]}],[\"$\",\"div\",null,{\"className\":\"my-8 rounded-lg border border-border bg-muted/50 p-6\",\"children\":[[\"$\",\"h3\",null,{\"className\":\"mt-0 text-lg font-semibold\",\"children\":\"Extension‑Only Usage\"}],[\"$\",\"p\",null,{\"className\":\"mb-0\",\"children\":[\"If you run the Roo Code extension \",[\"$\",\"strong\",null,{\"children\":\"without\"}],\" connecting to a Cloud account, your data is governed by the standalone\",\" \",[\"$\",\"a\",null,{\"href\":\"https://github.com/RooCodeInc/Roo-Code/blob/main/PRIVACY.md\",\"target\":\"_blank\",\"rel\":\"noopener noreferrer\",\"className\":\"text-primary hover:underline\",\"children\":\"Roo Code Extension Privacy Policy\"}],\".\"]}]]}],[\"$\",\"h2\",null,{\"className\":\"mt-12 text-2xl font-bold\",\"children\":\"Quick Summary\"}],[\"$\",\"ul\",null,{\"children\":[[\"$\",\"li\",null,{\"children\":[[\"$\",\"strong\",null,{\"children\":\"Your source code never transits Roo Code servers.\"}],\" It stays on your device and is sent \",[\"$\",\"strong\",null,{\"children\":\"directly\"}],\"—via a client‑to‑provider TLS connection—to the third‑party AI model you select. Roo Code never stores, inspects, or trains on your code.\"]}],[\"$\",\"li\",null,{\"children\":[[\"$\",\"strong\",null,{\"children\":\"Prompts and chat snippets are collected by default\"}],\" in Roo Code Cloud so you can search and re‑use past conversations. Organization admins can disable this collection at any time.\"]}],[\"$\",\"li\",null,{\"children\":[\"We collect only the data needed to operate Roo Code Cloud, do \",[\"$\",\"strong\",null,{\"children\":\"not\"}],\" sell customer data, and do \",[\"$\",\"strong\",null,{\"children\":\"not\"}],\" use your content to train models.\"]}]]}],[\"$\",\"h2\",null,{\"className\":\"mt-12 text-2xl font-bold\",\"children\":\"1. Information We Collect\"}],[\"$\",\"div\",null,{\"className\":\"overflow-x-auto\",\"children\":[\"$\",\"table\",null,{\"className\":\"min-w-full border-collapse border border-border\",\"children\":[[\"$\",\"thead\",null,{\"children\":[\"$\",\"tr\",null,{\"className\":\"bg-muted/50\",\"children\":[[\"$\",\"th\",null,{\"className\":\"border border-border px-4 py-2 text-left font-semibold\",\"children\":\"Category\"}],[\"$\",\"th\",null,{\"className\":\"border border-border px-4 py-2 text-left font-semibold\",\"children\":\"Examples\"}],[\"$\",\"th\",null,{\"className\":\"border border-border px-4 py-2 text-left font-semibold\",\"children\":\"Source\"}]]}]}],[\"$\",\"tbody\",null,{\"children\":[[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"className\":\"border border-border px-4 py-2 font-medium\",\"children\":\"Account Information\"}],[\"$\",\"td\",null,{\"className\":\"border border-border px-4 py-2\",\"children\":\"Name, email, organization, auth tokens\"}],[\"$\",\"td\",null,{\"className\":\"border border-border px-4 py-2\",\"children\":\"You\"}]]}],[\"$\",\"tr\",null,{\"className\":\"bg-muted/25\",\"children\":[[\"$\",\"td\",null,{\"className\":\"border border-border px-4 py-2 font-medium\",\"children\":\"Workspace Configuration\"}],[\"$\",\"td\",null,{\"className\":\"border border-border px-4 py-2\",\"children\":\"Org settings, allow‑lists, rules files, modes, dashboards\"}],[\"$\",\"td\",null,{\"className\":\"border border-border px-4 py-2\",\"children\":\"You / Extension (when signed in)\"}]]}],[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"className\":\"border border-border px-4 py-2 font-medium\",\"children\":\"Prompts, Chat Snippets \u0026 Token Counts\"}],[\"$\",\"td\",null,{\"className\":\"border border-border px-4 py-2\",\"children\":\"Text prompts, model outputs, token counts\"}],[\"$\",\"td\",null,{\"className\":\"border border-border px-4 py-2\",\"children\":\"Extension (when signed in)\"}]]}],[\"$\",\"tr\",null,{\"className\":\"bg-muted/25\",\"children\":[[\"$\",\"td\",null,{\"className\":\"border border-border px-4 py-2 font-medium\",\"children\":\"Usage Data\"}],[\"$\",\"td\",null,{\"className\":\"border border-border px-4 py-2\",\"children\":\"Feature clicks, error logs, performance metrics (captured via PostHog)\"}],[\"$\",\"td\",null,{\"className\":\"border border-border px-4 py-2\",\"children\":\"Services automatically (PostHog)\"}]]}],[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"className\":\"border border-border px-4 py-2 font-medium\",\"children\":\"Payment Data\"}],[\"$\",\"td\",null,{\"className\":\"border border-border px-4 py-2\",\"children\":\"Tokenized card details, billing address, invoices\"}],[\"$\",\"td\",null,{\"className\":\"border border-border px-4 py-2\",\"children\":\"Payment processor (Stripe)\"}]]}],[\"$\",\"tr\",null,{\"className\":\"bg-muted/25\",\"children\":[[\"$\",\"td\",null,{\"className\":\"border border-border px-4 py-2 font-medium\",\"children\":\"Marketing Data\"}],[\"$\",\"td\",null,{\"className\":\"border border-border px-4 py-2\",\"children\":[\"Cookies, IP address, browser type, page views,\",\" \",[\"$\",\"strong\",null,{\"children\":\"voluntary form submissions\"}],\" (e.g., newsletter or wait‑list sign‑ups)\"]}],[\"$\",\"td\",null,{\"className\":\"border border-border px-4 py-2\",\"children\":\"Marketing Site automatically / You\"}]]}]]}]]}]}],[\"$\",\"h2\",null,{\"className\":\"mt-12 text-2xl font-bold\",\"children\":\"2. How We Use Information\"}],[\"$\",\"ul\",null,{\"children\":[[\"$\",\"li\",null,{\"children\":[[\"$\",\"strong\",null,{\"children\":\"Operate \u0026 secure Roo Code Cloud\"}],\" (authentication, completions, abuse prevention)\"]}],[\"$\",\"li\",null,{\"children\":[[\"$\",\"strong\",null,{\"children\":\"Provide support \u0026 improve features\"}],\" (debugging, analytics, product decisions)\"]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"strong\",null,{\"children\":\"Process payments \u0026 manage subscriptions\"}]}],[\"$\",\"li\",null,{\"children\":[[\"$\",\"strong\",null,{\"children\":\"Send product updates and roadmap communications\"}],\" (opt‑out available)\"]}]]}],[\"$\",\"h2\",null,{\"className\":\"mt-12 text-2xl font-bold\",\"children\":\"3. Where Your Data Goes (And Doesn't)\"}],[\"$\",\"div\",null,{\"className\":\"overflow-x-auto\",\"children\":[\"$\",\"table\",null,{\"className\":\"min-w-full border-collapse border border-border\",\"children\":[[\"$\",\"thead\",null,{\"children\":[\"$\",\"tr\",null,{\"className\":\"bg-muted/50\",\"children\":[[\"$\",\"th\",null,{\"className\":\"border border-border px-4 py-2 text-left font-semibold\",\"children\":\"Data\"}],[\"$\",\"th\",null,{\"className\":\"border border-border px-4 py-2 text-left font-semibold\",\"children\":\"Sent To\"}],[\"$\",\"th\",null,{\"className\":\"border border-border px-4 py-2 text-left font-semibold\",\"children\":[[\"$\",\"strong\",null,{\"children\":\"Not\"}],\" Sent To\"]}]]}]}],[\"$\",\"tbody\",null,{\"children\":[[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"className\":\"border border-border px-4 py-2 font-medium\",\"children\":\"Code \u0026 files you work on\"}],[\"$\",\"td\",null,{\"className\":\"border border-border px-4 py-2\",\"children\":\"Your chosen model provider (direct client → provider TLS)\"}],[\"$\",\"td\",null,{\"className\":\"border border-border px-4 py-2\",\"children\":\"Roo Code servers; ad networks; model‑training pipelines\"}]]}],[\"$\",\"tr\",null,{\"className\":\"bg-muted/25\",\"children\":[[\"$\",\"td\",null,{\"className\":\"border border-border px-4 py-2 font-medium\",\"children\":\"Prompts, chat snippets \u0026 token counts (Cloud)\"}],[\"$\",\"td\",null,{\"className\":\"border border-border px-4 py-2\",\"children\":\"Roo Code Cloud (encrypted at rest)\"}],[\"$\",\"td\",null,{\"className\":\"border border-border px-4 py-2\",\"children\":\"Any third‑party\"}]]}],[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"className\":\"border border-border px-4 py-2 font-medium\",\"children\":\"Workspace Configuration\"}],[\"$\",\"td\",null,{\"className\":\"border border-border px-4 py-2\",\"children\":\"Roo Code Cloud (encrypted at rest)\"}],[\"$\",\"td\",null,{\"className\":\"border border-border px-4 py-2\",\"children\":\"Any third-party\"}]]}],[\"$\",\"tr\",null,{\"className\":\"bg-muted/25\",\"children\":[[\"$\",\"td\",null,{\"className\":\"border border-border px-4 py-2 font-medium\",\"children\":\"Usage \u0026 Telemetry\"}],[\"$\",\"td\",null,{\"className\":\"border border-border px-4 py-2\",\"children\":\"PostHog (self‑hosted analytics platform)\"}],[\"$\",\"td\",null,{\"className\":\"border border-border px-4 py-2\",\"children\":\"Ad networks or data brokers\"}]]}],[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"className\":\"border border-border px-4 py-2 font-medium\",\"children\":\"Payment Data\"}],[\"$\",\"td\",null,{\"className\":\"border border-border px-4 py-2\",\"children\":\"Stripe (PCI‑DSS Level 1)\"}],[\"$\",\"td\",null,{\"className\":\"border border-border px-4 py-2\",\"children\":\"Roo Code servers (we store only the Stripe customer ID)\"}]]}]]}]]}]}],[\"$\",\"h2\",null,{\"className\":\"mt-12 text-2xl font-bold\",\"children\":\"4. Data Retention\"}],[\"$\",\"ul\",null,{\"children\":[[\"$\",\"li\",null,{\"children\":[[\"$\",\"strong\",null,{\"children\":\"Source Code:\"}],\" Never stored on Roo Code servers.\"]}],[\"$\",\"li\",null,{\"children\":[[\"$\",\"strong\",null,{\"children\":\"Prompts \u0026 Chat Snippets:\"}],\" Persist in your Cloud workspace until you or your organization admin deletes them or disables collection.\"]}],[\"$\",\"li\",null,{\"children\":[[\"$\",\"strong\",null,{\"children\":\"Operational Logs \u0026 Analytics:\"}],\" Retained only as needed to operate and secure Roo Code Cloud.\"]}]]}],[\"$\",\"h2\",null,{\"className\":\"mt-12 text-2xl font-bold\",\"children\":\"5. Your Choices\"}],[\"$\",\"ul\",null,{\"children\":[[\"$\",\"li\",null,{\"children\":[[\"$\",\"strong\",null,{\"children\":\"Manage cookies:\"}],\" You can block or delete cookies in your browser settings; some site features may not function without them.\"]}],[\"$\",\"li\",null,{\"children\":[[\"$\",\"strong\",null,{\"children\":\"Disable prompt collection\"}],\" in Organization settings.\"]}],[\"$\",\"li\",null,{\"children\":[[\"$\",\"strong\",null,{\"children\":\"Delete your Cloud account\"}],\" at any time from\",\" \",[\"$\",\"strong\",null,{\"children\":\"Security Settings\"}],\" inside Roo Code Cloud.\"]}]]}],[\"$\",\"h2\",null,{\"className\":\"mt-12 text-2xl font-bold\",\"children\":\"6. Security Practices\"}],[\"$\",\"p\",null,{\"children\":\"We use TLS for all data in transit, AES‑256 encryption at rest, least‑privilege IAM, continuous monitoring, routine penetration testing, and maintain a SOC 2 program.\"}],[\"$\",\"h2\",null,{\"className\":\"mt-12 text-2xl font-bold\",\"children\":\"7. Updates to This Policy\"}],[\"$\",\"p\",null,{\"children\":[\"If our privacy practices change, we will update this policy and note the new\",\" \",[\"$\",\"strong\",null,{\"children\":\"Last Updated\"}],\" date at the top. For material changes that affect Cloud workspaces, we will also email registered workspace owners before the changes take effect.\"]}],[\"$\",\"h2\",null,{\"className\":\"mt-12 text-2xl font-bold\",\"children\":\"8. Contact Us\"}],[\"$\",\"p\",null,{\"children\":[\"Questions or concerns? Email\",\" \",[\"$\",\"a\",null,{\"href\":\"mailto:<EMAIL>\",\"className\":\"text-primary hover:underline\",\"children\":\"<EMAIL>\"}],\".\"]}]]}]}],\"$undefined\",null,[\"$\",\"$L7\",null,{\"children\":[\"$L8\",\"$L9\",null]}]]}],{},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"PtIHHJgblQy3WfHjn71Zr\",{\"children\":[[\"$\",\"$La\",null,{\"children\":\"$Lb\"}],null]}],[\"$\",\"$Lc\",null,{\"children\":\"$Ld\"}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$e\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"b:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\n8:null\n"])</script><script>self.__next_f.push([1,"9:null\nd:[[\"$\",\"title\",\"0\",{\"children\":\"Privacy Policy - Roo Code\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Privacy policy for Roo Code Cloud and marketing website. Learn how we handle your data and protect your privacy.\"}],[\"$\",\"link\",\"2\",{\"rel\":\"canonical\",\"href\":\"https://roocode.com\"}],[\"$\",\"link\",\"3\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\"}],[\"$\",\"link\",\"4\",{\"rel\":\"icon\",\"href\":\"/favicon-16x16.png\",\"sizes\":\"16x16\",\"type\":\"image/png\"}],[\"$\",\"link\",\"5\",{\"rel\":\"icon\",\"href\":\"/favicon-32x32.png\",\"sizes\":\"32x32\",\"type\":\"image/png\"}],[\"$\",\"link\",\"6\",{\"rel\":\"apple-touch-icon\",\"href\":\"/apple-touch-icon.png\"}],[\"$\",\"link\",\"7\",{\"rel\":\"android-chrome-192x192\",\"href\":\"/android-chrome-192x192.png\",\"sizes\":\"192x192\",\"type\":\"image/png\"}],[\"$\",\"link\",\"8\",{\"rel\":\"android-chrome-512x512\",\"href\":\"/android-chrome-512x512.png\",\"sizes\":\"512x512\",\"type\":\"image/png\"}]]\n"])</script><script>self.__next_f.push([1,"f:I[91095,[\"858\",\"static/chunks/aba955ec-9833060e0c799abe.js\",\"840\",\"static/chunks/e5c963a3-f3448fec3e69041a.js\",\"777\",\"static/chunks/88e8e2ac-7a1619a6e98006b4.js\",\"798\",\"static/chunks/2ae967de-afd0847aa04941df.js\",\"180\",\"static/chunks/dd0ec533-85265a862baf3739.js\",\"872\",\"static/chunks/5d6f4545-30a8b6f9fd887cd0.js\",\"604\",\"static/chunks/604-37da8378c60f8591.js\",\"156\",\"static/chunks/156-01d88aff08204544.js\",\"657\",\"static/chunks/657-9a922aaadb9c4bb2.js\",\"954\",\"static/chunks/954-09ef0ac74eac9fe3.js\",\"554\",\"static/chunks/554-3f07505b777a310d.js\",\"177\",\"static/chunks/app/layout-70c3bb938d5edbac.js\"],\"NavBar\"]\n10:I[29685,[\"858\",\"static/chunks/aba955ec-9833060e0c799abe.js\",\"840\",\"static/chunks/e5c963a3-f3448fec3e69041a.js\",\"777\",\"static/chunks/88e8e2ac-7a1619a6e98006b4.js\",\"798\",\"static/chunks/2ae967de-afd0847aa04941df.js\",\"180\",\"static/chunks/dd0ec533-85265a862baf3739.js\",\"872\",\"static/chunks/5d6f4545-30a8b6f9fd887cd0.js\",\"604\",\"static/chunks/604-37da8378c60f8591.js\",\"156\",\"static/chunks/156-01d88aff08204544.js\",\"657\",\"static/chunks/657-9a922aaadb9c4bb2.js\",\"954\",\"static/chunks/954-09ef0ac74eac9fe3.js\",\"554\",\"static/chunks/554-3f07505b777a310d.js\",\"177\",\"static/chunks/app/layout-70c3bb938d5edbac.js\"],\"Footer\"]\n4:[\"$\",\"div\",null,{\"className\":\"flex min-h-screen flex-col bg-background text-foreground\",\"children\":[[\"$\",\"$Lf\",null,{\"stars\":\"17.6k\",\"downloads\":\"702.6k\"}],[\"$\",\"main\",null,{\"className\":\"flex-1\",\"children\":[\"$\",\"$L5\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L6\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style"])</script><script>self.__next_f.push([1,"\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}],[\"$\",\"$L10\",null,{}]]}]\n"])</script></body></html>