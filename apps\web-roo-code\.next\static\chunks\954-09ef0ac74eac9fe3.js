(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[954],{8198:(e,t,r)=>{"use strict";r.d(t,{so:()=>a});var n=r(39582),i=r(34545),s=(0,i.createContext)({client:n.Ay});function a(e){var t=e.children,r=e.client,a=e.apiKey,o=e.options,u=(0,i.useRef)(null),l=(0,i.useMemo)(function(){return r?(a&&console.warn("[PostHog.js] You have provided both `client` and `apiKey` to `PostHogProvider`. `api<PERSON>ey` will be ignored in favour of `client`."),o&&console.warn("[PostHog.js] You have provided both `client` and `options` to `PostHogProvider`. `options` will be ignored in favour of `client`."),r):(a||console.warn("[PostHog.js] No `apiKey` or `client` were provided to `PostHogProvider`. Using default global `window.posthog` instance. You must initialize it manually. This is not recommended behavior."),n.Ay)},[r,a,JSON.stringify(o)]);return(0,i.useEffect)(function(){if(!r){var e=u.current;e?(a!==e.apiKey&&console.warn("[PostHog.js] You have provided a different `apiKey` to `PostHogProvider` than the one that was already initialized. This is not supported by our provider and we'll keep using the previous key. If you need to toggle between API Keys you need to control the `client` yourself and pass it in as a prop rather than an `apiKey` prop."),o&&!function e(t,r,n){if(void 0===n&&(n=new WeakMap),t===r)return!0;if("object"!=typeof t||null===t||"object"!=typeof r||null===r)return!1;if(n.has(t)&&n.get(t)===r)return!0;n.set(t,r);var i=Object.keys(t),s=Object.keys(r);if(i.length!==s.length)return!1;for(var a=0;a<i.length;a++){var o=i[a];if(!s.includes(o)||!e(t[o],r[o],n))return!1}return!0}(o,e.options)&&n.Ay.set_config(o)):(n.Ay.__loaded&&console.warn("[PostHog.js] `posthog` was already loaded elsewhere. This may cause issues."),n.Ay.init(a,o)),u.current={apiKey:a,options:null!=o?o:{}}}},[r,a,JSON.stringify(o)]),i.createElement(s.Provider,{value:{client:l}},t)}var o=function(){return useContext(s).client},u=function(e,t){return(u=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},l=function(){return(l=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};function c(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,n=Object.getOwnPropertySymbols(e);i<n.length;i++)0>t.indexOf(n[i])&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(r[n[i]]=e[n[i]]);return r}var h=function(e){return"function"==typeof e};function d(e){var t=e.flag,r=e.children,n=e.onIntersect,i=e.onClick,s=e.trackView,a=e.options,u=c(e,["flag","children","onIntersect","onClick","trackView","options"]),h=useRef(null);return useEffect(function(){if(null!==h.current&&s){var e=new IntersectionObserver(function(e){return n(e[0])},l({threshold:.1},a));return e.observe(h.current),function(){return e.disconnect()}}},[t,a,o(),h,s,n]),React.createElement("div",l({ref:h},u,{onClick:i}),r)}var f={componentStack:null,error:null},p={INVALID_FALLBACK:"[PostHog.js][PostHogErrorBoundary] Invalid fallback prop, provide a valid React element or a function that returns a valid React element."};!function(e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function t(){this.constructor=r}function r(t){var r=e.call(this,t)||this;return r.state=f,r}u(r,e),r.prototype=null===e?Object.create(e):(t.prototype=e.prototype,new t),r.prototype.componentDidCatch=function(e,t){var r,n=t.componentStack,i=this.props.additionalProperties;this.setState({error:e,componentStack:n}),h(i)?r=i(e):"object"==typeof i&&(r=i),this.context.client.captureException(e,r)},r.prototype.render=function(){var e=this.props,t=e.children,r=e.fallback,n=this.state;if(null==n.componentStack)return h(t)?t():t;var s=h(r)?i.createElement(r,{error:n.error,componentStack:n.componentStack}):r;return i.isValidElement(s)?s:(console.warn(p.INVALID_FALLBACK),i.createElement(i.Fragment,null))},r.contextType=s}(i.Component)},39770:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cancelIdleCallback:function(){return n},requestIdleCallback:function(){return r}});let r="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},n="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},80890:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c"}},80983:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return b},handleClientScriptLoad:function(){return y},initScriptLoader:function(){return m}});let n=r(5932),i=r(63721),s=r(47093),a=n._(r(41076)),o=i._(r(34545)),u=r(99130),l=r(92614),c=r(39770),h=new Map,d=new Set,f=e=>{if(a.default.preinit){e.forEach(e=>{a.default.preinit(e,{as:"style"})});return}{let t=document.head;e.forEach(e=>{let r=document.createElement("link");r.type="text/css",r.rel="stylesheet",r.href=e,t.appendChild(r)})}},p=e=>{let{src:t,id:r,onLoad:n=()=>{},onReady:i=null,dangerouslySetInnerHTML:s,children:a="",strategy:o="afterInteractive",onError:u,stylesheets:c}=e,p=r||t;if(p&&d.has(p))return;if(h.has(t)){d.add(p),h.get(t).then(n,u);return}let y=()=>{i&&i(),d.add(p)},m=document.createElement("script"),g=new Promise((e,t)=>{m.addEventListener("load",function(t){e(),n&&n.call(this,t),y()}),m.addEventListener("error",function(e){t(e)})}).catch(function(e){u&&u(e)});s?(m.innerHTML=s.__html||"",y()):a?(m.textContent="string"==typeof a?a:Array.isArray(a)?a.join(""):"",y()):t&&(m.src=t,h.set(t,g)),(0,l.setAttributesFromProps)(m,e),"worker"===o&&m.setAttribute("type","text/partytown"),m.setAttribute("data-nscript",o),c&&f(c),document.body.appendChild(m)};function y(e){let{strategy:t="afterInteractive"}=e;"lazyOnload"===t?window.addEventListener("load",()=>{(0,c.requestIdleCallback)(()=>p(e))}):p(e)}function m(e){e.forEach(y),[...document.querySelectorAll('[data-nscript="beforeInteractive"]'),...document.querySelectorAll('[data-nscript="beforePageRender"]')].forEach(e=>{let t=e.id||e.getAttribute("src");d.add(t)})}function g(e){let{id:t,src:r="",onLoad:n=()=>{},onReady:i=null,strategy:l="afterInteractive",onError:h,stylesheets:f,...y}=e,{updateScripts:m,scripts:g,getIsSsr:b,appDir:v,nonce:O}=(0,o.useContext)(u.HeadManagerContext),C=(0,o.useRef)(!1);(0,o.useEffect)(()=>{let e=t||r;C.current||(i&&e&&d.has(e)&&i(),C.current=!0)},[i,t,r]);let w=(0,o.useRef)(!1);if((0,o.useEffect)(()=>{if(!w.current){if("afterInteractive"===l)p(e);else if("lazyOnload"===l)"complete"===document.readyState?(0,c.requestIdleCallback)(()=>p(e)):window.addEventListener("load",()=>{(0,c.requestIdleCallback)(()=>p(e))});w.current=!0}},[e,l]),("beforeInteractive"===l||"worker"===l)&&(m?(g[l]=(g[l]||[]).concat([{id:t,src:r,onLoad:n,onReady:i,onError:h,...y}]),m(g)):b&&b()?d.add(t||r):b&&!b()&&p(e)),v){if(f&&f.forEach(e=>{a.default.preinit(e,{as:"style"})}),"beforeInteractive"===l)if(!r)return y.dangerouslySetInnerHTML&&(y.children=y.dangerouslySetInnerHTML.__html,delete y.dangerouslySetInnerHTML),(0,s.jsx)("script",{nonce:O,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([0,{...y,id:t}])+")"}});else return a.default.preload(r,y.integrity?{as:"script",integrity:y.integrity,nonce:O,crossOrigin:y.crossOrigin}:{as:"script",nonce:O,crossOrigin:y.crossOrigin}),(0,s.jsx)("script",{nonce:O,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([r,{...y,id:t}])+")"}});"afterInteractive"===l&&r&&a.default.preload(r,y.integrity?{as:"script",integrity:y.integrity,nonce:O,crossOrigin:y.crossOrigin}:{as:"script",nonce:O,crossOrigin:y.crossOrigin})}return null}Object.defineProperty(g,"__nextScript",{value:!0});let b=g;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},83154:(e,t,r)=>{"use strict";r.d(t,{E:()=>g});var n=r(18490),i=r(85283),s=r(69303),a=r(23792),o=class extends a.Q{constructor(e={}){super(),this.config=e,this.#e=new Map}#e;build(e,t,r){let s=t.queryKey,a=t.queryHash??(0,n.F$)(s,t),o=this.get(a);return o||(o=new i.X({client:e,queryKey:s,queryHash:a,options:e.defaultQueryOptions(t),state:r,defaultOptions:e.getQueryDefaults(s)}),this.add(o)),o}add(e){this.#e.has(e.queryHash)||(this.#e.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){let t=this.#e.get(e.queryHash);t&&(e.destroy(),t===e&&this.#e.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){s.jG.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}get(e){return this.#e.get(e)}getAll(){return[...this.#e.values()]}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,n.MK)(t,e))}findAll(e={}){let t=this.getAll();return Object.keys(e).length>0?t.filter(t=>(0,n.MK)(e,t)):t}notify(e){s.jG.batch(()=>{this.listeners.forEach(t=>{t(e)})})}onFocus(){s.jG.batch(()=>{this.getAll().forEach(e=>{e.onFocus()})})}onOnline(){s.jG.batch(()=>{this.getAll().forEach(e=>{e.onOnline()})})}},u=r(44254),l=r(73598),c=class extends u.k{#t;#r;#n;constructor(e){super(),this.mutationId=e.mutationId,this.#r=e.mutationCache,this.#t=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#t.includes(e)||(this.#t.push(e),this.clearGcTimeout(),this.#r.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#t=this.#t.filter(t=>t!==e),this.scheduleGc(),this.#r.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#t.length||("pending"===this.state.status?this.scheduleGc():this.#r.remove(this))}continue(){return this.#n?.continue()??this.execute(this.state.variables)}async execute(e){let t=()=>{this.#i({type:"continue"})};this.#n=(0,l.II)({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(Error("No mutationFn found")),onFail:(e,t)=>{this.#i({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#i({type:"pause"})},onContinue:t,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#r.canRun(this)});let r="pending"===this.state.status,n=!this.#n.canStart();try{if(r)t();else{this.#i({type:"pending",variables:e,isPaused:n}),await this.#r.config.onMutate?.(e,this);let t=await this.options.onMutate?.(e);t!==this.state.context&&this.#i({type:"pending",context:t,variables:e,isPaused:n})}let i=await this.#n.start();return await this.#r.config.onSuccess?.(i,e,this.state.context,this),await this.options.onSuccess?.(i,e,this.state.context),await this.#r.config.onSettled?.(i,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(i,null,e,this.state.context),this.#i({type:"success",data:i}),i}catch(t){try{throw await this.#r.config.onError?.(t,e,this.state.context,this),await this.options.onError?.(t,e,this.state.context),await this.#r.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,t,e,this.state.context),t}finally{this.#i({type:"error",error:t})}}finally{this.#r.runNext(this)}}#i(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),s.jG.batch(()=>{this.#t.forEach(t=>{t.onMutationUpdate(e)}),this.#r.notify({mutation:this,type:"updated",action:e})})}},h=class extends a.Q{constructor(e={}){super(),this.config=e,this.#s=new Set,this.#a=new Map,this.#o=0}#s;#a;#o;build(e,t,r){let n=new c({mutationCache:this,mutationId:++this.#o,options:e.defaultMutationOptions(t),state:r});return this.add(n),n}add(e){this.#s.add(e);let t=d(e);if("string"==typeof t){let r=this.#a.get(t);r?r.push(e):this.#a.set(t,[e])}this.notify({type:"added",mutation:e})}remove(e){if(this.#s.delete(e)){let t=d(e);if("string"==typeof t){let r=this.#a.get(t);if(r)if(r.length>1){let t=r.indexOf(e);-1!==t&&r.splice(t,1)}else r[0]===e&&this.#a.delete(t)}}this.notify({type:"removed",mutation:e})}canRun(e){let t=d(e);if("string"!=typeof t)return!0;{let r=this.#a.get(t),n=r?.find(e=>"pending"===e.state.status);return!n||n===e}}runNext(e){let t=d(e);if("string"!=typeof t)return Promise.resolve();{let r=this.#a.get(t)?.find(t=>t!==e&&t.state.isPaused);return r?.continue()??Promise.resolve()}}clear(){s.jG.batch(()=>{this.#s.forEach(e=>{this.notify({type:"removed",mutation:e})}),this.#s.clear(),this.#a.clear()})}getAll(){return Array.from(this.#s)}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,n.nJ)(t,e))}findAll(e={}){return this.getAll().filter(t=>(0,n.nJ)(e,t))}notify(e){s.jG.batch(()=>{this.listeners.forEach(t=>{t(e)})})}resumePausedMutations(){let e=this.getAll().filter(e=>e.state.isPaused);return s.jG.batch(()=>Promise.all(e.map(e=>e.continue().catch(n.lQ))))}};function d(e){return e.options.scope?.id}var f=r(52778),p=r(8665);function y(e){return{onFetch:(t,r)=>{let i=t.options,s=t.fetchOptions?.meta?.fetchMore?.direction,a=t.state.data?.pages||[],o=t.state.data?.pageParams||[],u={pages:[],pageParams:[]},l=0,c=async()=>{let r=!1,c=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(t.signal.aborted?r=!0:t.signal.addEventListener("abort",()=>{r=!0}),t.signal)})},h=(0,n.ZM)(t.options,t.fetchOptions),d=async(e,i,s)=>{if(r)return Promise.reject();if(null==i&&e.pages.length)return Promise.resolve(e);let a=(()=>{let e={client:t.client,queryKey:t.queryKey,pageParam:i,direction:s?"backward":"forward",meta:t.options.meta};return c(e),e})(),o=await h(a),{maxPages:u}=t.options,l=s?n.ZZ:n.y9;return{pages:l(e.pages,o,u),pageParams:l(e.pageParams,i,u)}};if(s&&a.length){let e="backward"===s,t={pages:a,pageParams:o},r=(e?function(e,{pages:t,pageParams:r}){return t.length>0?e.getPreviousPageParam?.(t[0],t,r[0],r):void 0}:m)(i,t);u=await d(t,r,e)}else{let t=e??a.length;do{let e=0===l?o[0]??i.initialPageParam:m(i,u);if(l>0&&null==e)break;u=await d(u,e),l++}while(l<t)}return u};t.options.persister?t.fetchFn=()=>t.options.persister?.(c,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},r):t.fetchFn=c}}}function m(e,{pages:t,pageParams:r}){let n=t.length-1;return t.length>0?e.getNextPageParam(t[n],t,r[n],r):void 0}var g=class{#u;#r;#l;#c;#h;#d;#f;#p;constructor(e={}){this.#u=e.queryCache||new o,this.#r=e.mutationCache||new h,this.#l=e.defaultOptions||{},this.#c=new Map,this.#h=new Map,this.#d=0}mount(){this.#d++,1===this.#d&&(this.#f=f.m.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#u.onFocus())}),this.#p=p.t.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#u.onOnline())}))}unmount(){this.#d--,0===this.#d&&(this.#f?.(),this.#f=void 0,this.#p?.(),this.#p=void 0)}isFetching(e){return this.#u.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#r.findAll({...e,status:"pending"}).length}getQueryData(e){let t=this.defaultQueryOptions({queryKey:e});return this.#u.get(t.queryHash)?.state.data}ensureQueryData(e){let t=this.defaultQueryOptions(e),r=this.#u.build(this,t),i=r.state.data;return void 0===i?this.fetchQuery(e):(e.revalidateIfStale&&r.isStaleByTime((0,n.d2)(t.staleTime,r))&&this.prefetchQuery(t),Promise.resolve(i))}getQueriesData(e){return this.#u.findAll(e).map(({queryKey:e,state:t})=>[e,t.data])}setQueryData(e,t,r){let i=this.defaultQueryOptions({queryKey:e}),s=this.#u.get(i.queryHash),a=s?.state.data,o=(0,n.Zw)(t,a);if(void 0!==o)return this.#u.build(this,i).setData(o,{...r,manual:!0})}setQueriesData(e,t,r){return s.jG.batch(()=>this.#u.findAll(e).map(({queryKey:e})=>[e,this.setQueryData(e,t,r)]))}getQueryState(e){let t=this.defaultQueryOptions({queryKey:e});return this.#u.get(t.queryHash)?.state}removeQueries(e){let t=this.#u;s.jG.batch(()=>{t.findAll(e).forEach(e=>{t.remove(e)})})}resetQueries(e,t){let r=this.#u;return s.jG.batch(()=>(r.findAll(e).forEach(e=>{e.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){let r={revert:!0,...t};return Promise.all(s.jG.batch(()=>this.#u.findAll(e).map(e=>e.cancel(r)))).then(n.lQ).catch(n.lQ)}invalidateQueries(e,t={}){return s.jG.batch(()=>(this.#u.findAll(e).forEach(e=>{e.invalidate()}),e?.refetchType==="none")?Promise.resolve():this.refetchQueries({...e,type:e?.refetchType??e?.type??"active"},t))}refetchQueries(e,t={}){let r={...t,cancelRefetch:t.cancelRefetch??!0};return Promise.all(s.jG.batch(()=>this.#u.findAll(e).filter(e=>!e.isDisabled()&&!e.isStatic()).map(e=>{let t=e.fetch(void 0,r);return r.throwOnError||(t=t.catch(n.lQ)),"paused"===e.state.fetchStatus?Promise.resolve():t}))).then(n.lQ)}fetchQuery(e){let t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);let r=this.#u.build(this,t);return r.isStaleByTime((0,n.d2)(t.staleTime,r))?r.fetch(t):Promise.resolve(r.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(n.lQ).catch(n.lQ)}fetchInfiniteQuery(e){return e.behavior=y(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(n.lQ).catch(n.lQ)}ensureInfiniteQueryData(e){return e.behavior=y(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return p.t.isOnline()?this.#r.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#u}getMutationCache(){return this.#r}getDefaultOptions(){return this.#l}setDefaultOptions(e){this.#l=e}setQueryDefaults(e,t){this.#c.set((0,n.EN)(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){let t=[...this.#c.values()],r={};return t.forEach(t=>{(0,n.Cp)(e,t.queryKey)&&Object.assign(r,t.defaultOptions)}),r}setMutationDefaults(e,t){this.#h.set((0,n.EN)(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){let t=[...this.#h.values()],r={};return t.forEach(t=>{(0,n.Cp)(e,t.mutationKey)&&Object.assign(r,t.defaultOptions)}),r}defaultQueryOptions(e){if(e._defaulted)return e;let t={...this.#l.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=(0,n.F$)(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===n.hT&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#l.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#u.clear(),this.#r.clear()}}},92614:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"setAttributesFromProps",{enumerable:!0,get:function(){return s}});let r={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv",noModule:"noModule"},n=["onLoad","onReady","dangerouslySetInnerHTML","children","onError","strategy","stylesheets"];function i(e){return["async","defer","noModule"].includes(e)}function s(e,t){for(let[s,a]of Object.entries(t)){if(!t.hasOwnProperty(s)||n.includes(s)||void 0===a)continue;let o=r[s]||s.toLowerCase();"SCRIPT"===e.tagName&&i(o)?e[o]=!!a:e.setAttribute(o,String(a)),(!1===a||"SCRIPT"===e.tagName&&i(o)&&(!a||"false"===a))&&(e.setAttribute(o,""),e.removeAttribute(o))}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}}]);