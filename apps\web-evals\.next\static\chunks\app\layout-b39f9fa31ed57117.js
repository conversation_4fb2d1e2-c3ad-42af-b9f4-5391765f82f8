(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{266:(e,r,n)=>{"use strict";n.d(r,{ThemeProvider:()=>t});var s=n(7093);n(4545);let i=(0,n(7560).default)(()=>Promise.resolve().then(n.bind(n,502)).then(e=>e.<PERSON>ider),{loadableGenerated:{webpack:()=>[null]},ssr:!1});function t(e){let{children:r,...n}=e;return(0,s.jsx)(i,{...n,children:r})}},2097:()=>{},3909:(e,r,n)=>{"use strict";n.d(r,{ReactQueryProvider:()=>o});var s=n(7093),i=n(9972),t=n(6890);function o(e){let{children:r}=e,n=new i.E;return(0,s.jsx)(t.Ht,{client:n,children:r})}},4948:(e,r,n)=>{"use strict";n.d(r,{HoppingLogo:()=>d});var s=n(7093),i=n(4545),t=n(8923),o=n(3281),l=n(8322);let h=e=>{let{width:r=50,height:n=32,fill:i="#fff",className:o,...h}=e,d=(0,t.useRouter)();return(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:r,height:n,viewBox:"90 12 100 64",onClick:()=>d.push("/"),className:(0,l.cn)("logo cursor-pointer",o),...h,children:(0,s.jsx)("path",{d:"M171.633,15.8336l-1.7284,6.2499c-.0915.3309-.4369.5221-.7659.4239l-28.9937-8.6507c-.1928-.0575-.4016-.0167-.5586.1092l-28.7143,23.0269c-.0838.0672-.1839.1112-.2901.1276l-17.0849,2.6329c-.3163.0488-.5419.3327-.5178.6519l.0742.9817c.0237.3136.2809.5583.5953.5664l19.8448.513.2263.0063,14.6634-7.8328c.2053-.1097.455-.0936.6445.0415l10.3884,7.4053c.1629.1161.2589.3045.2571.5045l-.0876,9.826c-.0011.1272.0373.2515.11.3559l14.6133,20.9682c.1146.1644.3024.2624.5028.2624h4.626c.4615,0,.7574-.4908.542-.8989l-10.4155-19.7312c-.1019-.193-.0934-.4255.0221-.6106l5.4305-8.6994c.0591-.0947.143-.1715.2425-.222l19.415-9.8522c.1973-.1001.4332-.0861.6172.0366l5.5481,3.6981c.1007.0671.2189.1029.3399.1029h5.0407c.4881,0,.7804-.5429.5116-.9503l-13.9967-21.2171c-.2898-.4393-.962-.3331-1.1022.1741Z",fill:i,strokeWidth:"0"})})},d=e=>{let r=(0,i.useRef)(null),n=(0,s.jsx)(h,{ref:r,...e}),[t,l]=(0,o.A)(n);return(0,i.useEffect)(()=>{let e=r.current,n=null!==e&&e.classList.contains("animate-hop");if(l&&e&&!n)e.classList.add("animate-hop");else if(e&&n){let r=()=>{e.classList.remove("animate-hop"),e.removeEventListener("animationiteration",r)};e.addEventListener("animationiteration",r)}},[l]),t}},8419:(e,r,n)=>{Promise.resolve().then(n.t.bind(n,2097,23)),Promise.resolve().then(n.bind(n,4948)),Promise.resolve().then(n.bind(n,3909)),Promise.resolve().then(n.bind(n,266)),Promise.resolve().then(n.bind(n,8978)),Promise.resolve().then(n.bind(n,6844)),Promise.resolve().then(n.bind(n,1053)),Promise.resolve().then(n.bind(n,5822)),Promise.resolve().then(n.bind(n,8510)),Promise.resolve().then(n.bind(n,1991)),Promise.resolve().then(n.bind(n,3849)),Promise.resolve().then(n.bind(n,7204)),Promise.resolve().then(n.bind(n,2800)),Promise.resolve().then(n.bind(n,5945)),Promise.resolve().then(n.bind(n,1762)),Promise.resolve().then(n.bind(n,962)),Promise.resolve().then(n.bind(n,7546)),Promise.resolve().then(n.bind(n,655)),Promise.resolve().then(n.bind(n,1897)),Promise.resolve().then(n.bind(n,5134)),Promise.resolve().then(n.bind(n,8020)),Promise.resolve().then(n.t.bind(n,1037,23)),Promise.resolve().then(n.t.bind(n,2471,23))}},e=>{var r=r=>e(e.s=r);e.O(0,[234,361,287,777,335,550,358],()=>r(8419)),_N_E=e.O()}]);