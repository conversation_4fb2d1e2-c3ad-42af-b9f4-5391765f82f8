"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[657],{8665:(e,t,s)=>{s.d(t,{t:()=>r});var i=s(23792),n=s(18490),r=new class extends i.Q{#e=!0;#t;#s;constructor(){super(),this.#s=e=>{if(!n.S$&&window.addEventListener){let t=()=>e(!0),s=()=>e(!1);return window.addEventListener("online",t,!1),window.addEventListener("offline",s,!1),()=>{window.removeEventListener("online",t),window.removeEventListener("offline",s)}}}}onSubscribe(){this.#t||this.setEventListener(this.#s)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#s=e,this.#t?.(),this.#t=e(this.setOnline.bind(this))}setOnline(e){this.#e!==e&&(this.#e=e,this.listeners.forEach(t=>{t(e)}))}isOnline(){return this.#e}}},10502:(e,t,s)=>{s.d(t,{D:()=>l,N:()=>h});var i=s(34545),n=(e,t,s,i,n,r,o,a)=>{let u=document.documentElement,c=["light","dark"];function l(t){var s;(Array.isArray(e)?e:[e]).forEach(e=>{let s="class"===e,i=s&&r?n.map(e=>r[e]||e):n;s?(u.classList.remove(...i),u.classList.add(r&&r[t]?r[t]:t)):u.setAttribute(e,t)}),s=t,a&&c.includes(s)&&(u.style.colorScheme=s)}if(i)l(i);else try{let e=localStorage.getItem(t)||s,i=o&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;l(i)}catch(e){}},r=["light","dark"],o="(prefers-color-scheme: dark)",a="undefined"==typeof window,u=i.createContext(void 0),c={setTheme:e=>{},themes:[]},l=()=>{var e;return null!=(e=i.useContext(u))?e:c},h=e=>i.useContext(u)?i.createElement(i.Fragment,null,e.children):i.createElement(f,{...e}),d=["light","dark"],f=e=>{let{forcedTheme:t,disableTransitionOnChange:s=!1,enableSystem:n=!0,enableColorScheme:a=!0,storageKey:c="theme",themes:l=d,defaultTheme:h=n?"system":"light",attribute:f="data-theme",value:b,children:g,nonce:S,scriptProps:w}=e,[C,E]=i.useState(()=>y(c,h)),[O,T]=i.useState(()=>"system"===C?v():C),F=b?Object.values(b):l,k=i.useCallback(e=>{let t=e;if(!t)return;"system"===e&&n&&(t=v());let i=b?b[t]:t,o=s?m(S):null,u=document.documentElement,c=e=>{"class"===e?(u.classList.remove(...F),i&&u.classList.add(i)):e.startsWith("data-")&&(i?u.setAttribute(e,i):u.removeAttribute(e))};if(Array.isArray(f)?f.forEach(c):c(f),a){let e=r.includes(h)?h:null,s=r.includes(t)?t:e;u.style.colorScheme=s}null==o||o()},[S]),j=i.useCallback(e=>{let t="function"==typeof e?e(C):e;E(t);try{localStorage.setItem(c,t)}catch(e){}},[C]),A=i.useCallback(e=>{T(v(e)),"system"===C&&n&&!t&&k("system")},[C,t]);i.useEffect(()=>{let e=window.matchMedia(o);return e.addListener(A),A(e),()=>e.removeListener(A)},[A]),i.useEffect(()=>{let e=e=>{e.key===c&&(e.newValue?E(e.newValue):j(h))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[j]),i.useEffect(()=>{k(null!=t?t:C)},[t,C]);let q=i.useMemo(()=>({theme:C,setTheme:j,forcedTheme:t,resolvedTheme:"system"===C?O:C,themes:n?[...l,"system"]:l,systemTheme:n?O:void 0}),[C,j,t,O,n,l]);return i.createElement(u.Provider,{value:q},i.createElement(p,{forcedTheme:t,storageKey:c,attribute:f,enableSystem:n,enableColorScheme:a,defaultTheme:h,value:b,themes:l,nonce:S,scriptProps:w}),g)},p=i.memo(e=>{let{forcedTheme:t,storageKey:s,attribute:r,enableSystem:o,enableColorScheme:a,defaultTheme:u,value:c,themes:l,nonce:h,scriptProps:d}=e,f=JSON.stringify([r,s,u,t,l,c,o,a]).slice(1,-1);return i.createElement("script",{...d,suppressHydrationWarning:!0,nonce:"undefined"==typeof window?h:"",dangerouslySetInnerHTML:{__html:"(".concat(n.toString(),")(").concat(f,")")}})}),y=(e,t)=>{let s;if(!a){try{s=localStorage.getItem(e)||void 0}catch(e){}return s||t}},m=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},v=e=>(e||(e=window.matchMedia(o)),e.matches?"dark":"light")},18490:(e,t,s)=>{s.d(t,{Cp:()=>p,EN:()=>f,Eh:()=>c,F$:()=>d,GU:()=>T,MK:()=>l,S$:()=>i,ZM:()=>O,ZZ:()=>C,Zw:()=>r,d2:()=>u,f8:()=>y,gn:()=>o,hT:()=>E,j3:()=>a,lQ:()=>n,nJ:()=>h,pl:()=>S,y9:()=>w,yy:()=>g});var i="undefined"==typeof window||"Deno"in globalThis;function n(){}function r(e,t){return"function"==typeof e?e(t):e}function o(e){return"number"==typeof e&&e>=0&&e!==1/0}function a(e,t){return Math.max(e+(t||0)-Date.now(),0)}function u(e,t){return"function"==typeof e?e(t):e}function c(e,t){return"function"==typeof e?e(t):e}function l(e,t){let{type:s="all",exact:i,fetchStatus:n,predicate:r,queryKey:o,stale:a}=e;if(o){if(i){if(t.queryHash!==d(o,t.options))return!1}else if(!p(t.queryKey,o))return!1}if("all"!==s){let e=t.isActive();if("active"===s&&!e||"inactive"===s&&e)return!1}return("boolean"!=typeof a||t.isStale()===a)&&(!n||n===t.state.fetchStatus)&&(!r||!!r(t))}function h(e,t){let{exact:s,status:i,predicate:n,mutationKey:r}=e;if(r){if(!t.options.mutationKey)return!1;if(s){if(f(t.options.mutationKey)!==f(r))return!1}else if(!p(t.options.mutationKey,r))return!1}return(!i||t.state.status===i)&&(!n||!!n(t))}function d(e,t){return(t?.queryKeyHashFn||f)(e)}function f(e){return JSON.stringify(e,(e,t)=>v(t)?Object.keys(t).sort().reduce((e,s)=>(e[s]=t[s],e),{}):t)}function p(e,t){return e===t||typeof e==typeof t&&!!e&&!!t&&"object"==typeof e&&"object"==typeof t&&Object.keys(t).every(s=>p(e[s],t[s]))}function y(e,t){if(!t||Object.keys(e).length!==Object.keys(t).length)return!1;for(let s in e)if(e[s]!==t[s])return!1;return!0}function m(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function v(e){if(!b(e))return!1;let t=e.constructor;if(void 0===t)return!0;let s=t.prototype;return!!(b(s)&&s.hasOwnProperty("isPrototypeOf"))&&Object.getPrototypeOf(e)===Object.prototype}function b(e){return"[object Object]"===Object.prototype.toString.call(e)}function g(e){return new Promise(t=>{setTimeout(t,e)})}function S(e,t,s){return"function"==typeof s.structuralSharing?s.structuralSharing(e,t):!1!==s.structuralSharing?function e(t,s){if(t===s)return t;let i=m(t)&&m(s);if(i||v(t)&&v(s)){let n=i?t:Object.keys(t),r=n.length,o=i?s:Object.keys(s),a=o.length,u=i?[]:{},c=new Set(n),l=0;for(let n=0;n<a;n++){let r=i?n:o[n];(!i&&c.has(r)||i)&&void 0===t[r]&&void 0===s[r]?(u[r]=void 0,l++):(u[r]=e(t[r],s[r]),u[r]===t[r]&&void 0!==t[r]&&l++)}return r===a&&l===r?t:u}return s}(e,t):t}function w(e,t,s=0){let i=[...e,t];return s&&i.length>s?i.slice(1):i}function C(e,t,s=0){let i=[t,...e];return s&&i.length>s?i.slice(0,-1):i}var E=Symbol();function O(e,t){return!e.queryFn&&t?.initialPromise?()=>t.initialPromise:e.queryFn&&e.queryFn!==E?e.queryFn:()=>Promise.reject(Error(`Missing queryFn: '${e.queryHash}'`))}function T(e,t){return"function"==typeof e?e(...t):!!e}},23792:(e,t,s)=>{s.d(t,{Q:()=>i});var i=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}}},44254:(e,t,s)=>{s.d(t,{k:()=>n});var i=s(18490),n=class{#i;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),(0,i.gn)(this.gcTime)&&(this.#i=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(i.S$?1/0:3e5))}clearGcTimeout(){this.#i&&(clearTimeout(this.#i),this.#i=void 0)}}},52778:(e,t,s)=>{s.d(t,{m:()=>r});var i=s(23792),n=s(18490),r=new class extends i.Q{#n;#t;#s;constructor(){super(),this.#s=e=>{if(!n.S$&&window.addEventListener){let t=()=>e();return window.addEventListener("visibilitychange",t,!1),()=>{window.removeEventListener("visibilitychange",t)}}}}onSubscribe(){this.#t||this.setEventListener(this.#s)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#s=e,this.#t?.(),this.#t=e(e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()})}setFocused(e){this.#n!==e&&(this.#n=e,this.onFocus())}onFocus(){let e=this.isFocused();this.listeners.forEach(t=>{t(e)})}isFocused(){return"boolean"==typeof this.#n?this.#n:globalThis.document?.visibilityState!=="hidden"}}},52950:(e,t,s)=>{s.d(t,{Ht:()=>a,jE:()=>o});var i=s(34545),n=s(47093),r=i.createContext(void 0),o=e=>{let t=i.useContext(r);if(e)return e;if(!t)throw Error("No QueryClient set, use QueryClientProvider to set one");return t},a=e=>{let{client:t,children:s}=e;return i.useEffect(()=>(t.mount(),()=>{t.unmount()}),[t]),(0,n.jsx)(r.Provider,{value:t,children:s})}},66566:(e,t,s)=>{function i(){let e,t,s=new Promise((s,i)=>{e=s,t=i});function i(e){Object.assign(s,e),delete s.resolve,delete s.reject}return s.status="pending",s.catch(()=>{}),s.resolve=t=>{i({status:"fulfilled",value:t}),e(t)},s.reject=e=>{i({status:"rejected",reason:e}),t(e)},s}s.d(t,{T:()=>i})},69303:(e,t,s)=>{s.d(t,{jG:()=>n});var i=e=>setTimeout(e,0),n=function(){let e=[],t=0,s=e=>{e()},n=e=>{e()},r=i,o=i=>{t?e.push(i):r(()=>{s(i)})},a=()=>{let t=e;e=[],t.length&&r(()=>{n(()=>{t.forEach(e=>{s(e)})})})};return{batch:e=>{let s;t++;try{s=e()}finally{--t||a()}return s},batchCalls:e=>(...t)=>{o(()=>{e(...t)})},schedule:o,setNotifyFunction:e=>{s=e},setBatchNotifyFunction:e=>{n=e},setScheduler:e=>{r=e}}}()},73598:(e,t,s)=>{s.d(t,{II:()=>h,v_:()=>u,wm:()=>l});var i=s(52778),n=s(8665),r=s(66566),o=s(18490);function a(e){return Math.min(1e3*2**e,3e4)}function u(e){return(e??"online")!=="online"||n.t.isOnline()}var c=class extends Error{constructor(e){super("CancelledError"),this.revert=e?.revert,this.silent=e?.silent}};function l(e){return e instanceof c}function h(e){let t,s=!1,l=0,h=!1,d=(0,r.T)(),f=()=>i.m.isFocused()&&("always"===e.networkMode||n.t.isOnline())&&e.canRun(),p=()=>u(e.networkMode)&&e.canRun(),y=s=>{h||(h=!0,e.onSuccess?.(s),t?.(),d.resolve(s))},m=s=>{h||(h=!0,e.onError?.(s),t?.(),d.reject(s))},v=()=>new Promise(s=>{t=e=>{(h||f())&&s(e)},e.onPause?.()}).then(()=>{t=void 0,h||e.onContinue?.()}),b=()=>{let t;if(h)return;let i=0===l?e.initialPromise:void 0;try{t=i??e.fn()}catch(e){t=Promise.reject(e)}Promise.resolve(t).then(y).catch(t=>{if(h)return;let i=e.retry??3*!o.S$,n=e.retryDelay??a,r="function"==typeof n?n(l,t):n,u=!0===i||"number"==typeof i&&l<i||"function"==typeof i&&i(l,t);if(s||!u){m(t);return}l++,e.onFail?.(l,t),(0,o.yy)(r).then(()=>f()?void 0:v()).then(()=>{s?m(t):b()})})};return{promise:d,cancel:t=>{h||(m(new c(t)),e.abort?.())},continue:()=>(t?.(),d),cancelRetry:()=>{s=!0},continueRetry:()=>{s=!1},canStart:p,start:()=>(p()?b():v().then(b),d)}}},85283:(e,t,s)=>{s.d(t,{X:()=>a,k:()=>u});var i=s(18490),n=s(69303),r=s(73598),o=s(44254),a=class extends o.k{#r;#o;#a;#u;#c;#l;#h;constructor(e){super(),this.#h=!1,this.#l=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.#u=e.client,this.#a=this.#u.getQueryCache(),this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#r=function(e){let t="function"==typeof e.initialData?e.initialData():e.initialData,s=void 0!==t,i=s?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:s?i??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=e.state??this.#r,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#c?.promise}setOptions(e){this.options={...this.#l,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#a.remove(this)}setData(e,t){let s=(0,i.pl)(this.state.data,e,this.options);return this.#d({data:s,type:"success",dataUpdatedAt:t?.updatedAt,manual:t?.manual}),s}setState(e,t){this.#d({type:"setState",state:e,setStateOptions:t})}cancel(e){let t=this.#c?.promise;return this.#c?.cancel(e),t?t.then(i.lQ).catch(i.lQ):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#r)}isActive(){return this.observers.some(e=>!1!==(0,i.Eh)(e.options.enabled,this))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===i.hT||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0&&this.observers.some(e=>"static"===(0,i.d2)(e.options.staleTime,this))}isStale(){return this.getObserversCount()>0?this.observers.some(e=>e.getCurrentResult().isStale):void 0===this.state.data||this.state.isInvalidated}isStaleByTime(e=0){return void 0===this.state.data||"static"!==e&&(!!this.state.isInvalidated||!(0,i.j3)(this.state.dataUpdatedAt,e))}onFocus(){let e=this.observers.find(e=>e.shouldFetchOnWindowFocus());e?.refetch({cancelRefetch:!1}),this.#c?.continue()}onOnline(){let e=this.observers.find(e=>e.shouldFetchOnReconnect());e?.refetch({cancelRefetch:!1}),this.#c?.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.#a.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter(t=>t!==e),this.observers.length||(this.#c&&(this.#h?this.#c.cancel({revert:!0}):this.#c.cancelRetry()),this.scheduleGc()),this.#a.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#d({type:"invalidate"})}fetch(e,t){if("idle"!==this.state.fetchStatus){if(void 0!==this.state.data&&t?.cancelRefetch)this.cancel({silent:!0});else if(this.#c)return this.#c.continueRetry(),this.#c.promise}if(e&&this.setOptions(e),!this.options.queryFn){let e=this.observers.find(e=>e.options.queryFn);e&&this.setOptions(e.options)}let s=new AbortController,n=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(this.#h=!0,s.signal)})},o=()=>{let e=(0,i.ZM)(this.options,t),s=(()=>{let e={client:this.#u,queryKey:this.queryKey,meta:this.meta};return n(e),e})();return(this.#h=!1,this.options.persister)?this.options.persister(e,s,this):e(s)},a=(()=>{let e={fetchOptions:t,options:this.options,queryKey:this.queryKey,client:this.#u,state:this.state,fetchFn:o};return n(e),e})();this.options.behavior?.onFetch(a,this),this.#o=this.state,("idle"===this.state.fetchStatus||this.state.fetchMeta!==a.fetchOptions?.meta)&&this.#d({type:"fetch",meta:a.fetchOptions?.meta});let u=e=>{(0,r.wm)(e)&&e.silent||this.#d({type:"error",error:e}),(0,r.wm)(e)||(this.#a.config.onError?.(e,this),this.#a.config.onSettled?.(this.state.data,e,this)),this.scheduleGc()};return this.#c=(0,r.II)({initialPromise:t?.initialPromise,fn:a.fetchFn,abort:s.abort.bind(s),onSuccess:e=>{if(void 0===e){u(Error(`${this.queryHash} data is undefined`));return}try{this.setData(e)}catch(e){u(e);return}this.#a.config.onSuccess?.(e,this),this.#a.config.onSettled?.(e,this.state.error,this),this.scheduleGc()},onError:u,onFail:(e,t)=>{this.#d({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#d({type:"pause"})},onContinue:()=>{this.#d({type:"continue"})},retry:a.options.retry,retryDelay:a.options.retryDelay,networkMode:a.options.networkMode,canRun:()=>!0}),this.#c.start()}#d(e){this.state=(t=>{switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":return{...t,...u(t.data,this.options),fetchMeta:e.meta??null};case"success":return{...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":let s=e.error;if((0,r.wm)(s)&&s.revert&&this.#o)return{...this.#o,fetchStatus:"idle"};return{...t,error:s,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:s,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}})(this.state),n.jG.batch(()=>{this.observers.forEach(e=>{e.onQueryUpdate()}),this.#a.notify({query:this,type:"updated",action:e})})}};function u(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:(0,r.v_)(t.networkMode)?"fetching":"paused",...void 0===e&&{error:null,status:"pending"}}}}}]);