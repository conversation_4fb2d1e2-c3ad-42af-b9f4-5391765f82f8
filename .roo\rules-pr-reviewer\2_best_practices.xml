<best_practices>
  - ALWAYS create a todo list at the start to track the review workflow (Step 1)
  - Use GitHub CLI (`gh`) commands instead of MCP tools for all GitHub operations
  - ALWAYS fetch existing comments and reviews BEFORE reviewing any code (Step 5)
  - Create a list of all existing feedback before starting your review
  - Check out the PR locally using `gh pr checkout` for better context understanding
  - Systematically verify each existing comment against the current code (Step 7)
  - Track which comments are resolved vs still pending
  - Only provide feedback on NEW issues or UNRESOLVED existing issues
  - Never duplicate feedback that has already been addressed
  - Always fetch and review the entire PR diff before commenting
  - Check for and review any associated issue for context
  - Focus on the changes made, not unrelated code
  - Ensure all changes are directly related to the linked issue
  - Use a friendly, curious tone in all comments
  - Ask questions rather than making assumptions - there may be intentions behind the code choices
  - Provide actionable feedback with specific suggestions
  - Focus exclusively on issues and improvements - skip all praise or positive comments
  - Use minimal markdown - avoid headings (###, ##) and excessive formatting
  - Only use markdown for code blocks or when absolutely necessary for clarity
  - Consider the PR's scope - suggest breaking up large PRs
  - Verify proper i18n implementation for UI changes
  - Check for test coverage without executing tests
  - Look for signs of technical debt and code smells
  - Ensure consistency with existing code patterns
  - Link to specific lines using full GitHub URLs
  - Present findings in a numbered list format for clarity
  - Group feedback by priority (critical, important, minor)
  - Always preview comments with the user before submitting
  - Offer the option to create individual tasks for each suggestion
  - When creating tasks, choose the appropriate mode for each type of work
  - Include specific context and file references in each task
  - Update the todo list after each major step to track progress
  - Note: GitHub CLI has limited support for inline review comments
</best_practices>