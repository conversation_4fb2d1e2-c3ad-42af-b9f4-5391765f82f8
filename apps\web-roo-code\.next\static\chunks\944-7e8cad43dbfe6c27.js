"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[944],{3117:(t,e,i)=>{i.d(e,{s:()=>r});var n=i(14787);function r(t){return(0,n.G)(t)&&"offsetHeight"in t}},8087:(t,e,i)=>{i.d(e,{A:()=>r});let n={direction:"forward",speed:2,startDelay:1e3,active:!0,breakpoints:{},playOnInit:!0,stopOnFocusIn:!0,stopOnInteraction:!0,stopOnMouseEnter:!1,rootNode:null};function r(t={}){let e,i,s,o,a,l=0,u=!1,h=!1;function c(){if(s||u)return;i.emit("autoScroll:play");let t=i.internalEngine(),{ownerWindow:n}=t;l=n.setTimeout(()=>{t.scrollBody=function(t){let{location:n,previousLocation:r,offsetLocation:s,target:o,scrollTarget:a,index:l,indexPrevious:u,limit:{reachedMin:h,reachedMax:c,constrain:p},options:{loop:f}}=t,m="forward"===e.direction?-1:1,g=()=>T,y=0,v=0,x=n.get(),w=0,b=!1,T={direction:()=>v,duration:()=>-1,velocity:()=>y,settled:()=>b,seek:function(){let t=0;r.set(n),y=m*e.speed,x+=y,n.add(y),o.set(n),v=Math.sign(x-w),w=x;let g=a.byDistance(0,!1).index;l.get()!==g&&(u.set(l.get()),l.set(g),i.emit("select"));let S="forward"===e.direction?h(s.get()):c(s.get());if(!f&&S){b=!0;let t=p(n.get());n.set(t),o.set(n),d()}return T},useBaseFriction:g,useBaseDuration:g,useFriction:g,useDuration:g};return T}(t),t.animation.start()},o),u=!0}function d(){if(s||!u)return;i.emit("autoScroll:stop");let t=i.internalEngine(),{ownerWindow:e}=t;t.scrollBody=a,e.clearTimeout(l),l=0,u=!1}function p(){h||d()}function f(){h||v()}function m(){h=!0,d()}function g(){h=!1,c()}function y(){i.off("settle",y),c()}function v(){i.on("settle",y)}return{name:"autoScroll",options:t,init:function(l,u){i=l;let{mergeOptions:h,optionsAtMedia:y}=u,v=h(n,r.globalOptions);if(e=y(h(v,t)),i.scrollSnapList().length<=1)return;o=e.startDelay,s=!1,a=i.internalEngine().scrollBody;let{eventStore:x}=i.internalEngine(),w=!!i.internalEngine().options.watchDrag,b=function(t,e){let i=t.rootNode();return e&&e(i)||i}(i,e.rootNode);w&&i.on("pointerDown",p),w&&!e.stopOnInteraction&&i.on("pointerUp",f),e.stopOnMouseEnter&&x.add(b,"mouseenter",m),e.stopOnMouseEnter&&!e.stopOnInteraction&&x.add(b,"mouseleave",g),e.stopOnFocusIn&&i.on("slideFocusStart",d),e.stopOnFocusIn&&!e.stopOnInteraction&&x.add(i.containerNode(),"focusout",c),e.playOnInit&&c()},destroy:function(){i.off("pointerDown",p).off("pointerUp",f).off("slideFocusStart",d).off("settle",y),d(),s=!0,u=!1},play:function(t){void 0!==t&&(o=t),c()},stop:function(){u&&d()},reset:function(){u&&(d(),v())},isPlaying:function(){return u}}}r.globalOptions=void 0},14787:(t,e,i)=>{i.d(e,{G:()=>n});function n(t){return"object"==typeof t&&null!==t}},21803:(t,e,i)=>{i.d(e,{xQ:()=>s});var n=i(34545),r=i(48844);function s(t=!0){let e=(0,n.useContext)(r.t);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:o,register:a}=e,l=(0,n.useId)();(0,n.useEffect)(()=>{if(t)return a(l)},[t]);let u=(0,n.useCallback)(()=>t&&o&&o(l),[l,o,t]);return!i&&o?[!1,u]:[!0]}},23392:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(10436).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},35338:(t,e,i)=>{i.d(e,{L:()=>n});let n=(0,i(34545).createContext)({})},40959:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(10436).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},48844:(t,e,i)=>{i.d(e,{t:()=>n});let n=(0,i(34545).createContext)(null)},50630:(t,e,i)=>{i.d(e,{M:()=>r});var n=i(34545);function r(t){let e=(0,n.useRef)(null);return null===e.current&&(e.current=t()),e.current}},54001:(t,e,i)=>{i.d(e,{N:()=>v});var n=i(47093),r=i(34545),s=i(35338),o=i(50630),a=i(76855),l=i(48844),u=i(3117),h=i(90221);class c extends r.Component{getSnapshotBeforeUpdate(t){let e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){let t=e.offsetParent,i=(0,u.s)(t)&&t.offsetWidth||0,n=this.props.sizeRef.current;n.height=e.offsetHeight||0,n.width=e.offsetWidth||0,n.top=e.offsetTop,n.left=e.offsetLeft,n.right=i-n.width-n.left}return null}componentDidUpdate(){}render(){return this.props.children}}function d(t){let{children:e,isPresent:i,anchorX:s}=t,o=(0,r.useId)(),a=(0,r.useRef)(null),l=(0,r.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:u}=(0,r.useContext)(h.Q);return(0,r.useInsertionEffect)(()=>{let{width:t,height:e,top:n,left:r,right:h}=l.current;if(i||!a.current||!t||!e)return;a.current.dataset.motionPopId=o;let c=document.createElement("style");return u&&(c.nonce=u),document.head.appendChild(c),c.sheet&&c.sheet.insertRule('\n          [data-motion-pop-id="'.concat(o,'"] {\n            position: absolute !important;\n            width: ').concat(t,"px !important;\n            height: ").concat(e,"px !important;\n            ").concat("left"===s?"left: ".concat(r):"right: ".concat(h),"px !important;\n            top: ").concat(n,"px !important;\n          }\n        ")),()=>{document.head.contains(c)&&document.head.removeChild(c)}},[i]),(0,n.jsx)(c,{isPresent:i,childRef:a,sizeRef:l,children:r.cloneElement(e,{ref:a})})}let p=t=>{let{children:e,initial:i,isPresent:s,onExitComplete:a,custom:u,presenceAffectsLayout:h,mode:c,anchorX:p}=t,m=(0,o.M)(f),g=(0,r.useId)(),y=!0,v=(0,r.useMemo)(()=>(y=!1,{id:g,initial:i,isPresent:s,custom:u,onExitComplete:t=>{for(let e of(m.set(t,!0),m.values()))if(!e)return;a&&a()},register:t=>(m.set(t,!1),()=>m.delete(t))}),[s,m,a]);return h&&y&&(v={...v}),(0,r.useMemo)(()=>{m.forEach((t,e)=>m.set(e,!1))},[s]),r.useEffect(()=>{s||m.size||!a||a()},[s]),"popLayout"===c&&(e=(0,n.jsx)(d,{isPresent:s,anchorX:p,children:e})),(0,n.jsx)(l.t.Provider,{value:v,children:e})};function f(){return new Map}var m=i(21803);let g=t=>t.key||"";function y(t){let e=[];return r.Children.forEach(t,t=>{(0,r.isValidElement)(t)&&e.push(t)}),e}let v=t=>{let{children:e,custom:i,initial:l=!0,onExitComplete:u,presenceAffectsLayout:h=!0,mode:c="sync",propagate:d=!1,anchorX:f="left"}=t,[v,x]=(0,m.xQ)(d),w=(0,r.useMemo)(()=>y(e),[e]),b=d&&!v?[]:w.map(g),T=(0,r.useRef)(!0),S=(0,r.useRef)(w),P=(0,o.M)(()=>new Map),[A,E]=(0,r.useState)(w),[M,D]=(0,r.useState)(w);(0,a.E)(()=>{T.current=!1,S.current=w;for(let t=0;t<M.length;t++){let e=g(M[t]);b.includes(e)?P.delete(e):!0!==P.get(e)&&P.set(e,!1)}},[M,b.length,b.join("-")]);let V=[];if(w!==A){let t=[...w];for(let e=0;e<M.length;e++){let i=M[e],n=g(i);b.includes(n)||(t.splice(e,0,i),V.push(i))}return"wait"===c&&V.length&&(t=V),D(y(t)),E(w),null}let{forceRender:k}=(0,r.useContext)(s.L);return(0,n.jsx)(n.Fragment,{children:M.map(t=>{let e=g(t),r=(!d||!!v)&&(w===M||b.includes(e));return(0,n.jsx)(p,{isPresent:r,initial:(!T.current||!!l)&&void 0,custom:i,presenceAffectsLayout:h,mode:c,onExitComplete:r?void 0:()=>{if(!P.has(e))return;P.set(e,!0);let t=!0;P.forEach(e=>{e||(t=!1)}),t&&(null==k||k(),D(S.current),d&&(null==x||x()),u&&u())},anchorX:f,children:t},e)})})}},59981:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(10436).A)("code-xml",[["path",{d:"m18 16 4-4-4-4",key:"1inbqp"}],["path",{d:"m6 8-4 4 4 4",key:"15zrgr"}],["path",{d:"m14.5 4-5 16",key:"e7oirm"}]])},63774:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(10436).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},71160:(t,e,i)=>{let n;function r(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function s(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function o(t,e,i,n){if("function"==typeof e){let[r,o]=s(n);e=e(void 0!==i?i:t.custom,r,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[r,o]=s(n);e=e(void 0!==i?i:t.custom,r,o)}return e}function a(t,e,i){let n=t.getProps();return o(n,e,void 0!==i?i:n.custom,t)}function l(t,e){return t?.[e]??t?.default??t}i.d(e,{P:()=>sM});let u=t=>t,h={},c=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],d={value:null,addProjectionMetrics:null};function p(t,e){let i=!1,n=!0,r={delta:0,timestamp:0,isProcessing:!1},s=()=>i=!0,o=c.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,n=new Set,r=!1,s=!1,o=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){o.has(e)&&(h.schedule(e),t()),l++,e(a)}let h={schedule:(t,e=!1,s=!1)=>{let a=s&&r?i:n;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{n.delete(t),o.delete(t)},process:t=>{if(a=t,r){s=!0;return}r=!0,[i,n]=[n,i],i.forEach(u),e&&d.value&&d.value.frameloop[e].push(l),l=0,i.clear(),r=!1,s&&(s=!1,h.process(t))}};return h}(s,e?i:void 0),t),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:p,update:f,preRender:m,render:g,postRender:y}=o,v=()=>{let s=h.useManualTiming?r.timestamp:performance.now();i=!1,h.useManualTiming||(r.delta=n?1e3/60:Math.max(Math.min(s-r.timestamp,40),1)),r.timestamp=s,r.isProcessing=!0,a.process(r),l.process(r),u.process(r),p.process(r),f.process(r),m.process(r),g.process(r),y.process(r),r.isProcessing=!1,i&&e&&(n=!1,t(v))},x=()=>{i=!0,n=!0,r.isProcessing||t(v)};return{schedule:c.reduce((t,e)=>{let n=o[e];return t[e]=(t,e=!1,r=!1)=>(i||x(),n.schedule(t,e,r)),t},{}),cancel:t=>{for(let e=0;e<c.length;e++)o[c[e]].cancel(t)},state:r,steps:o}}let{schedule:f,cancel:m,state:g,steps:y}=p("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:u,!0),v=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],x=new Set(v),w=new Set(["width","height","top","left","right","bottom",...v]);function b(t,e){-1===t.indexOf(e)&&t.push(e)}function T(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class S{constructor(){this.subscriptions=[]}add(t){return b(this.subscriptions,t),()=>T(this.subscriptions,t)}notify(t,e,i){let n=this.subscriptions.length;if(n)if(1===n)this.subscriptions[0](t,e,i);else for(let r=0;r<n;r++){let n=this.subscriptions[r];n&&n(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function P(){n=void 0}let A={now:()=>(void 0===n&&A.set(g.isProcessing||h.useManualTiming?g.timestamp:performance.now()),n),set:t=>{n=t,queueMicrotask(P)}},E=t=>!isNaN(parseFloat(t)),M={current:void 0};class D{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=A.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=A.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=E(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new S);let i=this.events[t].add(e);return"change"===t?()=>{i(),f.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return M.current&&M.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=A.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function V(t,e){return new D(t,e)}let k=t=>Array.isArray(t),C=t=>!!(t&&t.getVelocity);function R(t,e){let i=t.getValue("willChange");if(C(i)&&i.add)return i.add(e);if(!i&&h.WillChange){let i=new h.WillChange("auto");t.addValue("willChange",i),i.add(e)}}let L=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),j="data-"+L("framerAppearId"),F=(t,e)=>i=>e(t(i)),O=(...t)=>t.reduce(F),B=(t,e,i)=>i>e?e:i<t?t:i,I=t=>1e3*t,U=t=>t/1e3,N={layout:0,mainThread:0,waapi:0},$=()=>{},W=()=>{},z=t=>e=>"string"==typeof e&&e.startsWith(t),H=z("--"),Y=z("var(--"),X=t=>!!Y(t)&&q.test(t.split("/*")[0].trim()),q=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,K={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},G={...K,transform:t=>B(0,1,t)},_={...K,default:1},Z=t=>Math.round(1e5*t)/1e5,Q=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,J=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tt=(t,e)=>i=>!!("string"==typeof i&&J.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),te=(t,e,i)=>n=>{if("string"!=typeof n)return n;let[r,s,o,a]=n.match(Q);return{[t]:parseFloat(r),[e]:parseFloat(s),[i]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},ti=t=>B(0,255,t),tn={...K,transform:t=>Math.round(ti(t))},tr={test:tt("rgb","red"),parse:te("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:n=1})=>"rgba("+tn.transform(t)+", "+tn.transform(e)+", "+tn.transform(i)+", "+Z(G.transform(n))+")"},ts={test:tt("#"),parse:function(t){let e="",i="",n="",r="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),n=t.substring(5,7),r=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),n=t.substring(3,4),r=t.substring(4,5),e+=e,i+=i,n+=n,r+=r),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(n,16),alpha:r?parseInt(r,16)/255:1}},transform:tr.transform},to=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),ta=to("deg"),tl=to("%"),tu=to("px"),th=to("vh"),tc=to("vw"),td={...tl,parse:t=>tl.parse(t)/100,transform:t=>tl.transform(100*t)},tp={test:tt("hsl","hue"),parse:te("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:n=1})=>"hsla("+Math.round(t)+", "+tl.transform(Z(e))+", "+tl.transform(Z(i))+", "+Z(G.transform(n))+")"},tf={test:t=>tr.test(t)||ts.test(t)||tp.test(t),parse:t=>tr.test(t)?tr.parse(t):tp.test(t)?tp.parse(t):ts.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?tr.transform(t):tp.transform(t)},tm=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tg="number",ty="color",tv=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tx(t){let e=t.toString(),i=[],n={color:[],number:[],var:[]},r=[],s=0,o=e.replace(tv,t=>(tf.test(t)?(n.color.push(s),r.push(ty),i.push(tf.parse(t))):t.startsWith("var(")?(n.var.push(s),r.push("var"),i.push(t)):(n.number.push(s),r.push(tg),i.push(parseFloat(t))),++s,"${}")).split("${}");return{values:i,split:o,indexes:n,types:r}}function tw(t){return tx(t).values}function tb(t){let{split:e,types:i}=tx(t),n=e.length;return t=>{let r="";for(let s=0;s<n;s++)if(r+=e[s],void 0!==t[s]){let e=i[s];e===tg?r+=Z(t[s]):e===ty?r+=tf.transform(t[s]):r+=t[s]}return r}}let tT=t=>"number"==typeof t?0:t,tS={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(Q)?.length||0)+(t.match(tm)?.length||0)>0},parse:tw,createTransformer:tb,getAnimatableNone:function(t){let e=tw(t);return tb(t)(e.map(tT))}};function tP(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function tA(t,e){return i=>i>0?e:t}let tE=(t,e,i)=>t+(e-t)*i,tM=(t,e,i)=>{let n=t*t,r=i*(e*e-n)+n;return r<0?0:Math.sqrt(r)},tD=[ts,tr,tp],tV=t=>tD.find(e=>e.test(t));function tk(t){let e=tV(t);if($(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===tp&&(i=function({hue:t,saturation:e,lightness:i,alpha:n}){t/=360,i/=100;let r=0,s=0,o=0;if(e/=100){let n=i<.5?i*(1+e):i+e-i*e,a=2*i-n;r=tP(a,n,t+1/3),s=tP(a,n,t),o=tP(a,n,t-1/3)}else r=s=o=i;return{red:Math.round(255*r),green:Math.round(255*s),blue:Math.round(255*o),alpha:n}}(i)),i}let tC=(t,e)=>{let i=tk(t),n=tk(e);if(!i||!n)return tA(t,e);let r={...i};return t=>(r.red=tM(i.red,n.red,t),r.green=tM(i.green,n.green,t),r.blue=tM(i.blue,n.blue,t),r.alpha=tE(i.alpha,n.alpha,t),tr.transform(r))},tR=new Set(["none","hidden"]);function tL(t,e){return i=>tE(t,e,i)}function tj(t){return"number"==typeof t?tL:"string"==typeof t?X(t)?tA:tf.test(t)?tC:tB:Array.isArray(t)?tF:"object"==typeof t?tf.test(t)?tC:tO:tA}function tF(t,e){let i=[...t],n=i.length,r=t.map((t,i)=>tj(t)(t,e[i]));return t=>{for(let e=0;e<n;e++)i[e]=r[e](t);return i}}function tO(t,e){let i={...t,...e},n={};for(let r in i)void 0!==t[r]&&void 0!==e[r]&&(n[r]=tj(t[r])(t[r],e[r]));return t=>{for(let e in n)i[e]=n[e](t);return i}}let tB=(t,e)=>{let i=tS.createTransformer(e),n=tx(t),r=tx(e);return n.indexes.var.length===r.indexes.var.length&&n.indexes.color.length===r.indexes.color.length&&n.indexes.number.length>=r.indexes.number.length?tR.has(t)&&!r.values.length||tR.has(e)&&!n.values.length?function(t,e){return tR.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):O(tF(function(t,e){let i=[],n={color:0,var:0,number:0};for(let r=0;r<e.values.length;r++){let s=e.types[r],o=t.indexes[s][n[s]],a=t.values[o]??0;i[r]=a,n[s]++}return i}(n,r),r.values),i):($(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tA(t,e))};function tI(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?tE(t,e,i):tj(t)(t,e)}let tU=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>f.update(e,t),stop:()=>m(e),now:()=>g.isProcessing?g.timestamp:A.now()}},tN=(t,e,i=10)=>{let n="",r=Math.max(Math.round(e/i),2);for(let e=0;e<r;e++)n+=t(e/(r-1))+", ";return`linear(${n.substring(0,n.length-2)})`};function t$(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function tW(t,e,i){var n,r;let s=Math.max(e-5,0);return n=i-t(s),(r=e-s)?1e3/r*n:0}let tz={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function tH(t,e){return t*Math.sqrt(1-e*e)}let tY=["duration","bounce"],tX=["stiffness","damping","mass"];function tq(t,e){return e.some(e=>void 0!==t[e])}function tK(t=tz.visualDuration,e=tz.bounce){let i,n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:r,restDelta:s}=n,o=n.keyframes[0],a=n.keyframes[n.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:h,mass:c,duration:d,velocity:p,isResolvedFromDuration:f}=function(t){let e={velocity:tz.velocity,stiffness:tz.stiffness,damping:tz.damping,mass:tz.mass,isResolvedFromDuration:!1,...t};if(!tq(t,tX)&&tq(t,tY))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),n=i*i,r=2*B(.05,1,1-(t.bounce||0))*Math.sqrt(n);e={...e,mass:tz.mass,stiffness:n,damping:r}}else{let i=function({duration:t=tz.duration,bounce:e=tz.bounce,velocity:i=tz.velocity,mass:n=tz.mass}){let r,s;$(t<=I(tz.maxDuration),"Spring duration must be 10 seconds or less");let o=1-e;o=B(tz.minDamping,tz.maxDamping,o),t=B(tz.minDuration,tz.maxDuration,U(t)),o<1?(r=e=>{let n=e*o,r=n*t;return .001-(n-i)/tH(e,o)*Math.exp(-r)},s=e=>{let n=e*o*t,s=Math.pow(o,2)*Math.pow(e,2)*t,a=Math.exp(-n),l=tH(Math.pow(e,2),o);return(n*i+i-s)*a*(-r(e)+.001>0?-1:1)/l}):(r=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),s=e=>t*t*(i-e)*Math.exp(-e*t));let a=function(t,e,i){let n=i;for(let i=1;i<12;i++)n-=t(n)/e(n);return n}(r,s,5/t);if(t=I(t),isNaN(a))return{stiffness:tz.stiffness,damping:tz.damping,duration:t};{let e=Math.pow(a,2)*n;return{stiffness:e,damping:2*o*Math.sqrt(n*e),duration:t}}}(t);(e={...e,...i,mass:tz.mass}).isResolvedFromDuration=!0}return e}({...n,velocity:-U(n.velocity||0)}),m=p||0,g=h/(2*Math.sqrt(u*c)),y=a-o,v=U(Math.sqrt(u/c)),x=5>Math.abs(y);if(r||(r=x?tz.restSpeed.granular:tz.restSpeed.default),s||(s=x?tz.restDelta.granular:tz.restDelta.default),g<1){let t=tH(v,g);i=e=>a-Math.exp(-g*v*e)*((m+g*v*y)/t*Math.sin(t*e)+y*Math.cos(t*e))}else if(1===g)i=t=>a-Math.exp(-v*t)*(y+(m+v*y)*t);else{let t=v*Math.sqrt(g*g-1);i=e=>{let i=Math.exp(-g*v*e),n=Math.min(t*e,300);return a-i*((m+g*v*y)*Math.sinh(n)+t*y*Math.cosh(n))/t}}let w={calculatedDuration:f&&d||null,next:t=>{let e=i(t);if(f)l.done=t>=d;else{let n=0===t?m:0;g<1&&(n=0===t?I(m):tW(i,t,e));let o=Math.abs(a-e)<=s;l.done=Math.abs(n)<=r&&o}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(t$(w),2e4),e=tN(e=>w.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return w}function tG({keyframes:t,velocity:e=0,power:i=.8,timeConstant:n=325,bounceDamping:r=10,bounceStiffness:s=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:h}){let c,d,p=t[0],f={done:!1,value:p},m=t=>void 0!==a&&t<a||void 0!==l&&t>l,g=t=>void 0===a?l:void 0===l?a:Math.abs(a-t)<Math.abs(l-t)?a:l,y=i*e,v=p+y,x=void 0===o?v:o(v);x!==v&&(y=x-p);let w=t=>-y*Math.exp(-t/n),b=t=>x+w(t),T=t=>{let e=w(t),i=b(t);f.done=Math.abs(e)<=u,f.value=f.done?x:i},S=t=>{m(f.value)&&(c=t,d=tK({keyframes:[f.value,g(f.value)],velocity:tW(b,t,f.value),damping:r,stiffness:s,restDelta:u,restSpeed:h}))};return S(0),{calculatedDuration:null,next:t=>{let e=!1;return(d||void 0!==c||(e=!0,T(t),S(t)),void 0!==c&&t>=c)?d.next(t-c):(e||T(t),f)}}}tK.applyToOptions=t=>{let e=function(t,e=100,i){let n=i({...t,keyframes:[0,e]}),r=Math.min(t$(n),2e4);return{type:"keyframes",ease:t=>n.next(r*t).value/e,duration:U(r)}}(t,100,tK);return t.ease=e.ease,t.duration=I(e.duration),t.type="keyframes",t};let t_=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function tZ(t,e,i,n){if(t===e&&i===n)return u;let r=e=>(function(t,e,i,n,r){let s,o,a=0;do(s=t_(o=e+(i-e)/2,n,r)-t)>0?i=o:e=o;while(Math.abs(s)>1e-7&&++a<12);return o})(e,0,1,t,i);return t=>0===t||1===t?t:t_(r(t),e,n)}let tQ=tZ(.42,0,1,1),tJ=tZ(0,0,.58,1),t0=tZ(.42,0,.58,1),t1=t=>Array.isArray(t)&&"number"!=typeof t[0],t5=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,t3=t=>e=>1-t(1-e),t2=tZ(.33,1.53,.69,.99),t4=t3(t2),t6=t5(t4),t9=t=>(t*=2)<1?.5*t4(t):.5*(2-Math.pow(2,-10*(t-1))),t8=t=>1-Math.sin(Math.acos(t)),t7=t3(t8),et=t5(t8),ee=t=>Array.isArray(t)&&"number"==typeof t[0],ei={linear:u,easeIn:tQ,easeInOut:t0,easeOut:tJ,circIn:t8,circInOut:et,circOut:t7,backIn:t4,backInOut:t6,backOut:t2,anticipate:t9},en=t=>"string"==typeof t,er=t=>{if(ee(t)){W(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,n,r]=t;return tZ(e,i,n,r)}return en(t)?(W(void 0!==ei[t],`Invalid easing type '${t}'`),ei[t]):t},es=(t,e,i)=>{let n=e-t;return 0===n?1:(i-t)/n};function eo({duration:t=300,keyframes:e,times:i,ease:n="easeInOut"}){let r=t1(n)?n.map(er):er(n),s={done:!1,value:e[0]},o=function(t,e,{clamp:i=!0,ease:n,mixer:r}={}){let s=t.length;if(W(s===e.length,"Both input and output ranges must be the same length"),1===s)return()=>e[0];if(2===s&&e[0]===e[1])return()=>e[1];let o=t[0]===t[1];t[0]>t[s-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=function(t,e,i){let n=[],r=i||h.mix||tI,s=t.length-1;for(let i=0;i<s;i++){let s=r(t[i],t[i+1]);e&&(s=O(Array.isArray(e)?e[i]||u:e,s)),n.push(s)}return n}(e,n,r),l=a.length,c=i=>{if(o&&i<t[0])return e[0];let n=0;if(l>1)for(;n<t.length-2&&!(i<t[n+1]);n++);let r=es(t[n],t[n+1],i);return a[n](r)};return i?e=>c(B(t[0],t[s-1],e)):c}((i&&i.length===e.length?i:function(t){let e=[0];return function(t,e){let i=t[t.length-1];for(let n=1;n<=e;n++){let r=es(0,e,n);t.push(tE(i,1,r))}}(e,t.length-1),e}(e)).map(e=>e*t),e,{ease:Array.isArray(r)?r:e.map(()=>r||t0).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(s.value=o(e),s.done=e>=t,s)}}let ea=t=>null!==t;function el(t,{repeat:e,repeatType:i="loop"},n,r=1){let s=t.filter(ea),o=r<0||e&&"loop"!==i&&e%2==1?0:s.length-1;return o&&void 0!==n?n:s[o]}let eu={decay:tG,inertia:tG,tween:eo,keyframes:eo,spring:tK};function eh(t){"string"==typeof t.type&&(t.type=eu[t.type])}class ec{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let ed=t=>t/100;class ep extends ec{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==A.now()&&this.tick(A.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},N.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;eh(t);let{type:e=eo,repeat:i=0,repeatDelay:n=0,repeatType:r,velocity:s=0}=t,{keyframes:o}=t,a=e||eo;a!==eo&&"number"!=typeof o[0]&&(this.mixKeyframes=O(ed,tI(o[0],o[1])),o=[0,100]);let l=a({...t,keyframes:o});"mirror"===r&&(this.mirroredGenerator=a({...t,keyframes:[...o].reverse(),velocity:-s})),null===l.calculatedDuration&&(l.calculatedDuration=t$(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+n,this.totalDuration=this.resolvedDuration*(i+1)-n,this.generator=l}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:n,mixKeyframes:r,mirroredGenerator:s,resolvedDuration:o,calculatedDuration:a}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:u,repeat:h,repeatType:c,repeatDelay:d,type:p,onUpdate:f,finalKeyframe:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-n/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>n;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=n);let v=this.currentTime,x=i;if(h){let t=Math.min(this.currentTime,n)/o,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,h+1))%2&&("reverse"===c?(i=1-i,d&&(i-=d/o)):"mirror"===c&&(x=s)),v=B(0,1,i)*o}let w=y?{done:!1,value:u[0]}:x.next(v);r&&(w.value=r(w.value));let{done:b}=w;y||null===a||(b=this.playbackSpeed>=0?this.currentTime>=n:this.currentTime<=0);let T=null===this.holdTime&&("finished"===this.state||"running"===this.state&&b);return T&&p!==tG&&(w.value=el(u,this.options,m,this.speed)),f&&f(w.value),T&&this.finish(),w}then(t,e){return this.finished.then(t,e)}get duration(){return U(this.calculatedDuration)}get time(){return U(this.currentTime)}set time(t){t=I(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(A.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=U(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=tU,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(A.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,N.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}let ef=t=>180*t/Math.PI,em=t=>ey(ef(Math.atan2(t[1],t[0]))),eg={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:em,rotateZ:em,skewX:t=>ef(Math.atan(t[1])),skewY:t=>ef(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},ey=t=>((t%=360)<0&&(t+=360),t),ev=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),ex=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),ew={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:ev,scaleY:ex,scale:t=>(ev(t)+ex(t))/2,rotateX:t=>ey(ef(Math.atan2(t[6],t[5]))),rotateY:t=>ey(ef(Math.atan2(-t[2],t[0]))),rotateZ:em,rotate:em,skewX:t=>ef(Math.atan(t[4])),skewY:t=>ef(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function eb(t){return+!!t.includes("scale")}function eT(t,e){let i,n;if(!t||"none"===t)return eb(e);let r=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(r)i=ew,n=r;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=eg,n=e}if(!n)return eb(e);let s=i[e],o=n[1].split(",").map(eP);return"function"==typeof s?s(o):o[s]}let eS=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return eT(i,e)};function eP(t){return parseFloat(t.trim())}let eA=t=>t===K||t===tu,eE=new Set(["x","y","z"]),eM=v.filter(t=>!eE.has(t)),eD={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>eT(e,"x"),y:(t,{transform:e})=>eT(e,"y")};eD.translateX=eD.x,eD.translateY=eD.y;let eV=new Set,ek=!1,eC=!1,eR=!1;function eL(){if(eC){let t=Array.from(eV).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return eM.forEach(i=>{let n=t.getValue(i);void 0!==n&&(e.push([i,n.get()]),n.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}eC=!1,ek=!1,eV.forEach(t=>t.complete(eR)),eV.clear()}function ej(){eV.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(eC=!0)})}class eF{constructor(t,e,i,n,r,s=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=n,this.element=r,this.isAsync=s}scheduleResolve(){this.state="scheduled",this.isAsync?(eV.add(this),ek||(ek=!0,f.read(ej),f.resolveKeyframes(eL))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:n}=this;if(null===t[0]){let r=n?.get(),s=t[t.length-1];if(void 0!==r)t[0]=r;else if(i&&e){let n=i.readValue(e,s);null!=n&&(t[0]=n)}void 0===t[0]&&(t[0]=s),n&&void 0===r&&n.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),eV.delete(this)}cancel(){"scheduled"===this.state&&(eV.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let eO=t=>t.startsWith("--");function eB(t){let e;return()=>(void 0===e&&(e=t()),e)}let eI=eB(()=>void 0!==window.ScrollTimeline),eU={},eN=function(t,e){let i=eB(t);return()=>eU[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),e$=([t,e,i,n])=>`cubic-bezier(${t}, ${e}, ${i}, ${n})`,eW={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:e$([0,.65,.55,1]),circOut:e$([.55,0,1,.45]),backIn:e$([.31,.01,.66,-.59]),backOut:e$([.33,1.53,.69,.99])};function ez(t){return"function"==typeof t&&"applyToOptions"in t}class eH extends ec{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:n,pseudoElement:r,allowFlatten:s=!1,finalKeyframe:o,onComplete:a}=t;this.isPseudoElement=!!r,this.allowFlatten=s,this.options=t,W("string"!=typeof t.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:t,...e}){return ez(t)&&eN()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:n=0,duration:r=300,repeat:s=0,repeatType:o="loop",ease:a="easeOut",times:l}={},u){let h={[e]:i};l&&(h.offset=l);let c=function t(e,i){if(e)return"function"==typeof e?eN()?tN(e,i):"ease-out":ee(e)?e$(e):Array.isArray(e)?e.map(e=>t(e,i)||eW.easeOut):eW[e]}(a,r);Array.isArray(c)&&(h.easing=c),d.value&&N.waapi++;let p={delay:n,duration:r,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:s+1,direction:"reverse"===o?"alternate":"normal"};u&&(p.pseudoElement=u);let f=t.animate(h,p);return d.value&&f.finished.finally(()=>{N.waapi--}),f}(e,i,n,l,r),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!r){let t=el(n,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){eO(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"===t||"finished"===t||(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return U(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return U(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=I(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&eI())?(this.animation.timeline=t,u):e(this)}}let eY={anticipate:t9,backInOut:t6,circInOut:et};class eX extends eH{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in eY&&(t.ease=eY[t.ease])}(t),eh(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:n,element:r,...s}=this.options;if(!e)return;if(void 0!==t){e.set(t);return}let o=new ep({...s,autoplay:!1}),a=I(this.finishedTime??this.time);e.setWithVelocity(o.sample(a-10).value,o.sample(a).value,10),o.stop()}}let eq=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tS.test(t)||"0"===t)&&!t.startsWith("url("));var eK,eG,e_=i(3117);let eZ=new Set(["opacity","clipPath","filter","transform"]),eQ=eB(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class eJ extends ec{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:n=0,repeatDelay:r=0,repeatType:s="loop",keyframes:o,name:a,motionValue:l,element:u,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=A.now();let c={autoplay:t,delay:e,type:i,repeat:n,repeatDelay:r,repeatType:s,name:a,motionValue:l,element:u,...h},d=u?.KeyframeResolver||eF;this.keyframeResolver=new d(o,(t,e,i)=>this.onKeyframesResolved(t,e,c,!i),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,n){this.keyframeResolver=void 0;let{name:r,type:s,velocity:o,delay:a,isHandoff:l,onUpdate:c}=i;this.resolvedAt=A.now(),!function(t,e,i,n){let r=t[0];if(null===r)return!1;if("display"===e||"visibility"===e)return!0;let s=t[t.length-1],o=eq(r,e),a=eq(s,e);return $(o===a,`You are trying to animate ${e} from "${r}" to "${s}". ${r} is not an animatable value - to enable this animation set ${r} to a value animatable to ${s} via the \`style\` property.`),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||ez(i))&&n)}(t,r,s,o)&&((h.instantAnimations||!a)&&c?.(el(t,i,e)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);let d={startTime:n?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},p=!l&&function(t){let{motionValue:e,name:i,repeatDelay:n,repeatType:r,damping:s,type:o}=t;if(!(0,e_.s)(e?.owner?.current))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return eQ()&&i&&eZ.has(i)&&("transform"!==i||!l)&&!a&&!n&&"mirror"!==r&&0!==s&&"inertia"!==o}(d)?new eX({...d,element:d.motionValue.owner.current}):new ep(d);p.finished.then(()=>this.notifyFinished()).catch(u),this.pendingTimeline&&(this.stopTimeline=p.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=p}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),eR=!0,ej(),eL(),eR=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let e0=t=>null!==t,e1={type:"spring",stiffness:500,damping:25,restSpeed:10},e5=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),e3={type:"keyframes",duration:.8},e2={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},e4=(t,{keyframes:e})=>e.length>2?e3:x.has(t)?t.startsWith("scale")?e5(e[1]):e1:e2,e6=(t,e,i,n={},r,s)=>o=>{let a=l(n,t)||{},u=a.delay||n.delay||0,{elapsed:c=0}=n;c-=I(u);let d={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-c,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:s?void 0:r};!function({when:t,delay:e,delayChildren:i,staggerChildren:n,staggerDirection:r,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(a)&&Object.assign(d,e4(t,d)),d.duration&&(d.duration=I(d.duration)),d.repeatDelay&&(d.repeatDelay=I(d.repeatDelay)),void 0!==d.from&&(d.keyframes[0]=d.from);let p=!1;if(!1!==d.type&&(0!==d.duration||d.repeatDelay)||(d.duration=0,0!==d.delay||(p=!0)),(h.instantAnimations||h.skipAnimations)&&(p=!0,d.duration=0,d.delay=0),d.allowFlatten=!a.type&&!a.ease,p&&!s&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},n){let r=t.filter(e0),s=e&&"loop"!==i&&e%2==1?0:r.length-1;return r[s]}(d.keyframes,a);if(void 0!==t){f.update(()=>{d.onUpdate(t),d.onComplete()});return}}return a.isSync?new ep(d):new eJ(d)};function e9(t,e,{delay:i=0,transitionOverride:n,type:r}={}){let{transition:s=t.getDefaultTransition(),transitionEnd:o,...u}=e;n&&(s=n);let h=[],c=r&&t.animationState&&t.animationState.getState()[r];for(let e in u){let n=t.getValue(e,t.latestValues[e]??null),r=u[e];if(void 0===r||c&&function({protectedKeys:t,needsAnimating:e},i){let n=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,n}(c,e))continue;let o={delay:i,...l(s||{},e)},a=n.get();if(void 0!==a&&!n.isAnimating&&!Array.isArray(r)&&r===a&&!o.velocity)continue;let d=!1;if(window.MotionHandoffAnimation){let i=t.props[j];if(i){let t=window.MotionHandoffAnimation(i,e,f);null!==t&&(o.startTime=t,d=!0)}}R(t,e),n.start(e6(e,n,r,t.shouldReduceMotion&&w.has(e)?{type:!1}:o,t,d));let p=n.animation;p&&h.push(p)}return o&&Promise.all(h).then(()=>{f.update(()=>{o&&function(t,e){let{transitionEnd:i={},transition:n={},...r}=a(t,e)||{};for(let e in r={...r,...i}){var s;let i=k(s=r[e])?s[s.length-1]||0:s;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,V(i))}}(t,o)})}),h}function e8(t,e,i={}){let n=a(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:r=t.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(r=i.transitionOverride);let s=n?()=>Promise.all(e9(t,n,i)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(n=0)=>{let{delayChildren:s=0,staggerChildren:o,staggerDirection:a}=r;return function(t,e,i=0,n=0,r=1,s){let o=[],a=(t.variantChildren.size-1)*n,l=1===r?(t=0)=>t*n:(t=0)=>a-t*n;return Array.from(t.variantChildren).sort(e7).forEach((t,n)=>{t.notify("AnimationStart",e),o.push(e8(t,e,{...s,delay:i+l(n)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,s+n,o,a,i)}:()=>Promise.resolve(),{when:l}=r;if(!l)return Promise.all([s(),o(i.delay)]);{let[t,e]="beforeChildren"===l?[s,o]:[o,s];return t().then(()=>e())}}function e7(t,e){return t.sortNodePosition(e)}function it(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let n=0;n<i;n++)if(e[n]!==t[n])return!1;return!0}function ie(t){return"string"==typeof t||Array.isArray(t)}let ii=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ir=["initial",...ii],is=ir.length,io=[...ii].reverse(),ia=ii.length;function il(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function iu(){return{animate:il(!0),whileInView:il(),whileHover:il(),whileTap:il(),whileDrag:il(),whileFocus:il(),exit:il()}}class ih{constructor(t){this.isMounted=!1,this.node=t}update(){}}class ic extends ih{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let n;if(t.notify("AnimationStart",e),Array.isArray(e))n=Promise.all(e.map(e=>e8(t,e,i)));else if("string"==typeof e)n=e8(t,e,i);else{let r="function"==typeof e?a(t,e,i.custom):e;n=Promise.all(e9(t,r,i))}return n.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=iu(),n=!0,s=e=>(i,n)=>{let r=a(t,n,"exit"===e?t.presenceContext?.custom:void 0);if(r){let{transition:t,transitionEnd:e,...n}=r;i={...i,...n,...e}}return i};function o(o){let{props:l}=t,u=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<is;t++){let n=ir[t],r=e.props[n];(ie(r)||!1===r)&&(i[n]=r)}return i}(t.parent)||{},h=[],c=new Set,d={},p=1/0;for(let e=0;e<ia;e++){var f,m;let a=io[e],g=i[a],y=void 0!==l[a]?l[a]:u[a],v=ie(y),x=a===o?g.isActive:null;!1===x&&(p=e);let w=y===u[a]&&y!==l[a]&&v;if(w&&n&&t.manuallyAnimateOnMount&&(w=!1),g.protectedKeys={...d},!g.isActive&&null===x||!y&&!g.prevProp||r(y)||"boolean"==typeof y)continue;let b=(f=g.prevProp,"string"==typeof(m=y)?m!==f:!!Array.isArray(m)&&!it(m,f)),T=b||a===o&&g.isActive&&!w&&v||e>p&&v,S=!1,P=Array.isArray(y)?y:[y],A=P.reduce(s(a),{});!1===x&&(A={});let{prevResolvedValues:E={}}=g,M={...E,...A},D=e=>{T=!0,c.has(e)&&(S=!0,c.delete(e)),g.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in M){let e=A[t],i=E[t];if(d.hasOwnProperty(t))continue;let n=!1;(k(e)&&k(i)?it(e,i):e===i)?void 0!==e&&c.has(t)?D(t):g.protectedKeys[t]=!0:null!=e?D(t):c.add(t)}g.prevProp=y,g.prevResolvedValues=A,g.isActive&&(d={...d,...A}),n&&t.blockInitialAnimation&&(T=!1);let V=!(w&&b)||S;T&&V&&h.push(...P.map(t=>({animation:t,options:{type:a}})))}if(c.size){let e={};if("boolean"!=typeof l.initial){let i=a(t,Array.isArray(l.initial)?l.initial[0]:l.initial);i&&i.transition&&(e.transition=i.transition)}c.forEach(i=>{let n=t.getBaseTarget(i),r=t.getValue(i);r&&(r.liveStyle=!0),e[i]=n??null}),h.push({animation:e})}let g=!!h.length;return n&&(!1===l.initial||l.initial===l.animate)&&!t.manuallyAnimateOnMount&&(g=!1),n=!1,g?e(h):Promise.resolve()}return{animateChanges:o,setActive:function(e,n){if(i[e].isActive===n)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,n)),i[e].isActive=n;let r=o(e);for(let t in i)i[t].protectedKeys={};return r},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=iu(),n=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();r(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let id=0;class ip extends ih{constructor(){super(...arguments),this.id=id++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let n=this.node.animationState.setActive("exit",!t);e&&!t&&n.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let im={x:!1,y:!1};function ig(t,e,i,n={passive:!0}){return t.addEventListener(e,i,n),()=>t.removeEventListener(e,i)}let iy=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function iv(t){return{point:{x:t.pageX,y:t.pageY}}}let ix=t=>e=>iy(e)&&t(e,iv(e));function iw(t,e,i,n){return ig(t,e,ix(i),n)}function ib({top:t,left:e,right:i,bottom:n}){return{x:{min:e,max:i},y:{min:t,max:n}}}function iT(t){return t.max-t.min}function iS(t,e,i,n=.5){t.origin=n,t.originPoint=tE(e.min,e.max,t.origin),t.scale=iT(i)/iT(e),t.translate=tE(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function iP(t,e,i,n){iS(t.x,e.x,i.x,n?n.originX:void 0),iS(t.y,e.y,i.y,n?n.originY:void 0)}function iA(t,e,i){t.min=i.min+e.min,t.max=t.min+iT(e)}function iE(t,e,i){t.min=e.min-i.min,t.max=t.min+iT(e)}function iM(t,e,i){iE(t.x,e.x,i.x),iE(t.y,e.y,i.y)}let iD=()=>({translate:0,scale:1,origin:0,originPoint:0}),iV=()=>({x:iD(),y:iD()}),ik=()=>({min:0,max:0}),iC=()=>({x:ik(),y:ik()});function iR(t){return[t("x"),t("y")]}function iL(t){return void 0===t||1===t}function ij({scale:t,scaleX:e,scaleY:i}){return!iL(t)||!iL(e)||!iL(i)}function iF(t){return ij(t)||iO(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function iO(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function iB(t,e,i,n,r){return void 0!==r&&(t=n+r*(t-n)),n+i*(t-n)+e}function iI(t,e=0,i=1,n,r){t.min=iB(t.min,e,i,n,r),t.max=iB(t.max,e,i,n,r)}function iU(t,{x:e,y:i}){iI(t.x,e.translate,e.scale,e.originPoint),iI(t.y,i.translate,i.scale,i.originPoint)}function iN(t,e){t.min=t.min+e,t.max=t.max+e}function i$(t,e,i,n,r=.5){let s=tE(t.min,t.max,r);iI(t,e,i,s,n)}function iW(t,e){i$(t.x,e.x,e.scaleX,e.scale,e.originX),i$(t.y,e.y,e.scaleY,e.scale,e.originY)}function iz(t,e){return ib(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:n.y,right:n.x}}(t.getBoundingClientRect(),e))}let iH=({current:t})=>t?t.ownerDocument.defaultView:null;function iY(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let iX=(t,e)=>Math.abs(t-e);class iq{constructor(t,e,{transformPagePoint:i,contextWindow:n,dragSnapToOrigin:r=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=i_(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(iX(t.x,e.x)**2+iX(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:n}=t,{timestamp:r}=g;this.history.push({...n,timestamp:r});let{onStart:s,onMove:o}=this.handlers;e||(s&&s(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=iK(e,this.transformPagePoint),f.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:n,resumeAnimation:r}=this.handlers;if(this.dragSnapToOrigin&&r&&r(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=i_("pointercancel"===t.type?this.lastMoveEventInfo:iK(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,s),n&&n(t,s)},!iy(t))return;this.dragSnapToOrigin=r,this.handlers=e,this.transformPagePoint=i,this.contextWindow=n||window;let s=iK(iv(t),this.transformPagePoint),{point:o}=s,{timestamp:a}=g;this.history=[{...o,timestamp:a}];let{onSessionStart:l}=e;l&&l(t,i_(s,this.history)),this.removeListeners=O(iw(this.contextWindow,"pointermove",this.handlePointerMove),iw(this.contextWindow,"pointerup",this.handlePointerUp),iw(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),m(this.updatePoint)}}function iK(t,e){return e?{point:e(t.point)}:t}function iG(t,e){return{x:t.x-e.x,y:t.y-e.y}}function i_({point:t},e){return{point:t,delta:iG(t,iZ(e)),offset:iG(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,n=null,r=iZ(t);for(;i>=0&&(n=t[i],!(r.timestamp-n.timestamp>I(.1)));)i--;if(!n)return{x:0,y:0};let s=U(r.timestamp-n.timestamp);if(0===s)return{x:0,y:0};let o={x:(r.x-n.x)/s,y:(r.y-n.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,.1)}}function iZ(t){return t[t.length-1]}function iQ(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function iJ(t,e){let i=e.min-t.min,n=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,n]=[n,i]),{min:i,max:n}}function i0(t,e,i){return{min:i1(t,e),max:i1(t,i)}}function i1(t,e){return"number"==typeof t?t:t[e]||0}let i5=new WeakMap;class i3{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=iC(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:n}=this.getProps();this.panSession=new iq(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(iv(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:n,onDragStart:r}=this.getProps();if(i&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(im[t])return null;else return im[t]=!0,()=>{im[t]=!1};return im.x||im.y?null:(im.x=im.y=!0,()=>{im.x=im.y=!1})}(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),iR(t=>{let e=this.getAxisMotionValue(t).get()||0;if(tl.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let n=i.layout.layoutBox[t];n&&(e=iT(n)*(parseFloat(e)/100))}}this.originPoint[t]=e}),r&&f.postRender(()=>r(t,e)),R(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:n,onDirectionLock:r,onDrag:s}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(n&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&r&&r(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),s&&s(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>iR(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:n,contextWindow:iH(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:n}=e;this.startAnimation(n);let{onDragEnd:r}=this.getProps();r&&f.postRender(()=>r(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:n}=this.getProps();if(!i||!i2(t,n,this.currentDirection))return;let r=this.getAxisMotionValue(t),s=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(s=function(t,{min:e,max:i},n){return void 0!==e&&t<e?t=n?tE(e,t,n.min):Math.max(t,e):void 0!==i&&t>i&&(t=n?tE(i,t,n.max):Math.min(t,i)),t}(s,this.constraints[t],this.elastic[t])),r.set(s)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,n=this.constraints;t&&iY(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:n,right:r}){return{x:iQ(t.x,i,r),y:iQ(t.y,e,n)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:i0(t,"left","right"),y:i0(t,"top","bottom")}}(e),n!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&iR(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!iY(e))return!1;let n=e.current;W(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:r}=this.visualElement;if(!r||!r.layout)return!1;let s=function(t,e,i){let n=iz(t,i),{scroll:r}=e;return r&&(iN(n.x,r.offset.x),iN(n.y,r.offset.y)),n}(n,r.root,this.visualElement.getTransformPagePoint()),o={x:iJ((t=r.layout.layoutBox).x,s.x),y:iJ(t.y,s.y)};if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=ib(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:n,dragTransition:r,dragSnapToOrigin:s,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(iR(o=>{if(!i2(o,e,this.currentDirection))return;let l=a&&a[o]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[o]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...r,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return R(this.visualElement,t),i.start(e6(t,i,0,e,this.visualElement,!1))}stopAnimation(){iR(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){iR(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){iR(e=>{let{drag:i}=this.getProps();if(!i2(e,i,this.currentDirection))return;let{projection:n}=this.visualElement,r=this.getAxisMotionValue(e);if(n&&n.layout){let{min:i,max:s}=n.layout.layoutBox[e];r.set(t[e]-tE(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!iY(e)||!i||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};iR(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();n[t]=function(t,e){let i=.5,n=iT(t),r=iT(e);return r>n?i=es(e.min,e.max-n,t.min):n>r&&(i=es(t.min,t.max-r,e.min)),B(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),iR(e=>{if(!i2(e,t,null))return;let i=this.getAxisMotionValue(e),{min:r,max:s}=this.constraints[e];i.set(tE(r,s,n[e]))})}addListeners(){if(!this.visualElement.current)return;i5.set(this.visualElement,this);let t=iw(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();iY(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,n=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),f.read(e);let r=ig(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(iR(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{r(),t(),n(),s&&s()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:n=!1,dragConstraints:r=!1,dragElastic:s=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:n,dragConstraints:r,dragElastic:s,dragMomentum:o}}}function i2(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class i4 extends ih{constructor(t){super(t),this.removeGroupControls=u,this.removeListeners=u,this.controls=new i3(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||u}unmount(){this.removeGroupControls(),this.removeListeners()}}let i6=t=>(e,i)=>{t&&f.postRender(()=>t(e,i))};class i9 extends ih{constructor(){super(...arguments),this.removePointerDownListener=u}onPointerDown(t){this.session=new iq(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:iH(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:n}=this.node.getProps();return{onSessionStart:i6(t),onStart:i6(e),onMove:i,onEnd:(t,e)=>{delete this.session,n&&f.postRender(()=>n(t,e))}}}mount(){this.removePointerDownListener=iw(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var i8=i(47093);let{schedule:i7}=p(queueMicrotask,!1);var nt=i(34545),ne=i(21803),ni=i(35338);let nn=(0,nt.createContext)({}),nr={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function ns(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let no={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!tu.test(t))return t;else t=parseFloat(t);let i=ns(t,e.target.x),n=ns(t,e.target.y);return`${i}% ${n}%`}},na={};class nl extends nt.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:n}=this.props,{projection:r}=t;for(let t in nh)na[t]=nh[t],H(t)&&(na[t].isCSSVariable=!0);r&&(e.group&&e.group.add(r),i&&i.register&&n&&i.register(r),r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),nr.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:n,isPresent:r}=this.props,{projection:s}=i;return s&&(s.isPresent=r,n||t.layoutDependency!==e||void 0===e||t.isPresent!==r?s.willUpdate():this.safeToRemove(),t.isPresent===r||(r?s.promote():s.relegate()||f.postRender(()=>{let t=s.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),i7.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:n}=t;n&&(n.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(n),i&&i.deregister&&i.deregister(n))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function nu(t){let[e,i]=(0,ne.xQ)(),n=(0,nt.useContext)(ni.L);return(0,i8.jsx)(nl,{...t,layoutGroup:n,switchLayoutGroup:(0,nt.useContext)(nn),isPresent:e,safeToRemove:i})}let nh={borderRadius:{...no,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:no,borderTopRightRadius:no,borderBottomLeftRadius:no,borderBottomRightRadius:no,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let n=tS.parse(t);if(n.length>5)return t;let r=tS.createTransformer(t),s=+("number"!=typeof n[0]),o=i.x.scale*e.x,a=i.y.scale*e.y;n[0+s]/=o,n[1+s]/=a;let l=tE(o,a,.5);return"number"==typeof n[2+s]&&(n[2+s]/=l),"number"==typeof n[3+s]&&(n[3+s]/=l),r(n)}}};var nc=i(14787);function nd(t){return(0,nc.G)(t)&&"ownerSVGElement"in t}let np=(t,e)=>t.depth-e.depth;class nf{constructor(){this.children=[],this.isDirty=!1}add(t){b(this.children,t),this.isDirty=!0}remove(t){T(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(np),this.isDirty=!1,this.children.forEach(t)}}function nm(t){return C(t)?t.get():t}let ng=["TopLeft","TopRight","BottomLeft","BottomRight"],ny=ng.length,nv=t=>"string"==typeof t?parseFloat(t):t,nx=t=>"number"==typeof t||tu.test(t);function nw(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let nb=nS(0,.5,t7),nT=nS(.5,.95,u);function nS(t,e,i){return n=>n<t?0:n>e?1:i(es(t,e,n))}function nP(t,e){t.min=e.min,t.max=e.max}function nA(t,e){nP(t.x,e.x),nP(t.y,e.y)}function nE(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function nM(t,e,i,n,r){return t-=e,t=n+1/i*(t-n),void 0!==r&&(t=n+1/r*(t-n)),t}function nD(t,e,[i,n,r],s,o){!function(t,e=0,i=1,n=.5,r,s=t,o=t){if(tl.test(e)&&(e=parseFloat(e),e=tE(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=tE(s.min,s.max,n);t===s&&(a-=e),t.min=nM(t.min,e,i,a,r),t.max=nM(t.max,e,i,a,r)}(t,e[i],e[n],e[r],e.scale,s,o)}let nV=["x","scaleX","originX"],nk=["y","scaleY","originY"];function nC(t,e,i,n){nD(t.x,e,nV,i?i.x:void 0,n?n.x:void 0),nD(t.y,e,nk,i?i.y:void 0,n?n.y:void 0)}function nR(t){return 0===t.translate&&1===t.scale}function nL(t){return nR(t.x)&&nR(t.y)}function nj(t,e){return t.min===e.min&&t.max===e.max}function nF(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function nO(t,e){return nF(t.x,e.x)&&nF(t.y,e.y)}function nB(t){return iT(t.x)/iT(t.y)}function nI(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class nU{constructor(){this.members=[]}add(t){b(this.members,t),t.scheduleRender()}remove(t){if(T(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:n}=t.options;!1===n&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let nN={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},n$=["","X","Y","Z"],nW={visibility:"hidden"},nz=0;function nH(t,e,i,n){let{latestValues:r}=e;r[t]&&(i[t]=r[t],e.setStaticValue(t,0),n&&(n[t]=0))}function nY({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:n,resetTransform:r}){return class{constructor(t={},i=e?.()){this.id=nz++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,d.value&&(nN.nodes=nN.calculatedTargetDeltas=nN.calculatedProjections=0),this.nodes.forEach(nK),this.nodes.forEach(n1),this.nodes.forEach(n5),this.nodes.forEach(nG),d.addProjectionMetrics&&d.addProjectionMetrics(nN)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new nf)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new S),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=nd(e)&&!(nd(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:i,layout:n,visualElement:r}=this.options;if(r&&!r.current&&r.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(n||i)&&(this.isLayoutDirty=!0),t){let i,n=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=A.now(),n=({timestamp:r})=>{let s=r-i;s>=250&&(m(n),t(s-e))};return f.setup(n,!0),()=>m(n)}(n,250),nr.hasAnimatedSinceResize&&(nr.hasAnimatedSinceResize=!1,this.nodes.forEach(n0))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&r&&(i||n)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let s=this.options.transition||r.getDefaultTransition()||n8,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=r.getProps(),u=!this.targetLayout||!nO(this.targetLayout,n),h=!e&&i;if(this.options.layoutRoot||this.resumeFrom||h||e&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...l(s,"layout"),onPlay:o,onComplete:a};(r.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,h)}else e||n0(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),m(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(n3),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let n=i.props[j];if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",f,!(t||i))}let{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&t(r)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(nZ);return}this.isUpdating||this.nodes.forEach(nQ),this.isUpdating=!1,this.nodes.forEach(nJ),this.nodes.forEach(nX),this.nodes.forEach(nq),this.clearAllSnapshots();let t=A.now();g.delta=B(0,1e3/60,t-g.timestamp),g.timestamp=t,g.isProcessing=!0,y.update.process(g),y.preRender.process(g),y.render.process(g),g.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,i7.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(n_),this.sharedNodes.forEach(n2)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,f.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){f.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),!this.snapshot||iT(this.snapshot.measuredBox.x)||iT(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=iC(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=n(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!r)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!nL(this.projectionDelta),i=this.getTransformTemplate(),n=i?i(this.latestValues,""):void 0,s=n!==this.prevTransformTemplateValue;t&&this.instance&&(e||iF(this.latestValues)||s)&&(r(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),n=this.removeElementScroll(i);return t&&(n=this.removeTransform(n)),re((e=n).x),re(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return iC();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(rn))){let{scroll:t}=this.root;t&&(iN(e.x,t.offset.x),iN(e.y,t.offset.y))}return e}removeElementScroll(t){let e=iC();if(nA(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let n=this.path[i],{scroll:r,options:s}=n;n!==this.root&&r&&s.layoutScroll&&(r.wasRoot&&nA(e,t),iN(e.x,r.offset.x),iN(e.y,r.offset.y))}return e}applyTransform(t,e=!1){let i=iC();nA(i,t);for(let t=0;t<this.path.length;t++){let n=this.path[t];!e&&n.options.layoutScroll&&n.scroll&&n!==n.root&&iW(i,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),iF(n.latestValues)&&iW(i,n.latestValues)}return iF(this.latestValues)&&iW(i,this.latestValues),i}removeTransform(t){let e=iC();nA(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!iF(i.latestValues))continue;ij(i.latestValues)&&i.updateSnapshot();let n=iC();nA(n,i.measurePageBox()),nC(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,n)}return iF(this.latestValues)&&nC(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==g.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:n,layoutId:r}=this.options;if(this.layout&&(n||r)){if(this.resolvedRelativeTargetAt=g.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iC(),this.relativeTargetOrigin=iC(),iM(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),nA(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=iC(),this.targetWithTransforms=iC()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var s,o,a;this.forceRelativeParentToResolveTarget(),s=this.target,o=this.relativeTarget,a=this.relativeParent.target,iA(s.x,o.x,a.x),iA(s.y,o.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):nA(this.target,this.layout.layoutBox),iU(this.target,this.targetDelta)):nA(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iC(),this.relativeTargetOrigin=iC(),iM(this.relativeTargetOrigin,this.target,t.target),nA(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}d.value&&nN.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||ij(this.parent.latestValues)||iO(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===g.timestamp&&(i=!1),i)return;let{layout:n,layoutId:r}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||r))return;nA(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,o=this.treeScale.y;!function(t,e,i,n=!1){let r,s,o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){s=(r=i[a]).projectionDelta;let{visualElement:o}=r.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(n&&r.options.layoutScroll&&r.scroll&&r!==r.root&&iW(t,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),s&&(e.x*=s.x.scale,e.y*=s.y.scale,iU(t,s)),n&&iF(r.latestValues)&&iW(t,r.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=iC());let{target:a}=t;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(nE(this.prevProjectionDelta.x,this.projectionDelta.x),nE(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),iP(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===s&&this.treeScale.y===o&&nI(this.projectionDelta.x,this.prevProjectionDelta.x)&&nI(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),d.value&&nN.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=iV(),this.projectionDelta=iV(),this.projectionDeltaWithTransform=iV()}setAnimationOrigin(t,e=!1){let i,n=this.snapshot,r=n?n.latestValues:{},s={...this.latestValues},o=iV();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=iC(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,c=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(n9));this.animationProgress=0,this.mixTargetDelta=e=>{let n=e/1e3;if(n4(o.x,t.x,n),n4(o.y,t.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,d,p,f,m,g;if(iM(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,f=this.relativeTargetOrigin,m=a,g=n,n6(p.x,f.x,m.x,g),n6(p.y,f.y,m.y,g),i&&(u=this.relativeTarget,d=i,nj(u.x,d.x)&&nj(u.y,d.y)))this.isProjectionDirty=!1;i||(i=iC()),nA(i,this.relativeTarget)}l&&(this.animationValues=s,function(t,e,i,n,r,s){r?(t.opacity=tE(0,i.opacity??1,nb(n)),t.opacityExit=tE(e.opacity??1,0,nT(n))):s&&(t.opacity=tE(e.opacity??1,i.opacity??1,n));for(let r=0;r<ny;r++){let s=`border${ng[r]}Radius`,o=nw(e,s),a=nw(i,s);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||nx(o)===nx(a)?(t[s]=Math.max(tE(nv(o),nv(a),n),0),(tl.test(a)||tl.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||i.rotate)&&(t.rotate=tE(e.rotate||0,i.rotate||0,n))}(s,r,this.latestValues,n,c,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(m(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=f.update(()=>{nr.hasAnimatedSinceResize=!0,N.layout++,this.motionValue||(this.motionValue=V(0)),this.currentAnimation=function(t,e,i){let n=C(t)?t:V(t);return n.start(e6("",n,e,i)),n.animation}(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{N.layout--},onComplete:()=>{N.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:n,latestValues:r}=t;if(e&&i&&n){if(this!==t&&this.layout&&n&&ri(this.options.animationType,this.layout.layoutBox,n.layoutBox)){i=this.target||iC();let e=iT(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let n=iT(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+n}nA(e,i),iW(e,r),iP(this.projectionDeltaWithTransform,this.layoutCorrected,e,r)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new nU),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let n=this.getStack();n&&n.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let n={};i.z&&nH("z",t,n,this.animationValues);for(let e=0;e<n$.length;e++)nH(`rotate${n$[e]}`,t,n,this.animationValues),nH(`skew${n$[e]}`,t,n,this.animationValues);for(let e in t.render(),n)t.setStaticValue(e,n[e]),this.animationValues&&(this.animationValues[e]=n[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return nW;let e={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=nm(t?.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none",e;let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=nm(t?.pointerEvents)||""),this.hasProjected&&!iF(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1),e}let r=n.animationValues||n.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,i){let n="",r=t.x.translate/e.x,s=t.y.translate/e.y,o=i?.z||0;if((r||s||o)&&(n=`translate3d(${r}px, ${s}px, ${o}px) `),(1!==e.x||1!==e.y)&&(n+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:r,rotateY:s,skewX:o,skewY:a}=i;t&&(n=`perspective(${t}px) ${n}`),e&&(n+=`rotate(${e}deg) `),r&&(n+=`rotateX(${r}deg) `),s&&(n+=`rotateY(${s}deg) `),o&&(n+=`skewX(${o}deg) `),a&&(n+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(n+=`scale(${a}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,r),i&&(e.transform=i(r,e.transform));let{x:s,y:o}=this.projectionDelta;for(let t in e.transformOrigin=`${100*s.origin}% ${100*o.origin}% 0`,n.animationValues?e.opacity=n===this?r.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:r.opacityExit:e.opacity=n===this?void 0!==r.opacity?r.opacity:"":void 0!==r.opacityExit?r.opacityExit:0,na){if(void 0===r[t])continue;let{correct:i,applyTo:s,isCSSVariable:o}=na[t],a="none"===e.transform?r[t]:i(r[t],n);if(s){let t=s.length;for(let i=0;i<t;i++)e[s[i]]=a}else o?this.options.visualElement.renderState.vars[t]=a:e[t]=a}return this.options.layoutId&&(e.pointerEvents=n===this?nm(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(nZ),this.root.sharedNodes.clear()}}}function nX(t){t.updateLayout()}function nq(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:n}=t.layout,{animationType:r}=t.options,s=e.source!==t.layout.source;"size"===r?iR(t=>{let n=s?e.measuredBox[t]:e.layoutBox[t],r=iT(n);n.min=i[t].min,n.max=n.min+r}):ri(r,e.layoutBox,i)&&iR(n=>{let r=s?e.measuredBox[n]:e.layoutBox[n],o=iT(i[n]);r.max=r.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[n].max=t.relativeTarget[n].min+o)});let o=iV();iP(o,i,e.layoutBox);let a=iV();s?iP(a,t.applyTransform(n,!0),e.measuredBox):iP(a,i,e.layoutBox);let l=!nL(o),u=!1;if(!t.resumeFrom){let n=t.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:r,layout:s}=n;if(r&&s){let o=iC();iM(o,e.layoutBox,r.layoutBox);let a=iC();iM(a,i,s.layoutBox),nO(o,a)||(u=!0),n.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=n)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function nK(t){d.value&&nN.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function nG(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function n_(t){t.clearSnapshot()}function nZ(t){t.clearMeasurements()}function nQ(t){t.isLayoutDirty=!1}function nJ(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function n0(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function n1(t){t.resolveTargetDelta()}function n5(t){t.calcProjection()}function n3(t){t.resetSkewAndRotation()}function n2(t){t.removeLeadSnapshot()}function n4(t,e,i){t.translate=tE(e.translate,0,i),t.scale=tE(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function n6(t,e,i,n){t.min=tE(e.min,i.min,n),t.max=tE(e.max,i.max,n)}function n9(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let n8={duration:.45,ease:[.4,0,.1,1]},n7=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),rt=n7("applewebkit/")&&!n7("chrome/")?Math.round:u;function re(t){t.min=rt(t.min),t.max=rt(t.max)}function ri(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(nB(e)-nB(i)))}function rn(t){return t!==t.root&&t.scroll?.wasRoot}let rr=nY({attachResizeListener:(t,e)=>ig(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),rs={current:void 0},ro=nY({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!rs.current){let t=new rr({});t.mount(window),t.setOptions({layoutScroll:!0}),rs.current=t}return rs.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function ra(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,i=(void 0)??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),n=new AbortController;return[i,{passive:!0,...e,signal:n.signal},()=>n.abort()]}function rl(t){return!("touch"===t.pointerType||im.x||im.y)}function ru(t,e,i){let{props:n}=t;t.animationState&&n.whileHover&&t.animationState.setActive("whileHover","Start"===i);let r=n["onHover"+i];r&&f.postRender(()=>r(e,iv(e)))}class rh extends ih{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,r,s]=ra(t,i),o=t=>{if(!rl(t))return;let{target:i}=t,n=e(i,t);if("function"!=typeof n||!i)return;let s=t=>{rl(t)&&(n(t),i.removeEventListener("pointerleave",s))};i.addEventListener("pointerleave",s,r)};return n.forEach(t=>{t.addEventListener("pointerenter",o,r)}),s}(t,(t,e)=>(ru(this.node,e,"Start"),t=>ru(this.node,t,"End"))))}unmount(){}}class rc extends ih{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=O(ig(this.node.current,"focus",()=>this.onFocus()),ig(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let rd=(t,e)=>!!e&&(t===e||rd(t,e.parentElement)),rp=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),rf=new WeakSet;function rm(t){return e=>{"Enter"===e.key&&t(e)}}function rg(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let ry=(t,e)=>{let i=t.currentTarget;if(!i)return;let n=rm(()=>{if(rf.has(i))return;rg(i,"down");let t=rm(()=>{rg(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>rg(i,"cancel"),e)});i.addEventListener("keydown",n,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",n),e)};function rv(t){return iy(t)&&!(im.x||im.y)}function rx(t,e,i){let{props:n}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&n.whileTap&&t.animationState.setActive("whileTap","Start"===i);let r=n["onTap"+("End"===i?"":i)];r&&f.postRender(()=>r(e,iv(e)))}class rw extends ih{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,r,s]=ra(t,i),o=t=>{let n=t.currentTarget;if(!rv(t))return;rf.add(n);let s=e(n,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),rf.has(n)&&rf.delete(n),rv(t)&&"function"==typeof s&&s(t,{success:e})},a=t=>{o(t,n===window||n===document||i.useGlobalTarget||rd(n,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,r),window.addEventListener("pointercancel",l,r)};return n.forEach(t=>{if((i.useGlobalTarget?window:t).addEventListener("pointerdown",o,r),(0,e_.s)(t))t.addEventListener("focus",t=>ry(t,r)),!rp.has(t.tagName)&&-1===t.tabIndex&&!t.hasAttribute("tabindex")&&(t.tabIndex=0)}),s}(t,(t,e)=>(rx(this.node,e,"Start"),(t,{success:e})=>rx(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let rb=new WeakMap,rT=new WeakMap,rS=t=>{let e=rb.get(t.target);e&&e(t)},rP=t=>{t.forEach(rS)},rA={some:0,all:1};class rE extends ih{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:n="some",once:r}=t,s={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof n?n:rA[n]};return function(t,e,i){let n=function({root:t,...e}){let i=t||document;rT.has(i)||rT.set(i,{});let n=rT.get(i),r=JSON.stringify(e);return n[r]||(n[r]=new IntersectionObserver(rP,{root:t,...e})),n[r]}(e);return rb.set(t,i),n.observe(t),()=>{rb.delete(t),n.unobserve(t)}}(this.node.current,s,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,r&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:n}=this.node.getProps(),s=e?i:n;s&&s(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let rM=(0,nt.createContext)({strict:!1});var rD=i(90221);let rV=(0,nt.createContext)({});function rk(t){return r(t.animate)||ir.some(e=>ie(t[e]))}function rC(t){return!!(rk(t)||t.variants)}function rR(t){return Array.isArray(t)?t.join(" "):t}var rL=i(83563);let rj={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},rF={};for(let t in rj)rF[t]={isEnabled:e=>rj[t].some(t=>!!e[t])};let rO=Symbol.for("motionComponentSymbol");var rB=i(48844),rI=i(76855);function rU(t,{layout:e,layoutId:i}){return x.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!na[t]||"opacity"===t)}let rN=(t,e)=>e&&"number"==typeof t?e.transform(t):t,r$={...K,transform:Math.round},rW={borderWidth:tu,borderTopWidth:tu,borderRightWidth:tu,borderBottomWidth:tu,borderLeftWidth:tu,borderRadius:tu,radius:tu,borderTopLeftRadius:tu,borderTopRightRadius:tu,borderBottomRightRadius:tu,borderBottomLeftRadius:tu,width:tu,maxWidth:tu,height:tu,maxHeight:tu,top:tu,right:tu,bottom:tu,left:tu,padding:tu,paddingTop:tu,paddingRight:tu,paddingBottom:tu,paddingLeft:tu,margin:tu,marginTop:tu,marginRight:tu,marginBottom:tu,marginLeft:tu,backgroundPositionX:tu,backgroundPositionY:tu,rotate:ta,rotateX:ta,rotateY:ta,rotateZ:ta,scale:_,scaleX:_,scaleY:_,scaleZ:_,skew:ta,skewX:ta,skewY:ta,distance:tu,translateX:tu,translateY:tu,translateZ:tu,x:tu,y:tu,z:tu,perspective:tu,transformPerspective:tu,opacity:G,originX:td,originY:td,originZ:tu,zIndex:r$,fillOpacity:G,strokeOpacity:G,numOctaves:r$},rz={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},rH=v.length;function rY(t,e,i){let{style:n,vars:r,transformOrigin:s}=t,o=!1,a=!1;for(let t in e){let i=e[t];if(x.has(t)){o=!0;continue}if(H(t)){r[t]=i;continue}{let e=rN(i,rW[t]);t.startsWith("origin")?(a=!0,s[t]=e):n[t]=e}}if(!e.transform&&(o||i?n.transform=function(t,e,i){let n="",r=!0;for(let s=0;s<rH;s++){let o=v[s],a=t[o];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!o.startsWith("scale"):0===parseFloat(a))||i){let t=rN(a,rW[o]);if(!l){r=!1;let e=rz[o]||o;n+=`${e}(${t}) `}i&&(e[o]=t)}}return n=n.trim(),i?n=i(e,r?"":n):r&&(n="none"),n}(e,t.transform,i):n.transform&&(n.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:i=0}=s;n.transformOrigin=`${t} ${e} ${i}`}}let rX=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function rq(t,e,i){for(let n in e)C(e[n])||rU(n,i)||(t[n]=e[n])}let rK={offset:"stroke-dashoffset",array:"stroke-dasharray"},rG={offset:"strokeDashoffset",array:"strokeDasharray"};function r_(t,{attrX:e,attrY:i,attrScale:n,pathLength:r,pathSpacing:s=1,pathOffset:o=0,...a},l,u,h){if(rY(t,a,u),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:c,style:d}=t;c.transform&&(d.transform=c.transform,delete c.transform),(d.transform||c.transformOrigin)&&(d.transformOrigin=c.transformOrigin??"50% 50%",delete c.transformOrigin),d.transform&&(d.transformBox=h?.transformBox??"fill-box",delete c.transformBox),void 0!==e&&(c.x=e),void 0!==i&&(c.y=i),void 0!==n&&(c.scale=n),void 0!==r&&function(t,e,i=1,n=0,r=!0){t.pathLength=1;let s=r?rK:rG;t[s.offset]=tu.transform(-n);let o=tu.transform(e),a=tu.transform(i);t[s.array]=`${o} ${a}`}(c,r,s,o,!1)}let rZ=()=>({...rX(),attrs:{}}),rQ=t=>"string"==typeof t&&"svg"===t.toLowerCase(),rJ=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function r0(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||rJ.has(t)}let r1=t=>!r0(t);try{!function(t){t&&(r1=e=>e.startsWith("on")?!r0(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}let r5=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function r3(t){if("string"!=typeof t||t.includes("-"));else if(r5.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var r2=i(50630);let r4=t=>(e,i)=>{let n=(0,nt.useContext)(rV),s=(0,nt.useContext)(rB.t),a=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,n,s){return{latestValues:function(t,e,i,n){let s={},a=n(t,{});for(let t in a)s[t]=nm(a[t]);let{initial:l,animate:u}=t,h=rk(t),c=rC(t);e&&c&&!h&&!1!==t.inherit&&(void 0===l&&(l=e.initial),void 0===u&&(u=e.animate));let d=!!i&&!1===i.initial,p=(d=d||!1===l)?u:l;if(p&&"boolean"!=typeof p&&!r(p)){let e=Array.isArray(p)?p:[p];for(let i=0;i<e.length;i++){let n=o(t,e[i]);if(n){let{transitionEnd:t,transition:e,...i}=n;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=d?e.length-1:0;e=e[t]}null!==e&&(s[t]=e)}for(let e in t)s[e]=t[e]}}}return s}(i,n,s,t),renderState:e()}})(t,e,n,s);return i?a():(0,r2.M)(a)};function r6(t,e,i){let{style:n}=t,r={};for(let s in n)(C(n[s])||e.style&&C(e.style[s])||rU(s,t)||i?.getValue(s)?.liveStyle!==void 0)&&(r[s]=n[s]);return r}let r9={useVisualState:r4({scrapeMotionValuesFromProps:r6,createRenderState:rX})};function r8(t,e,i){let n=r6(t,e,i);for(let i in t)(C(t[i])||C(e[i]))&&(n[-1!==v.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return n}let r7={useVisualState:r4({scrapeMotionValuesFromProps:r8,createRenderState:rZ})},st=t=>e=>e.test(t),se=[K,tu,tl,ta,tc,th,{test:t=>"auto"===t,parse:t=>t}],si=t=>se.find(st(t)),sn=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),sr=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,ss=t=>/^0[^.\s]+$/u.test(t),so=new Set(["brightness","contrast","saturate","opacity"]);function sa(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[n]=i.match(Q)||[];if(!n)return t;let r=i.replace(n,""),s=+!!so.has(e);return n!==i&&(s*=100),e+"("+s+r+")"}let sl=/\b([a-z-]*)\(.*?\)/gu,su={...tS,getAnimatableNone:t=>{let e=t.match(sl);return e?e.map(sa).join(" "):t}},sh={...rW,color:tf,backgroundColor:tf,outlineColor:tf,fill:tf,stroke:tf,borderColor:tf,borderTopColor:tf,borderRightColor:tf,borderBottomColor:tf,borderLeftColor:tf,filter:su,WebkitFilter:su},sc=t=>sh[t];function sd(t,e){let i=sc(t);return i!==su&&(i=tS),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let sp=new Set(["auto","none","0"]);class sf extends eF{constructor(t,e,i,n,r){super(t,e,i,n,r,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let n=t[i];if("string"==typeof n&&X(n=n.trim())){let r=function t(e,i,n=1){W(n<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[r,s]=function(t){let e=sr.exec(t);if(!e)return[,];let[,i,n,r]=e;return[`--${i??n}`,r]}(e);if(!r)return;let o=window.getComputedStyle(i).getPropertyValue(r);if(o){let t=o.trim();return sn(t)?parseFloat(t):t}return X(s)?t(s,i,n+1):s}(n,e.current);void 0!==r&&(t[i]=r),i===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!w.has(i)||2!==t.length)return;let[n,r]=t,s=si(n),o=si(r);if(s!==o)if(eA(s)&&eA(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else eD[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var n;(null===t[e]||("number"==typeof(n=t[e])?0===n:null===n||"none"===n||"0"===n||ss(n)))&&i.push(e)}if(i.length){let n,r=0;for(;r<t.length&&!n;){let e=t[r];"string"==typeof e&&!sp.has(e)&&tx(e).values.length&&(n=t[r]),r++}if(n&&e)for(let r of i)t[r]=sd(e,n)}}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=eD[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let n=e[e.length-1];void 0!==n&&t.getValue(i,n).jump(n,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let n=t.getValue(e);n&&n.jump(this.measuredOrigin,!1);let r=i.length-1,s=i[r];i[r]=eD[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let sm=[...se,tf,tS],sg=t=>sm.find(st(t)),sy={current:null},sv={current:!1},sx=new WeakMap,sw=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class sb{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:n,blockInitialAnimation:r,visualState:s},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=eF,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=A.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,f.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=s;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=n,this.options=o,this.blockInitialAnimation=!!r,this.isControllingVariants=rk(e),this.isVariantNode=rC(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:u,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in h){let e=h[t];void 0!==a[t]&&C(e)&&e.set(a[t],!1)}}mount(t){if(this.current=t,sx.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),!sv.current&&(sv.current=!0,rL.B))if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>sy.current=t.matches;t.addListener(e),e()}else sy.current=!1;this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||sy.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),m(this.notifyUpdate),m(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let n=x.has(t);n&&this.onBindTransform&&this.onBindTransform();let r=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&f.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),s=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{r(),s(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in rF){let e=rF[t];if(!e)continue;let{isEnabled:i,Feature:n}=e;if(!this.features[t]&&n&&i(this.props)&&(this.features[t]=new n(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):iC()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<sw.length;e++){let i=sw[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let n=t["on"+i];n&&(this.propEventSubscriptions[i]=this.on(i,n))}this.prevMotionValues=function(t,e,i){for(let n in e){let r=e[n],s=i[n];if(C(r))t.addValue(n,r);else if(C(s))t.addValue(n,V(r,{owner:t}));else if(s!==r)if(t.hasValue(n)){let e=t.getValue(n);!0===e.liveStyle?e.jump(r):e.hasAnimated||e.set(r)}else{let e=t.getStaticValue(n);t.addValue(n,V(void 0!==e?e:r,{owner:t}))}}for(let n in i)void 0===e[n]&&t.removeValue(n);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=V(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&(sn(i)||ss(i))?i=parseFloat(i):!sg(i)&&tS.test(e)&&(i=sd(t,e)),this.setBaseTarget(t,C(i)?i.get():i)),C(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let n=o(this.props,i,this.presenceContext?.custom);n&&(e=n[t])}if(i&&void 0!==e)return e;let n=this.getBaseTargetFromProps(this.props,t);return void 0===n||C(n)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new S),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class sT extends sb{constructor(){super(...arguments),this.KeyframeResolver=sf}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;C(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function sS(t,{style:e,vars:i},n,r){for(let s in Object.assign(t.style,e,r&&r.getProjectionStyles(n)),i)t.style.setProperty(s,i[s])}class sP extends sT{constructor(){super(...arguments),this.type="html",this.renderInstance=sS}readValueFromInstance(t,e){if(x.has(e))return this.projection?.isProjecting?eb(e):eS(t,e);{let i=window.getComputedStyle(t),n=(H(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(t,{transformPagePoint:e}){return iz(t,e)}build(t,e,i){rY(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return r6(t,e,i)}}let sA=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class sE extends sT{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=iC}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(x.has(e)){let t=sc(e);return t&&t.default||0}return e=sA.has(e)?e:L(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return r8(t,e,i)}build(t,e,i){r_(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,n){for(let i in sS(t,e,void 0,n),e.attrs)t.setAttribute(sA.has(i)?i:L(i),e.attrs[i])}mount(t){this.isSVGTag=rQ(t.tagName),super.mount(t)}}let sM=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,n)=>"create"===n?t:(e.has(n)||e.set(n,t(n)),e.get(n))})}((eK={animation:{Feature:ic},exit:{Feature:ip},inView:{Feature:rE},tap:{Feature:rw},focus:{Feature:rc},hover:{Feature:rh},pan:{Feature:i9},drag:{Feature:i4,ProjectionNode:ro,MeasureLayout:nu},layout:{ProjectionNode:ro,MeasureLayout:nu}},eG=(t,e)=>r3(t)?new sE(e):new sP(e,{allowProjection:t!==nt.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function(t){var e,i;let{preloadedFeatures:n,createVisualElement:r,useRender:s,useVisualState:o,Component:a}=t;function l(t,e){var i,n,l;let u,h={...(0,nt.useContext)(rD.Q),...t,layoutId:function(t){let{layoutId:e}=t,i=(0,nt.useContext)(ni.L).id;return i&&void 0!==e?i+"-"+e:e}(t)},{isStatic:c}=h,d=function(t){let{initial:e,animate:i}=function(t,e){if(rk(t)){let{initial:e,animate:i}=t;return{initial:!1===e||ie(e)?e:void 0,animate:ie(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,nt.useContext)(rV));return(0,nt.useMemo)(()=>({initial:e,animate:i}),[rR(e),rR(i)])}(t),p=o(t,c);if(!c&&rL.B){n=0,l=0,(0,nt.useContext)(rM).strict;let t=function(t){let{drag:e,layout:i}=rF;if(!e&&!i)return{};let n={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(h);u=t.MeasureLayout,d.visualElement=function(t,e,i,n,r){let{visualElement:s}=(0,nt.useContext)(rV),o=(0,nt.useContext)(rM),a=(0,nt.useContext)(rB.t),l=(0,nt.useContext)(rD.Q).reducedMotion,u=(0,nt.useRef)(null);n=n||o.renderer,!u.current&&n&&(u.current=n(t,{visualState:e,parent:s,props:i,presenceContext:a,blockInitialAnimation:!!a&&!1===a.initial,reducedMotionConfig:l}));let h=u.current,c=(0,nt.useContext)(nn);h&&!h.projection&&r&&("html"===h.type||"svg"===h.type)&&function(t,e,i,n){let{layoutId:r,layout:s,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:h}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:r,layout:s,alwaysMeasureLayout:!!o||a&&iY(a),visualElement:t,animationType:"string"==typeof s?s:"both",initialPromotionConfig:n,crossfade:h,layoutScroll:l,layoutRoot:u})}(u.current,i,r,c);let d=(0,nt.useRef)(!1);(0,nt.useInsertionEffect)(()=>{h&&d.current&&h.update(i,a)});let p=i[j],f=(0,nt.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return(0,rI.E)(()=>{h&&(d.current=!0,window.MotionIsMounted=!0,h.updateFeatures(),i7.render(h.render),f.current&&h.animationState&&h.animationState.animateChanges())}),(0,nt.useEffect)(()=>{h&&(!f.current&&h.animationState&&h.animationState.animateChanges(),f.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(p)}),f.current=!1))}),h}(a,p,h,r,t.ProjectionNode)}return(0,i8.jsxs)(rV.Provider,{value:d,children:[u&&d.visualElement?(0,i8.jsx)(u,{visualElement:d.visualElement,...h}):null,s(a,t,(i=d.visualElement,(0,nt.useCallback)(t=>{t&&p.onMount&&p.onMount(t),i&&(t?i.mount(t):i.unmount()),e&&("function"==typeof e?e(t):iY(e)&&(e.current=t))},[i])),p,c,d.visualElement)]})}n&&function(t){for(let e in t)rF[e]={...rF[e],...t[e]}}(n),l.displayName="motion.".concat("string"==typeof a?a:"create(".concat(null!==(i=null!==(e=a.displayName)&&void 0!==e?e:a.name)&&void 0!==i?i:"",")"));let u=(0,nt.forwardRef)(l);return u[rO]=a,u}({...r3(t)?r7:r9,preloadedFeatures:eK,useRender:function(t=!1){return(e,i,n,{latestValues:r},s)=>{let o=(r3(e)?function(t,e,i,n){let r=(0,nt.useMemo)(()=>{let i=rZ();return r_(i,e,rQ(n),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};rq(e,t.style,t),r.style={...e,...r.style}}return r}:function(t,e){let i={},n=function(t,e){let i=t.style||{},n={};return rq(n,i,t),Object.assign(n,function({transformTemplate:t},e){return(0,nt.useMemo)(()=>{let i=rX();return rY(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),n}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=n,i})(i,r,s,e),a=function(t,e,i){let n={};for(let r in t)("values"!==r||"object"!=typeof t.values)&&(r1(r)||!0===i&&r0(r)||!e&&!r0(r)||t.draggable&&r.startsWith("onDrag"))&&(n[r]=t[r]);return n}(i,"string"==typeof e,t),l=e!==nt.Fragment?{...a,...o,ref:n}:{},{children:u}=i,h=(0,nt.useMemo)(()=>C(u)?u.get():u,[u]);return(0,nt.createElement)(e,{...l,children:h})}}(e),createVisualElement:eG,Component:t})}))},73093:(t,e,i)=>{i.d(e,{A:()=>r});let n={active:!0,breakpoints:{},delay:4e3,jump:!1,playOnInit:!0,stopOnFocusIn:!0,stopOnInteraction:!0,stopOnMouseEnter:!1,stopOnLastSnap:!1,rootNode:null};function r(t={}){let e,i,s,o,a=null,l=0,u=!1,h=!1,c=!1,d=!1;function p(){if(!s){if(g()){c=!0;return}u||i.emit("autoplay:play");let{ownerWindow:t}=i.internalEngine();t.clearTimeout(l),l=t.setTimeout(b,o[i.selectedScrollSnap()]),a=new Date().getTime(),i.emit("autoplay:timerset"),u=!0}}function f(){if(!s){u&&i.emit("autoplay:stop");let{ownerWindow:t}=i.internalEngine();t.clearTimeout(l),l=0,a=null,i.emit("autoplay:timerstopped"),u=!1}}function m(){if(g())return c=u,f();c&&p()}function g(){let{ownerDocument:t}=i.internalEngine();return"hidden"===t.visibilityState}function y(){h||f()}function v(){h||p()}function x(){h=!0,f()}function w(){h=!1,p()}function b(){let{index:t}=i.internalEngine(),n=t.clone().add(1).get(),r=i.scrollSnapList().length-1,s=e.stopOnLastSnap&&n===r;if(i.canScrollNext()?i.scrollNext(d):i.scrollTo(0,d),i.emit("autoplay:select"),s)return f();p()}return{name:"autoplay",options:t,init:function(a,l){i=a;let{mergeOptions:u,optionsAtMedia:h}=l,c=u(n,r.globalOptions);if(e=h(u(c,t)),i.scrollSnapList().length<=1)return;d=e.jump,s=!1,o=function(t,e){let i=t.scrollSnapList();return"number"==typeof e?i.map(()=>e):e(i,t)}(i,e.delay);let{eventStore:g,ownerDocument:b}=i.internalEngine(),T=!!i.internalEngine().options.watchDrag,S=function(t,e){let i=t.rootNode();return e&&e(i)||i}(i,e.rootNode);g.add(b,"visibilitychange",m),T&&i.on("pointerDown",y),T&&!e.stopOnInteraction&&i.on("pointerUp",v),e.stopOnMouseEnter&&g.add(S,"mouseenter",x),e.stopOnMouseEnter&&!e.stopOnInteraction&&g.add(S,"mouseleave",w),e.stopOnFocusIn&&i.on("slideFocusStart",f),e.stopOnFocusIn&&!e.stopOnInteraction&&g.add(i.containerNode(),"focusout",p),e.playOnInit&&p()},destroy:function(){i.off("pointerDown",y).off("pointerUp",v).off("slideFocusStart",f),f(),s=!0,u=!1},play:function(t){void 0!==t&&(d=t),p()},stop:function(){u&&f()},reset:function(){u&&p()},isPlaying:function(){return u},timeUntilNext:function(){return a?o[i.selectedScrollSnap()]-(new Date().getTime()-a):null}}}r.globalOptions=void 0},75173:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(10436).A)("users-round",[["path",{d:"M18 21a8 8 0 0 0-16 0",key:"3ypg7q"}],["circle",{cx:"10",cy:"8",r:"5",key:"o932ke"}],["path",{d:"M22 20c0-3.37-2-6.5-4-8a5 5 0 0 0-.45-8.3",key:"10s06x"}]])},75227:(t,e,i)=>{i.d(e,{A:()=>A});var n=i(34545);function r(t){return"[object Object]"===Object.prototype.toString.call(t)||Array.isArray(t)}function s(t,e){let i=Object.keys(t),n=Object.keys(e);return i.length===n.length&&JSON.stringify(Object.keys(t.breakpoints||{}))===JSON.stringify(Object.keys(e.breakpoints||{}))&&i.every(i=>{let n=t[i],o=e[i];return"function"==typeof n?`${n}`==`${o}`:r(n)&&r(o)?s(n,o):n===o})}function o(t){return t.concat().sort((t,e)=>t.name>e.name?1:-1).map(t=>t.options)}function a(t){return"number"==typeof t}function l(t){return"string"==typeof t}function u(t){return"boolean"==typeof t}function h(t){return"[object Object]"===Object.prototype.toString.call(t)}function c(t){return Math.abs(t)}function d(t){return Math.sign(t)}function p(t){return y(t).map(Number)}function f(t){return t[m(t)]}function m(t){return Math.max(0,t.length-1)}function g(t,e=0){return Array.from(Array(t),(t,i)=>e+i)}function y(t){return Object.keys(t)}function v(t,e){return void 0!==e.MouseEvent&&t instanceof e.MouseEvent}function x(){let t=[],e={add:function(i,n,r,s={passive:!0}){let o;return"addEventListener"in i?(i.addEventListener(n,r,s),o=()=>i.removeEventListener(n,r,s)):(i.addListener(r),o=()=>i.removeListener(r)),t.push(o),e},clear:function(){t=t.filter(t=>t())}};return e}function w(t=0,e=0){let i=c(t-e);function n(i){return i<t||i>e}return{length:i,max:e,min:t,constrain:function(i){return n(i)?i<t?t:e:i},reachedAny:n,reachedMax:function(t){return t>e},reachedMin:function(e){return e<t},removeOffset:function(t){return i?t-i*Math.ceil((t-e)/i):t}}}function b(t){let e=t;function i(t){return a(t)?t:t.get()}return{get:function(){return e},set:function(t){e=i(t)},add:function(t){e+=i(t)},subtract:function(t){e-=i(t)}}}function T(t,e){let i="x"===t.scroll?function(t){return`translate3d(${t}px,0px,0px)`}:function(t){return`translate3d(0px,${t}px,0px)`},n=e.style,r=null,s=!1;return{clear:function(){s||(n.transform="",e.getAttribute("style")||e.removeAttribute("style"))},to:function(e){if(s)return;let o=Math.round(100*t.direction(e))/100;o!==r&&(n.transform=i(o),r=o)},toggleActive:function(t){s=!t}}}let S={align:"center",axis:"x",container:null,slides:null,containScroll:"trimSnaps",direction:"ltr",slidesToScroll:1,inViewThreshold:0,breakpoints:{},dragFree:!1,dragThreshold:10,loop:!1,skipSnaps:!1,duration:25,startIndex:0,active:!0,watchDrag:!0,watchResize:!0,watchSlides:!0,watchFocus:!0};function P(t,e,i){let n,r,s,o,A,E=t.ownerDocument,M=E.defaultView,D=function(t){function e(t,e){return function t(e,i){return[e,i].reduce((e,i)=>(y(i).forEach(n=>{let r=e[n],s=i[n],o=h(r)&&h(s);e[n]=o?t(r,s):s}),e),{})}(t,e||{})}return{mergeOptions:e,optionsAtMedia:function(i){let n=i.breakpoints||{},r=y(n).filter(e=>t.matchMedia(e).matches).map(t=>n[t]).reduce((t,i)=>e(t,i),{});return e(i,r)},optionsMediaQueries:function(e){return e.map(t=>y(t.breakpoints||{})).reduce((t,e)=>t.concat(e),[]).map(t.matchMedia)}}}(M),V=(A=[],{init:function(t,e){return(A=e.filter(({options:t})=>!1!==D.optionsAtMedia(t).active)).forEach(e=>e.init(t,D)),e.reduce((t,e)=>Object.assign(t,{[e.name]:e}),{})},destroy:function(){A=A.filter(t=>t.destroy())}}),k=x(),C=function(){let t,e={},i={init:function(e){t=e},emit:function(n){return(e[n]||[]).forEach(e=>e(t,n)),i},off:function(t,n){return e[t]=(e[t]||[]).filter(t=>t!==n),i},on:function(t,n){return e[t]=(e[t]||[]).concat([n]),i},clear:function(){e={}}};return i}(),{mergeOptions:R,optionsAtMedia:L,optionsMediaQueries:j}=D,{on:F,off:O,emit:B}=C,I=!1,U=R(S,P.globalOptions),N=R(U),$=[];function W(e,i){if(I)return;N=L(U=R(U,e)),$=i||$;let{container:h,slides:S}=N;s=(l(h)?t.querySelector(h):h)||t.children[0];let P=l(S)?s.querySelectorAll(S):S;o=[].slice.call(P||s.children),n=function e(i){let n=function(t,e,i,n,r,s,o){let h,S,{align:P,axis:A,direction:E,startIndex:M,loop:D,duration:V,dragFree:k,dragThreshold:C,inViewThreshold:R,slidesToScroll:L,skipSnaps:j,containScroll:F,watchResize:O,watchSlides:B,watchDrag:I,watchFocus:U}=s,N={measure:function(t){let{offsetTop:e,offsetLeft:i,offsetWidth:n,offsetHeight:r}=t;return{top:e,right:i+n,bottom:e+r,left:i,width:n,height:r}}},$=N.measure(e),W=i.map(N.measure),z=function(t,e){let i="rtl"===e,n="y"===t,r=!n&&i?-1:1;return{scroll:n?"y":"x",cross:n?"x":"y",startEdge:n?"top":i?"right":"left",endEdge:n?"bottom":i?"left":"right",measureSize:function(t){let{height:e,width:i}=t;return n?e:i},direction:function(t){return t*r}}}(A,E),H=z.measureSize($),Y={measure:function(t){return t/100*H}},X=function(t,e){let i={start:function(){return 0},center:function(t){return(e-t)/2},end:function(t){return e-t}};return{measure:function(n,r){return l(t)?i[t](n):t(e,n,r)}}}(P,H),q=!D&&!!F,{slideSizes:K,slideSizesWithGaps:G,startGap:_,endGap:Z}=function(t,e,i,n,r,s){let{measureSize:o,startEdge:a,endEdge:l}=t,u=i[0]&&r,h=function(){if(!u)return 0;let t=i[0];return c(e[a]-t[a])}(),d=u?parseFloat(s.getComputedStyle(f(n)).getPropertyValue(`margin-${l}`)):0,p=i.map(o),g=i.map((t,e,i)=>{let n=e===m(i);return e?n?p[e]+d:i[e+1][a]-t[a]:p[e]+h}).map(c);return{slideSizes:p,slideSizesWithGaps:g,startGap:h,endGap:d}}(z,$,W,i,D||!!F,r),Q=function(t,e,i,n,r,s,o,l,u){let{startEdge:h,endEdge:d,direction:g}=t,y=a(i);return{groupSlides:function(t){return y?p(t).filter(t=>t%i==0).map(e=>t.slice(e,e+i)):t.length?p(t).reduce((i,a,u)=>{let p=f(i)||0,y=a===m(t),v=r[h]-s[p][h],x=r[h]-s[a][d],w=n||0!==p?0:g(o),b=c(x-(!n&&y?g(l):0)-(v+w));return u&&b>e+2&&i.push(a),y&&i.push(t.length),i},[]).map((e,i,n)=>{let r=Math.max(n[i-1]||0);return t.slice(r,e)}):[]}}}(z,H,L,D,$,W,_,Z,0),{snaps:J,snapsAligned:tt}=function(t,e,i,n,r){let{startEdge:s,endEdge:o}=t,{groupSlides:a}=r,l=a(n).map(t=>f(t)[o]-t[0][s]).map(c).map(e.measure),u=n.map(t=>i[s]-t[s]).map(t=>-c(t)),h=a(u).map(t=>t[0]).map((t,e)=>t+l[e]);return{snaps:u,snapsAligned:h}}(z,X,$,W,Q),te=-f(J)+f(G),{snapsContained:ti,scrollContainLimit:tn}=function(t,e,i,n,r){let s=w(-e+t,0),o=i.map((t,e)=>{let{min:n,max:r}=s,o=s.constrain(t),a=e===m(i);return e?a||function(t,e){return 1>=c(t-e)}(n,o)?n:function(t,e){return 1>=c(t-e)}(r,o)?r:o:r}).map(t=>parseFloat(t.toFixed(3))),a=function(){let t=o[0],e=f(o);return w(o.lastIndexOf(t),o.indexOf(e)+1)}();function l(t,e){return 1>=c(t-e)}return{snapsContained:function(){if(e<=t+2)return[s.max];if("keepSnaps"===n)return o;let{min:i,max:r}=a;return o.slice(i,r)}(),scrollContainLimit:a}}(H,te,tt,F,0),tr=q?ti:tt,{limit:ts}=function(t,e,i){let n=e[0];return{limit:w(i?n-t:f(e),n)}}(te,tr,D),to=function t(e,i,n){let{constrain:r}=w(0,e),s=e+1,o=a(i);function a(t){return n?c((s+t)%s):r(t)}function l(){return t(e,o,n)}let u={get:function(){return o},set:function(t){return o=a(t),u},add:function(t){return l().set(o+t)},clone:l};return u}(m(tr),M,D),ta=to.clone(),tl=p(i),tu=({dragHandler:t,scrollBody:e,scrollBounds:i,options:{loop:n}})=>{n||i.constrain(t.pointerDown()),e.seek()},th=({scrollBody:t,translate:e,location:i,offsetLocation:n,previousLocation:r,scrollLooper:s,slideLooper:o,dragHandler:a,animation:l,eventHandler:u,scrollBounds:h,options:{loop:c}},d)=>{let p=t.settled(),f=!h.shouldConstrain(),m=c?p:p&&f,g=m&&!a.pointerDown();g&&l.stop();let y=i.get()*d+r.get()*(1-d);n.set(y),c&&(s.loop(t.direction()),o.loop()),e.to(n.get()),g&&u.emit("settle"),m||u.emit("scroll")},tc=function(t,e,i,n){let r=x(),s=1e3/60,o=null,a=0,l=0;function u(t){if(!l)return;o||(o=t,i(),i());let r=t-o;for(o=t,a+=r;a>=s;)i(),a-=s;n(a/s),l&&(l=e.requestAnimationFrame(u))}function h(){e.cancelAnimationFrame(l),o=null,a=0,l=0}return{init:function(){r.add(t,"visibilitychange",()=>{t.hidden&&(o=null,a=0)})},destroy:function(){h(),r.clear()},start:function(){l||(l=e.requestAnimationFrame(u))},stop:h,update:i,render:n}}(n,r,()=>tu(tA),t=>th(tA,t)),td=tr[to.get()],tp=b(td),tf=b(td),tm=b(td),tg=b(td),ty=function(t,e,i,n,r,s){let o=0,a=0,l=r,u=.68,h=t.get(),p=0;function f(t){return l=t,g}function m(t){return u=t,g}let g={direction:function(){return a},duration:function(){return l},velocity:function(){return o},seek:function(){let e=n.get()-t.get(),r=0;return l?(i.set(t),o+=e/l,o*=u,h+=o,t.add(o),r=h-p):(o=0,i.set(n),t.set(n),r=e),a=d(r),p=h,g},settled:function(){return .001>c(n.get()-e.get())},useBaseFriction:function(){return m(.68)},useBaseDuration:function(){return f(r)},useFriction:m,useDuration:f};return g}(tp,tm,tf,tg,V,.68),tv=function(t,e,i,n,r){let{reachedAny:s,removeOffset:o,constrain:a}=n;function l(t){return t.concat().sort((t,e)=>c(t)-c(e))[0]}function u(e,n){let r=[e,e+i,e-i];if(!t)return e;if(!n)return l(r);let s=r.filter(t=>d(t)===n);return s.length?l(s):f(r)-i}return{byDistance:function(i,n){let l=r.get()+i,{index:h,distance:d}=function(i){let n=t?o(i):a(i),{index:r}=e.map((t,e)=>({diff:u(t-n,0),index:e})).sort((t,e)=>c(t.diff)-c(e.diff))[0];return{index:r,distance:n}}(l),p=!t&&s(l);if(!n||p)return{index:h,distance:i};let f=i+u(e[h]-d,0);return{index:h,distance:f}},byIndex:function(t,i){let n=u(e[t]-r.get(),i);return{index:t,distance:n}},shortcut:u}}(D,tr,te,ts,tg),tx=function(t,e,i,n,r,s,o){function a(r){let a=r.distance,l=r.index!==e.get();s.add(a),a&&(n.duration()?t.start():(t.update(),t.render(1),t.update())),l&&(i.set(e.get()),e.set(r.index),o.emit("select"))}return{distance:function(t,e){a(r.byDistance(t,e))},index:function(t,i){let n=e.clone().set(t);a(r.byIndex(n.get(),i))}}}(tc,to,ta,ty,tv,tg,o),tw=function(t){let{max:e,length:i}=t;return{get:function(t){return i?-((t-e)/i):0}}}(ts),tb=x(),tT=function(t,e,i,n){let r,s={},o=null,a=null,l=!1;return{init:function(){r=new IntersectionObserver(t=>{l||(t.forEach(t=>{s[e.indexOf(t.target)]=t}),o=null,a=null,i.emit("slidesInView"))},{root:t.parentElement,threshold:n}),e.forEach(t=>r.observe(t))},destroy:function(){r&&r.disconnect(),l=!0},get:function(t=!0){if(t&&o)return o;if(!t&&a)return a;let e=y(s).reduce((e,i)=>{let n=parseInt(i),{isIntersecting:r}=s[n];return(t&&r||!t&&!r)&&e.push(n),e},[]);return t&&(o=e),t||(a=e),e}}}(e,i,o,R),{slideRegistry:tS}=function(t,e,i,n,r,s){let{groupSlides:o}=r,{min:a,max:l}=n;return{slideRegistry:function(){let n=o(s);return 1===i.length?[s]:t&&"keepSnaps"!==e?n.slice(a,l).map((t,e,i)=>{let n=e===m(i);return e?n?g(m(s)-f(i)[0]+1,f(i)[0]):t:g(f(i[0])+1)}):n}()}}(q,F,tr,tn,Q,tl),tP=function(t,e,i,n,r,s,o,l){let h={passive:!0,capture:!0},c=0;function d(t){"Tab"===t.code&&(c=new Date().getTime())}return{init:function(p){l&&(s.add(document,"keydown",d,!1),e.forEach((e,d)=>{s.add(e,"focus",e=>{(u(l)||l(p,e))&&function(e){if(new Date().getTime()-c>10)return;o.emit("slideFocusStart"),t.scrollLeft=0;let s=i.findIndex(t=>t.includes(e));a(s)&&(r.useDuration(0),n.index(s,0),o.emit("slideFocus"))}(d)},h)}))}}}(t,i,tS,tx,ty,tb,o,U),tA={ownerDocument:n,ownerWindow:r,eventHandler:o,containerRect:$,slideRects:W,animation:tc,axis:z,dragHandler:function(t,e,i,n,r,s,o,a,l,h,p,f,m,g,y,b,T,S,P){let{cross:A,direction:E}=t,M=["INPUT","SELECT","TEXTAREA"],D={passive:!1},V=x(),k=x(),C=w(50,225).constrain(g.measure(20)),R={mouse:300,touch:400},L={mouse:500,touch:600},j=y?43:25,F=!1,O=0,B=0,I=!1,U=!1,N=!1,$=!1;function W(t){if(!v(t,n)&&t.touches.length>=2)return z(t);let e=s.readPoint(t),i=s.readPoint(t,A),o=c(e-O),l=c(i-B);if(!U&&!$&&(!t.cancelable||!(U=o>l)))return z(t);let u=s.pointerMove(t);o>b&&(N=!0),h.useFriction(.3).useDuration(.75),a.start(),r.add(E(u)),t.preventDefault()}function z(t){let e=p.byDistance(0,!1).index!==f.get(),i=s.pointerUp(t)*(y?L:R)[$?"mouse":"touch"],n=function(t,e){let i=f.add(-+d(t)),n=p.byDistance(t,!y).distance;return y||c(t)<C?n:T&&e?.5*n:p.byIndex(i.get(),0).distance}(E(i),e),r=function(t,e){var i,n;if(0===t||0===e||c(t)<=c(e))return 0;let r=(i=c(t),n=c(e),c(i-n));return c(r/t)}(i,n);U=!1,I=!1,k.clear(),h.useDuration(j-10*r).useFriction(.68+r/50),l.distance(n,!y),$=!1,m.emit("pointerUp")}function H(t){N&&(t.stopPropagation(),t.preventDefault(),N=!1)}return{init:function(t){P&&V.add(e,"dragstart",t=>t.preventDefault(),D).add(e,"touchmove",()=>void 0,D).add(e,"touchend",()=>void 0).add(e,"touchstart",a).add(e,"mousedown",a).add(e,"touchcancel",z).add(e,"contextmenu",z).add(e,"click",H,!0);function a(a){(u(P)||P(t,a))&&function(t){let a=v(t,n);if($=a,N=y&&a&&!t.buttons&&F,F=c(r.get()-o.get())>=2,(!a||0===t.button)&&!function(t){let e=t.nodeName||"";return M.includes(e)}(t.target)){I=!0,s.pointerDown(t),h.useFriction(0).useDuration(0),r.set(o);let n=$?i:e;k.add(n,"touchmove",W,D).add(n,"touchend",z).add(n,"mousemove",W,D).add(n,"mouseup",z),O=s.readPoint(t),B=s.readPoint(t,A),m.emit("pointerDown")}}(a)}},destroy:function(){V.clear(),k.clear()},pointerDown:function(){return I}}}(z,t,n,r,tg,function(t,e){let i,n;function r(t){return t.timeStamp}function s(i,n){let r=n||t.scroll,s=`client${"x"===r?"X":"Y"}`;return(v(i,e)?i:i.touches[0])[s]}return{pointerDown:function(t){return i=t,n=t,s(t)},pointerMove:function(t){let e=s(t)-s(n),o=r(t)-r(i)>170;return n=t,o&&(i=t),e},pointerUp:function(t){if(!i||!n)return 0;let e=s(n)-s(i),o=r(t)-r(i),a=r(t)-r(n)>170,l=e/o;return o&&!a&&c(l)>.1?l:0},readPoint:s}}(z,r),tp,tc,tx,ty,tv,to,o,Y,k,C,j,0,I),eventStore:tb,percentOfView:Y,index:to,indexPrevious:ta,limit:ts,location:tp,offsetLocation:tm,previousLocation:tf,options:s,resizeHandler:function(t,e,i,n,r,s,o){let a,l,h=[t].concat(n),d=[],p=!1;function f(t){return r.measureSize(o.measure(t))}return{init:function(r){s&&(l=f(t),d=n.map(f),a=new ResizeObserver(i=>{(u(s)||s(r,i))&&function(i){for(let s of i){if(p)return;let i=s.target===t,o=n.indexOf(s.target),a=i?l:d[o];if(c(f(i?t:n[o])-a)>=.5){r.reInit(),e.emit("resize");break}}}(i)}),i.requestAnimationFrame(()=>{h.forEach(t=>a.observe(t))}))},destroy:function(){p=!0,a&&a.disconnect()}}}(e,o,r,i,z,O,N),scrollBody:ty,scrollBounds:function(t,e,i,n,r){let s=r.measure(10),o=r.measure(50),a=w(.1,.99),l=!1;function u(){return!!(!l&&t.reachedAny(i.get())&&t.reachedAny(e.get()))}return{shouldConstrain:u,constrain:function(r){if(!u())return;let l=t.reachedMin(e.get())?"min":"max",h=c(t[l]-e.get()),d=i.get()-e.get(),p=a.constrain(h/o);i.subtract(d*p),!r&&c(d)<s&&(i.set(t.constrain(i.get())),n.useDuration(25).useBaseFriction())},toggleActive:function(t){l=!t}}}(ts,tm,tg,ty,Y),scrollLooper:function(t,e,i,n){let{reachedMin:r,reachedMax:s}=w(e.min+.1,e.max+.1);return{loop:function(e){if(!(1===e?s(i.get()):-1===e&&r(i.get())))return;let o=-(t*+e);n.forEach(t=>t.add(o))}}}(te,ts,tm,[tp,tm,tf,tg]),scrollProgress:tw,scrollSnapList:tr.map(tw.get),scrollSnaps:tr,scrollTarget:tv,scrollTo:tx,slideLooper:function(t,e,i,n,r,s,o,a,l){let u=p(r),h=p(r).reverse(),c=m(f(h,o[0]),i,!1).concat(m(f(u,e-o[0]-1),-i,!0));function d(t,e){return t.reduce((t,e)=>t-r[e],e)}function f(t,e){return t.reduce((t,i)=>d(t,e)>0?t.concat([i]):t,[])}function m(r,o,u){let h=s.map((t,i)=>({start:t-n[i]+.5+o,end:t+e-.5+o}));return r.map(e=>{let n=u?0:-i,r=u?i:0,s=h[e][u?"end":"start"];return{index:e,loopPoint:s,slideLocation:b(-1),translate:T(t,l[e]),target:()=>a.get()>s?n:r}})}return{canLoop:function(){return c.every(({index:t})=>.1>=d(u.filter(e=>e!==t),e))},clear:function(){c.forEach(t=>t.translate.clear())},loop:function(){c.forEach(t=>{let{target:e,translate:i,slideLocation:n}=t,r=e();r!==n.get()&&(i.to(r),n.set(r))})},loopPoints:c}}(z,H,te,K,G,J,tr,tm,i),slideFocus:tP,slidesHandler:(S=!1,{init:function(t){B&&(h=new MutationObserver(e=>{!S&&(u(B)||B(t,e))&&function(e){for(let i of e)if("childList"===i.type){t.reInit(),o.emit("slidesChanged");break}}(e)})).observe(e,{childList:!0})},destroy:function(){h&&h.disconnect(),S=!0}}),slidesInView:tT,slideIndexes:tl,slideRegistry:tS,slidesToScroll:Q,target:tg,translate:T(z,e)};return tA}(t,s,o,E,M,i,C);return i.loop&&!n.slideLooper.canLoop()?e(Object.assign({},i,{loop:!1})):n}(N),j([U,...$.map(({options:t})=>t)]).forEach(t=>k.add(t,"change",z)),N.active&&(n.translate.to(n.location.get()),n.animation.init(),n.slidesInView.init(),n.slideFocus.init(q),n.eventHandler.init(q),n.resizeHandler.init(q),n.slidesHandler.init(q),n.options.loop&&n.slideLooper.loop(),s.offsetParent&&o.length&&n.dragHandler.init(q),r=V.init(q,$))}function z(t,e){let i=X();H(),W(R({startIndex:i},t),e),C.emit("reInit")}function H(){n.dragHandler.destroy(),n.eventStore.clear(),n.translate.clear(),n.slideLooper.clear(),n.resizeHandler.destroy(),n.slidesHandler.destroy(),n.slidesInView.destroy(),n.animation.destroy(),V.destroy(),k.clear()}function Y(t,e,i){N.active&&!I&&(n.scrollBody.useBaseFriction().useDuration(!0===e?0:N.duration),n.scrollTo.index(t,i||0))}function X(){return n.index.get()}let q={canScrollNext:function(){return n.index.add(1).get()!==X()},canScrollPrev:function(){return n.index.add(-1).get()!==X()},containerNode:function(){return s},internalEngine:function(){return n},destroy:function(){I||(I=!0,k.clear(),H(),C.emit("destroy"),C.clear())},off:O,on:F,emit:B,plugins:function(){return r},previousScrollSnap:function(){return n.indexPrevious.get()},reInit:z,rootNode:function(){return t},scrollNext:function(t){Y(n.index.add(1).get(),t,-1)},scrollPrev:function(t){Y(n.index.add(-1).get(),t,1)},scrollProgress:function(){return n.scrollProgress.get(n.offsetLocation.get())},scrollSnapList:function(){return n.scrollSnapList},scrollTo:Y,selectedScrollSnap:X,slideNodes:function(){return o},slidesInView:function(){return n.slidesInView.get()},slidesNotInView:function(){return n.slidesInView.get(!1)}};return W(e,i),setTimeout(()=>C.emit("init"),0),q}function A(t={},e=[]){let i=(0,n.useRef)(t),r=(0,n.useRef)(e),[a,l]=(0,n.useState)(),[u,h]=(0,n.useState)(),c=(0,n.useCallback)(()=>{a&&a.reInit(i.current,r.current)},[a]);return(0,n.useEffect)(()=>{s(i.current,t)||(i.current=t,c())},[t,c]),(0,n.useEffect)(()=>{!function(t,e){if(t.length!==e.length)return!1;let i=o(t),n=o(e);return i.every((t,e)=>s(t,n[e]))}(r.current,e)&&(r.current=e,c())},[e,c]),(0,n.useEffect)(()=>{if("undefined"!=typeof window&&window.document&&window.document.createElement&&u){P.globalOptions=A.globalOptions;let t=P(u,i.current,r.current);return l(t),()=>t.destroy()}l(void 0)},[u,l]),[h,a]}P.globalOptions=void 0,A.globalOptions=void 0},76855:(t,e,i)=>{i.d(e,{E:()=>r});var n=i(34545);let r=i(83563).B?n.useLayoutEffect:n.useEffect},83563:(t,e,i)=>{i.d(e,{B:()=>n});let n="undefined"!=typeof window},83932:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(10436).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},90221:(t,e,i)=>{i.d(e,{Q:()=>n});let n=(0,i(34545).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})}}]);