"use strict";exports.id=328,exports.ids=[328],exports.modules={15361:(e,t,r)=>{r.d(t,{Lt:()=>s.AlertDialog,Rx:()=>s.AlertDialogAction,Zr:()=>s.<PERSON><PERSON>og<PERSON>ancel,EO:()=>s.AlertDialogContent,$v:()=>s.AlertDialogDescription,ck:()=>s.AlertDialogFooter,wd:()=>s.AlertDialogHeader,r7:()=>s.AlertDialogTitle,$n:()=>u.$,uB:()=>f.Command,xL:()=>f.CommandEmpty,L$:()=>f.CommandGroup,G7:()=>f.CommandInput,h_:()=>f.CommandItem,oI:()=>f.CommandList,lG:()=>g.<PERSON>,Cf:()=>g.<PERSON>,Es:()=>g.<PERSON>,L3:()=>g.<PERSON>,rI:()=>p.DropdownMenu,SQ:()=>p.DropdownMenuContent,_2:()=>p.DropdownMenuItem,ty:()=>p.DropdownMenuTrigger,MJ:()=>m.FormControl,zB:()=>m.FormField,eI:()=>m.FormItem,lR:()=>m.FormLabel,C5:()=>m.FormMessage,KF:()=>A,AM:()=>y.Popover,hl:()=>y.PopoverContent,Wv:()=>y.PopoverTrigger,FK:()=>M.ScrollArea,Ap:()=>I.Slider,XI:()=>N.Table,BF:()=>N.TableBody,nA:()=>N.TableCell,nd:()=>N.TableHead,A0:()=>N.TableHeader,Hj:()=>N.TableRow,tU:()=>j.Tabs,j7:()=>j.TabsList,Xi:()=>j.TabsTrigger,TM:()=>C});var s=r(20164),a=r(3641),n=r(44508),i=r(36118),o=r(28377),l=r(10244);let d=(0,o.F)("inline-flex items-center justify-center rounded-sm border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/70",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c({className:e,variant:t,asChild:r=!1,...s}){let n=r?i.Slot:"span";return(0,a.jsx)(n,{"data-slot":"badge",className:(0,l.cn)(d({variant:t}),e),...s})}var u=r(99115),f=r(46016),g=r(49431);r(62604);var p=r(66310),m=r(61805);r(76021);var v=r(38131),h=r.n(v),k=r(20574),w=r(1821),x=r(24378),y=r(35940);let b=(0,o.F)("px-2 py-1",{variants:{variant:{default:"border-foreground/10 text-foreground bg-card hover:bg-card/80",secondary:"border-foreground/10 bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",inverted:"bg-background"}},defaultVariants:{variant:"default"}}),A=n.forwardRef(({options:e,onValueChange:t,variant:r,defaultValue:s=[],placeholder:i="Select options",maxCount:o=3,modalPopover:d=!1,className:u,...g},p)=>{let[m,v]=n.useState(s),[A,M]=n.useState(!1),I=e=>{let r=m.includes(e)?m.filter(t=>t!==e):[...m,e];v(r),t(r)},N=()=>{let e=m.slice(0,o);v(e),t(e)},j=n.useRef(new Map),C=n.useRef(""),D=n.useCallback((t,r)=>{if(C.current!==r)for(let{obj:{value:t},score:s}of(C.current=r,j.current.clear(),h().go(r,e,{key:"label"})))j.current.set(t,s);return"all"===t?.01*(j.current.size>1):j.current.get(t)??0},[e]);return(0,a.jsxs)(y.Popover,{open:A,onOpenChange:M,modal:d,children:[(0,a.jsx)(y.PopoverTrigger,{asChild:!0,children:(0,a.jsx)("div",{ref:p,...g,onClick:()=>{M(e=>!e)},className:(0,l.cn)("flex w-full rounded-sm min-h-9 h-auto items-center justify-between [&_svg]:pointer-events-auto","font-medium border border-input bg-input hover:opacity-80 cursor-pointer",u),children:m.length>0?(0,a.jsx)("div",{className:"flex justify-between items-center w-full",children:(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-1 p-1",children:[m.slice(0,o).map(t=>(0,a.jsx)(c,{className:(0,l.cn)(b({variant:r})),children:(0,a.jsxs)("div",{className:"flex items-center gap-1.5",children:[(0,a.jsx)("div",{children:e.find(e=>e.value===t)?.label}),(0,a.jsx)("div",{onClick:e=>{e.stopPropagation(),I(t)},className:"cursor-pointer",children:(0,a.jsx)(k.A,{className:"size-4 rounded-full p-0.5 bg-accent/5"})})]})},t)),m.length>o&&(0,a.jsx)(c,{className:(0,l.cn)("text-ring",b({variant:r})),children:(0,a.jsxs)("div",{className:"flex items-center gap-1.5",children:[(0,a.jsx)("div",{children:`+ ${m.length-o} more`}),(0,a.jsx)("div",{onClick:e=>{e.stopPropagation(),N()},className:"cursor-pointer",children:(0,a.jsx)(k.A,{className:"size-4 rounded-full p-0.5 bg-ring/5"})})]})})]})}):(0,a.jsxs)("div",{className:"flex items-center justify-between w-full mx-auto",children:[(0,a.jsx)("span",{className:"text-muted-foreground mx-3",children:i}),(0,a.jsx)(w.A,{className:"opacity-50 size-4 mx-2"})]})})}),(0,a.jsx)(y.PopoverContent,{className:"p-0 w-[var(--radix-popover-trigger-width)]",align:"start",onEscapeKeyDown:()=>M(!1),children:(0,a.jsxs)(f.Command,{filter:D,children:[(0,a.jsx)(f.CommandInput,{placeholder:"Search",onKeyDown:e=>{if("Enter"===e.key)M(!0);else if("Backspace"===e.key&&!e.currentTarget.value){let e=[...m];e.pop(),v(e),t(e)}}}),(0,a.jsxs)(f.CommandList,{children:[(0,a.jsx)(f.CommandEmpty,{children:"No results found."}),(0,a.jsxs)(f.CommandGroup,{children:[e.map(e=>(0,a.jsxs)(f.CommandItem,{value:e.value,onSelect:()=>I(e.value),className:"flex items-center justify-between",children:[(0,a.jsx)("span",{children:e.label}),(0,a.jsx)(x.A,{className:(0,l.cn)("text-accent group-data-[selected=true]:text-accent-foreground size-4",{"opacity-0":!m.includes(e.value)})})]},e.value)),(0,a.jsx)(f.CommandItem,{value:"all",onSelect:()=>{let e=Array.from(j.current.keys());if(m.length===e.length&&m.sort().join(",")===e.sort().join(",")){v([]),t([]);return}v(e),t(e)},className:"flex items-center justify-between",children:(0,a.jsx)("span",{children:"Select All"})},"all")]})]})]})})]})});A.displayName="MultiSelect";var M=r(1196);r(54927),r(84774);var I=r(76572);r(176);var N=r(81371),j=r(16667);function C({className:e,...t}){return(0,a.jsx)("textarea",{"data-slot":"textarea",className:(0,l.cn)("placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive flex field-sizing-content min-h-16 w-full rounded-sm px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50","border border-input bg-input",e),...t})}r(8442)},29230:(e,t,r)=>{r.r(t),r.d(t,{default:()=>a});var s=r(11280);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},65391:(e,t,r)=>{let s,a;r.d(t,{Sn:()=>D,J:()=>S,ut:()=>q,UT:()=>T,xF:()=>F,w8:()=>z,z:()=>$,NL:()=>E,x1:()=>R,Sk:()=>K});var n={};r.r(n),r.d(n,{runs:()=>m,runsRelations:()=>v,schema:()=>b,taskMetrics:()=>w,tasks:()=>h,tasksRelations:()=>k,toolErrors:()=>x,toolErrorsRelations:()=>y});var i=r(60398),o=r(6611),l=r(90234),d=r(56125),c=r(58061),u=r(69993),f=r(85129),g=r(58395),p=r(63429);let m=(0,i.cJ)("runs",{id:(0,o.nd)().primaryKey().generatedAlwaysAsIdentity(),taskMetricsId:(0,o.nd)("task_metrics_id").references(()=>w.id),model:(0,l.Qq)().notNull(),description:(0,l.Qq)(),settings:(0,d.Fx)().$type(),pid:(0,o.nd)(),socketPath:(0,l.Qq)("socket_path").notNull(),concurrency:(0,o.nd)().default(2).notNull(),timeout:(0,o.nd)().default(5).notNull(),passed:(0,o.nd)().default(0).notNull(),failed:(0,o.nd)().default(0).notNull(),createdAt:(0,c.vE)("created_at").notNull()}),v=(0,p.K1)(m,({one:e})=>({taskMetrics:e(w,{fields:[m.taskMetricsId],references:[w.id]})})),h=(0,i.cJ)("tasks",{id:(0,o.nd)().primaryKey().generatedAlwaysAsIdentity(),runId:(0,o.nd)("run_id").references(()=>m.id).notNull(),taskMetricsId:(0,o.nd)("task_metrics_id").references(()=>w.id),language:(0,l.Qq)().notNull().$type(),exercise:(0,l.Qq)().notNull(),passed:(0,u.zM)(),startedAt:(0,c.vE)("started_at"),finishedAt:(0,c.vE)("finished_at"),createdAt:(0,c.vE)("created_at").notNull()},e=>[(0,f.GL)("tasks_language_exercise_idx").on(e.runId,e.language,e.exercise)]),k=(0,p.K1)(h,({one:e})=>({run:e(m,{fields:[h.runId],references:[m.id]}),taskMetrics:e(w,{fields:[h.taskMetricsId],references:[w.id]})})),w=(0,i.cJ)("taskMetrics",{id:(0,o.nd)().primaryKey().generatedAlwaysAsIdentity(),tokensIn:(0,o.nd)("tokens_in").notNull(),tokensOut:(0,o.nd)("tokens_out").notNull(),tokensContext:(0,o.nd)("tokens_context").notNull(),cacheWrites:(0,o.nd)("cache_writes").notNull(),cacheReads:(0,o.nd)("cache_reads").notNull(),cost:(0,g.x)().notNull(),duration:(0,o.nd)().notNull(),toolUsage:(0,d.Fx)("tool_usage").$type(),createdAt:(0,c.vE)("created_at").notNull()}),x=(0,i.cJ)("toolErrors",{id:(0,o.nd)().primaryKey().generatedAlwaysAsIdentity(),runId:(0,o.nd)("run_id").references(()=>m.id),taskId:(0,o.nd)("task_id").references(()=>h.id),toolName:(0,l.Qq)("tool_name").notNull().$type(),error:(0,l.Qq)().notNull(),createdAt:(0,c.vE)("created_at").notNull()}),y=(0,p.K1)(x,({one:e})=>({run:e(m,{fields:[x.runId],references:[m.id]}),task:e(h,{fields:[x.taskId],references:[h.id]})})),b={runs:m,runsRelations:v,tasks:h,tasksRelations:k,taskMetrics:w,toolErrors:x,toolErrorsRelations:y};var A=r(36050);class M extends Error{}class I extends Error{}var N=r(51642),j=r(61786);let C=(0,j.A)(process.env.DATABASE_URL,{prepare:!1}),D=(0,N.f)({client:C,schema:n}),E=()=>{if(!process.env.PRODUCTION_DATABASE_URL)throw Error("PRODUCTION_DATABASE_URL is not set");return a||(s=(0,j.A)(process.env.PRODUCTION_DATABASE_URL,{prepare:!1}),a=(0,N.f)({client:s,schema:n})),a};var _=r(35964);let T=async e=>{let t=(await D.insert(h).values({...e,createdAt:new Date}).returning())[0];if(!t)throw new I;return t},R=async e=>D.query.tasks.findMany({where:(0,A.eq)(h.runId,e),with:{taskMetrics:!0},orderBy:(0,_.Y)(h.id)}),q=async e=>{let t=(await D.insert(b.runs).values({...e,createdAt:new Date}).returning())[0];if(!t)throw new I;return t},F=async e=>{let t=await D.query.runs.findFirst({where:(0,A.eq)(b.runs.id,e),columns:{taskMetricsId:!0}});if(!t)throw new M;let r=await D.query.tasks.findMany({where:(0,A.eq)(b.tasks.runId,e),columns:{id:!0,taskMetricsId:!0}});await D.delete(b.toolErrors).where((0,A.RV)(b.toolErrors.taskId,r.map(({id:e})=>e))),await D.delete(b.tasks).where((0,A.eq)(b.tasks.runId,e)),await D.delete(b.toolErrors).where((0,A.eq)(b.toolErrors.runId,e)),await D.delete(b.runs).where((0,A.eq)(b.runs.id,e));let s=r.map(({taskMetricsId:e})=>e).filter(e=>null!=e);s.push(t.taskMetricsId??-1),await D.delete(b.taskMetrics).where((0,A.RV)(b.taskMetrics.id,s))},S=async({sourceDb:e,targetDb:t,runId:r})=>{let s=await e.query.runs.findFirst({where:(0,A.eq)(b.runs.id,r),with:{taskMetrics:!0}});if(!s)throw new M(`Run with ID ${r} not found`);let a=null;if(s.taskMetrics){let e={tokensIn:s.taskMetrics.tokensIn,tokensOut:s.taskMetrics.tokensOut,tokensContext:s.taskMetrics.tokensContext,cacheWrites:s.taskMetrics.cacheWrites,cacheReads:s.taskMetrics.cacheReads,cost:s.taskMetrics.cost,duration:s.taskMetrics.duration,toolUsage:s.taskMetrics.toolUsage},r=(await t.insert(b.taskMetrics).values({...e,createdAt:new Date}).returning())[0];if(!r)throw new I("Failed to create run taskMetrics");a=r.id}let n={taskMetricsId:a,model:s.model,description:s.description,settings:s.settings,pid:s.pid,socketPath:s.socketPath,concurrency:s.concurrency,passed:s.passed,failed:s.failed},i=(await t.insert(b.runs).values({...n,createdAt:new Date}).returning())[0];if(!i)throw new I("Failed to create run");let o=i.id,l=await e.query.tasks.findMany({where:(0,A.eq)(b.tasks.runId,r),with:{taskMetrics:!0}}),d=new Map;for(let e of l){let r=null;if(e.taskMetrics){let s={tokensIn:e.taskMetrics.tokensIn,tokensOut:e.taskMetrics.tokensOut,tokensContext:e.taskMetrics.tokensContext,cacheWrites:e.taskMetrics.cacheWrites,cacheReads:e.taskMetrics.cacheReads,cost:e.taskMetrics.cost,duration:e.taskMetrics.duration,toolUsage:e.taskMetrics.toolUsage},a=(await t.insert(b.taskMetrics).values({...s,createdAt:new Date}).returning())[0];if(!a)throw new I("Failed to create task taskMetrics");r=a.id}let s={runId:o,taskMetricsId:r,language:e.language,exercise:e.exercise,passed:e.passed,startedAt:e.startedAt,finishedAt:e.finishedAt},a=(await t.insert(b.tasks).values({...s,createdAt:new Date}).returning())[0];if(!a)throw new I("Failed to create task");d.set(e.id,a.id)}for(let[r,s]of d)for(let a of(await e.query.toolErrors.findMany({where:(0,A.eq)(b.toolErrors.taskId,r)}))){let e={runId:o,taskId:s,toolName:a.toolName,error:a.error};await t.insert(b.toolErrors).values({...e,createdAt:new Date})}for(let s of(await e.query.toolErrors.findMany({where:(0,A.eq)(b.toolErrors.runId,r)}))){if(s.taskId&&d.has(s.taskId))continue;let e={runId:o,taskId:s.taskId&&d.get(s.taskId)||null,toolName:s.toolName,error:s.error};await t.insert(b.toolErrors).values({...e,createdAt:new Date})}return o};var U=r(33873),L=r(79748),P=r(79551);let O=U.dirname((0,P.fileURLToPath)("file:///C:/Users/<USER>/Desktop/personale/Roo-Code/packages/evals/src/exercises/index.ts"));U.resolve(O,"..","..","..","..","..","evals");let z=["go","java","javascript","python","rust"],K=async(e,t)=>{try{let r=U.resolve(e,t);return(await L.readdir(r,{withFileTypes:!0})).filter(e=>e.isDirectory()&&!e.name.startsWith(".")).map(e=>e.name)}catch(e){return console.error(`Error listing directories at ${t}:`,e),[]}},$=async(e,t)=>K(O,U.join(e,t))}};