"use strict";exports.id=672,exports.ids=[672],exports.modules={148:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(72364).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},60770:(e,t,r)=>{r.d(t,{EH:()=>l,_y:()=>d,a3:()=>a,vv:()=>n});let s=new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}),n=e=>s.format(e),a=e=>{let t=Math.floor(e/1e3),r=Math.floor(t/3600),s=Math.floor(t%3600/60),n=t%60,a=[];return r>0&&a.push(`${r}h`),s>0&&a.push(`${s}m`),(n>0||0===a.length)&&a.push(`${n}s`),a.join(" ")},d=e=>e<1e3?e.toString():e<1e6?`${(e/1e3).toFixed(1)}k`:e<1e9?`${(e/1e6).toFixed(1)}M`:`${(e/1e9).toFixed(1)}B`,l=e=>0===e.attempts?"0%":`${((e.attempts-e.failures)/e.attempts*100).toFixed(1)}%`},97978:(e,t,r)=>{r.d(t,{Lc:()=>w,Ze:()=>$});var s={};r.r(s),r.d(s,{runs:()=>k,runsRelations:()=>y,schema:()=>A,taskMetrics:()=>N,tasks:()=>p,tasksRelations:()=>_,toolErrors:()=>h,toolErrorsRelations:()=>m});var n=r(10992),a=r(17542),d=r(98741),l=r(12),o=r(94396),i=r(22520),c=r(15224),u=r(98894),f=r(22509);let k=(0,n.cJ)("runs",{id:(0,a.nd)().primaryKey().generatedAlwaysAsIdentity(),taskMetricsId:(0,a.nd)("task_metrics_id").references(()=>N.id),model:(0,d.Qq)().notNull(),description:(0,d.Qq)(),settings:(0,l.Fx)().$type(),pid:(0,a.nd)(),socketPath:(0,d.Qq)("socket_path").notNull(),concurrency:(0,a.nd)().default(2).notNull(),timeout:(0,a.nd)().default(5).notNull(),passed:(0,a.nd)().default(0).notNull(),failed:(0,a.nd)().default(0).notNull(),createdAt:(0,o.vE)("created_at").notNull()}),y=(0,f.K1)(k,({one:e})=>({taskMetrics:e(N,{fields:[k.taskMetricsId],references:[N.id]})})),p=(0,n.cJ)("tasks",{id:(0,a.nd)().primaryKey().generatedAlwaysAsIdentity(),runId:(0,a.nd)("run_id").references(()=>k.id).notNull(),taskMetricsId:(0,a.nd)("task_metrics_id").references(()=>N.id),language:(0,d.Qq)().notNull().$type(),exercise:(0,d.Qq)().notNull(),passed:(0,i.zM)(),startedAt:(0,o.vE)("started_at"),finishedAt:(0,o.vE)("finished_at"),createdAt:(0,o.vE)("created_at").notNull()},e=>[(0,c.GL)("tasks_language_exercise_idx").on(e.runId,e.language,e.exercise)]),_=(0,f.K1)(p,({one:e})=>({run:e(k,{fields:[p.runId],references:[k.id]}),taskMetrics:e(N,{fields:[p.taskMetricsId],references:[N.id]})})),N=(0,n.cJ)("taskMetrics",{id:(0,a.nd)().primaryKey().generatedAlwaysAsIdentity(),tokensIn:(0,a.nd)("tokens_in").notNull(),tokensOut:(0,a.nd)("tokens_out").notNull(),tokensContext:(0,a.nd)("tokens_context").notNull(),cacheWrites:(0,a.nd)("cache_writes").notNull(),cacheReads:(0,a.nd)("cache_reads").notNull(),cost:(0,u.x)().notNull(),duration:(0,a.nd)().notNull(),toolUsage:(0,l.Fx)("tool_usage").$type(),createdAt:(0,o.vE)("created_at").notNull()}),h=(0,n.cJ)("toolErrors",{id:(0,a.nd)().primaryKey().generatedAlwaysAsIdentity(),runId:(0,a.nd)("run_id").references(()=>k.id),taskId:(0,a.nd)("task_id").references(()=>p.id),toolName:(0,d.Qq)("tool_name").notNull().$type(),error:(0,d.Qq)().notNull(),createdAt:(0,o.vE)("created_at").notNull()}),m=(0,f.K1)(h,({one:e})=>({run:e(k,{fields:[h.runId],references:[k.id]}),task:e(p,{fields:[h.taskId],references:[p.id]})})),A={runs:k,runsRelations:y,tasks:p,tasksRelations:_,taskMetrics:N,toolErrors:h,toolErrorsRelations:m};var v=r(88589),x=r(23891);class I extends Error{}var M=r(55278);let g=(0,r(40078).A)(process.env.DATABASE_URL,{prepare:!1}),E=(0,M.f)({client:g,schema:s}),w=async e=>{let t=await E.query.runs.findFirst({where:(0,v.eq)(A.runs.id,e)});if(!t)throw new I;return t},$=async()=>E.query.runs.findMany({orderBy:(0,x.i)(A.runs.id),with:{taskMetrics:!0}});var q=r(33873);r(79748);var F=r(79551);let K=q.dirname((0,F.fileURLToPath)("file:///C:/Users/<USER>/Desktop/personale/Roo-Code/packages/evals/src/exercises/index.ts"));q.resolve(K,"..","..","..","..","..","evals")}};