1:"$Sreact.fragment"
2:I[80983,["858","static/chunks/aba955ec-9833060e0c799abe.js","840","static/chunks/e5c963a3-f3448fec3e69041a.js","777","static/chunks/88e8e2ac-7a1619a6e98006b4.js","798","static/chunks/2ae967de-afd0847aa04941df.js","180","static/chunks/dd0ec533-85265a862baf3739.js","872","static/chunks/5d6f4545-30a8b6f9fd887cd0.js","604","static/chunks/604-37da8378c60f8591.js","156","static/chunks/156-01d88aff08204544.js","657","static/chunks/657-9a922aaadb9c4bb2.js","954","static/chunks/954-09ef0ac74eac9fe3.js","554","static/chunks/554-3f07505b777a310d.js","177","static/chunks/app/layout-70c3bb938d5edbac.js"],""]
3:I[39183,["858","static/chunks/aba955ec-9833060e0c799abe.js","840","static/chunks/e5c963a3-f3448fec3e69041a.js","777","static/chunks/88e8e2ac-7a1619a6e98006b4.js","798","static/chunks/2ae967de-afd0847aa04941df.js","180","static/chunks/dd0ec533-85265a862baf3739.js","872","static/chunks/5d6f4545-30a8b6f9fd887cd0.js","604","static/chunks/604-37da8378c60f8591.js","156","static/chunks/156-01d88aff08204544.js","657","static/chunks/657-9a922aaadb9c4bb2.js","954","static/chunks/954-09ef0ac74eac9fe3.js","554","static/chunks/554-3f07505b777a310d.js","177","static/chunks/app/layout-70c3bb938d5edbac.js"],"Providers"]
5:I[95823,[],""]
6:I[20531,[],""]
7:I[6445,[],"OutletBoundary"]
a:I[6445,[],"ViewportBoundary"]
c:I[6445,[],"MetadataBoundary"]
e:I[38826,[],""]
:HL["/_next/static/css/c16eb50558afbc00.css","style"]
0:{"P":null,"b":"Hrz9dGjjQ4zbSgAqNHnYG","p":"","c":["","terms"],"i":false,"f":[[["",{"children":["terms",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/c16eb50558afbc00.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","suppressHydrationWarning":true,"children":[["$","head",null,{"children":["$","link",null,{"rel":"stylesheet","type":"text/css","href":"https://cdn.jsdelivr.net/gh/devicons/devicon@latest/devicon.min.css"}]}],["$","body",null,{"className":"__className_e8ce0c","children":[["$","$L2",null,{"src":"https://www.googletagmanager.com/gtag/js?id=AW-17391954825","strategy":"afterInteractive"}],["$","$L2",null,{"id":"google-analytics","strategy":"afterInteractive","children":"\n\t\t\t\t\t\twindow.dataLayer = window.dataLayer || [];\n\t\t\t\t\t\tfunction gtag(){dataLayer.push(arguments);}\n\t\t\t\t\t\tgtag('js', new Date());\n\t\t\t\t\t\tgtag('config', 'AW-17391954825');\n\t\t\t\t\t"}],["$","div",null,{"itemScope":true,"itemType":"https://schema.org/WebSite","children":[["$","link",null,{"itemProp":"url","href":"https://roocode.com"}],["$","meta",null,{"itemProp":"name","content":"Roo Code"}]]}],["$","$L3",null,{"children":"$L4"}]]}]]}]]}],{"children":["terms",["$","$1","c",{"children":[null,["$","$L5",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L6",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","div",null,{"className":"container mx-auto px-4 py-12 sm:px-6 lg:px-8","children":["$","div",null,{"className":"prose prose-lg mx-auto max-w-4xl dark:prose-invert","children":[["$","h1",null,{"className":"text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl","children":"Roo Code Cloud Terms of Service"}],["$","p",null,{"className":"text-muted-foreground","children":["$","em",null,{"children":"(Version 1.0 – Effective June 19, 2025)"}]}],["$","p",null,{"className":"lead","children":["These Terms of Service (\"",["$","strong",null,{"children":"TOS"}],"\") govern access to and use of the Roo Code Cloud service (the \"",["$","strong",null,{"children":"Service"}],"\"). They apply to:"]}],["$","ul",null,{"className":"lead","children":[["$","li",null,{"children":[["$","strong",null,{"children":"(a)"}]," every ",["$","strong",null,{"children":"Sales Order Form"}]," or similar document mutually executed by Roo Code and the customer that references these TOS; ",["$","strong",null,{"children":"and"}]]}],["$","li",null,{"children":[["$","strong",null,{"children":"(b)"}]," any"," ",["$","strong",null,{"children":"online plan-selection, self-service sign-up, or in-app purchase flow"}]," ","through which a customer clicks an \"I Agree\" (or equivalent) button to accept these TOS — such flow also being an ",["$","strong",null,{"children":"\"Order Form.\""}]]}]]}],["$","p",null,{"children":["By ",["$","strong",null,{"children":"creating an account, clicking to accept, or using the Service"}],", the person or entity doing so (\"",["$","strong",null,{"children":"Customer"}],"\") agrees to be bound by these TOS, even if no separate Order Form is signed."]}],["$","p",null,{"children":["If Roo Code and Customer later execute a Master Subscription Agreement (\"",["$","strong",null,{"children":"MSA"}],"\"), the MSA governs; otherwise, these TOS and the applicable Order Form together form the entire agreement (the \"",["$","strong",null,{"children":"Agreement"}],"\")."]}],["$","h2",null,{"className":"mt-12 text-2xl font-bold","children":"1. Agreement Framework"}],["$","ol",null,{"children":[["$","li",null,{"children":[["$","strong",null,{"children":"Incorporation of Standard Terms."}],["$","br",null,{}],"The"," ",["$","a",null,{"href":"https://commonpaper.com/standards/cloud-service-agreement/2.0/","target":"_blank","rel":"noopener noreferrer","className":"text-primary hover:underline","children":["$","em",null,{"children":"Common Paper Cloud Service Standard Terms v 2.0"}]}]," ","(the \"",["$","strong",null,{"children":"Standard Terms"}],"\") are incorporated by reference. If these TOS conflict with the Standard Terms, these TOS control."]}],["$","li",null,{"children":[["$","strong",null,{"children":"Order of Precedence."}],["$","br",null,{}],"(a) Order Form (b) these TOS (c) Standard Terms."]}]]}],["$","h2",null,{"className":"mt-12 text-2xl font-bold","children":"2. Key Commercial Terms"}],["$","div",null,{"className":"overflow-x-auto","children":["$","table",null,{"className":"min-w-full border-collapse border border-border","children":[["$","thead",null,{"children":["$","tr",null,{"className":"bg-muted/50","children":[["$","th",null,{"className":"border border-border px-4 py-2 text-left font-semibold","children":"Term"}],["$","th",null,{"className":"border border-border px-4 py-2 text-left font-semibold","children":"Value"}]]}]}],["$","tbody",null,{"children":[["$","tr",null,{"children":[["$","td",null,{"className":"border border-border px-4 py-2 font-medium","children":"Governing Law / Forum"}],["$","td",null,{"className":"border border-border px-4 py-2","children":"Delaware law; exclusive jurisdiction and venue in the state or federal courts located in Delaware"}]]}],["$","tr",null,{"className":"bg-muted/25","children":[["$","td",null,{"className":"border border-border px-4 py-2 font-medium","children":"Plans & Subscription Periods"}],["$","td",null,{"className":"border border-border px-4 py-2","children":[["$","em",null,{"children":"Free Plan:"}]," month-to-month.",["$","br",null,{}],["$","em",null,{"children":"Paid Plans:"}]," Monthly ",["$","strong",null,{"children":"or"}]," Annual, as selected in an Order Form or the online flow."]}]]}],["$","tr",null,{"children":[["$","td",null,{"className":"border border-border px-4 py-2 font-medium","children":"Auto-Renewal & Non-Renewal Notice"}],["$","td",null,{"className":"border border-border px-4 py-2","children":[["$","em",null,{"children":"Free Plan:"}]," renews continuously until cancelled in the dashboard.",["$","br",null,{}],["$","em",null,{"children":"Paid Plans:"}]," renew for the same period unless either party gives 30 days' written notice before the current period ends."]}]]}],["$","tr",null,{"className":"bg-muted/25","children":[["$","td",null,{"className":"border border-border px-4 py-2 font-medium","children":"Fees & Usage"}],["$","td",null,{"className":"border border-border px-4 py-2","children":[["$","em",null,{"children":"Free Plan:"}]," Subscription Fee = $0.",["$","br",null,{}],["$","em",null,{"children":"Paid Plans:"}]," Fees stated in the Order Form or online checkout"," ",["$","strong",null,{"children":"plus"}]," usage-based fees, calculated and invoiced monthly."]}]]}],["$","tr",null,{"children":[["$","td",null,{"className":"border border-border px-4 py-2 font-medium","children":"Payment Terms"}],["$","td",null,{"className":"border border-border px-4 py-2","children":[["$","em",null,{"children":"Monthly paid plans:"}]," credit-card charge on the billing date.",["$","br",null,{}],["$","em",null,{"children":"Annual paid plans:"}]," invoiced Net 30 (credit card optional)."]}]]}],["$","tr",null,{"className":"bg-muted/25","children":[["$","td",null,{"className":"border border-border px-4 py-2 font-medium","children":"General Liability Cap"}],["$","td",null,{"className":"border border-border px-4 py-2","children":"The greater of (i) USD 100 and (ii) 1 × Fees paid or payable in the 12 months before the event giving rise to liability."}]]}],["$","tr",null,{"children":[["$","td",null,{"className":"border border-border px-4 py-2 font-medium","children":"Increased Cap / Unlimited Claims"}],["$","td",null,{"className":"border border-border px-4 py-2","children":"None"}]]}],["$","tr",null,{"className":"bg-muted/25","children":[["$","td",null,{"className":"border border-border px-4 py-2 font-medium","children":"Trial / Pilot"}],["$","td",null,{"className":"border border-border px-4 py-2","children":"Not offered"}]]}],["$","tr",null,{"children":[["$","td",null,{"className":"border border-border px-4 py-2 font-medium","children":"Beta Features"}],["$","td",null,{"className":"border border-border px-4 py-2","children":"None – only generally available features are provided"}]]}],["$","tr",null,{"className":"bg-muted/25","children":[["$","td",null,{"className":"border border-border px-4 py-2 font-medium","children":"Security Standard"}],["$","td",null,{"className":"border border-border px-4 py-2","children":"Roo Code maintains commercially reasonable administrative, physical, and technical safeguards"}]]}],["$","tr",null,{"children":[["$","td",null,{"className":"border border-border px-4 py-2 font-medium","children":"Machine-Learning Use"}],["$","td",null,{"className":"border border-border px-4 py-2","children":["Roo Code ",["$","strong",null,{"children":"does not"}]," use Customer Content to train, fine-tune, or improve any ML or AI models"]}]]}],["$","tr",null,{"className":"bg-muted/25","children":[["$","td",null,{"className":"border border-border px-4 py-2 font-medium","children":"Data Processing Addendum (DPA)"}],["$","td",null,{"className":"border border-border px-4 py-2","children":"GDPR/CCPA-ready DPA available upon written request"}]]}],["$","tr",null,{"children":[["$","td",null,{"className":"border border-border px-4 py-2 font-medium","children":"Publicity / Logo Rights"}],["$","td",null,{"className":"border border-border px-4 py-2","children":"Roo Code may identify Customer (name & logo) in marketing materials unless Customer opts out in writing"}]]}]]}]]}]}],["$","h2",null,{"className":"mt-12 text-2xl font-bold","children":"3. Modifications to the Standard Terms"}],["$","ol",null,{"children":[["$","li",null,{"children":[["$","strong",null,{"children":"Section 1.6 (Machine Learning)."}],["$","br",null,{}],"\"Provider will not use Customer Content or Usage Data to train, fine-tune, or improve any machine-learning or AI model, except with Customer's prior written consent.\""]}],["$","li",null,{"children":[["$","strong",null,{"children":"Section 3 (Security)."}],["$","br",null,{}],"Replace \"reasonable\" with \"commercially reasonable.\""]}],["$","li",null,{"children":[["$","strong",null,{"children":"Section 4 (Fees & Payment)."}],["$","br",null,{}],"Add usage-billing language above and delete any provision allowing unilateral fee increases."]}],["$","li",null,{"children":[["$","strong",null,{"children":"Section 5 (Term & Termination)."}],["$","br",null,{}],"Insert auto-renewal and free-plan language above."]}],["$","li",null,{"children":[["$","strong",null,{"children":"Sections 7 (Trials / Betas) and any SLA references."}],["$","br",null,{}],"Deleted – Roo Code offers no trials, pilots, betas, or SLA credits under these TOS."]}],["$","li",null,{"children":[["$","strong",null,{"children":"Section 12.12 (Publicity)."}],["$","br",null,{}],"As reflected in the \"Publicity / Logo Rights\" row above."]}]]}],["$","h2",null,{"className":"mt-12 text-2xl font-bold","children":"4. Use of the Service"}],["$","p",null,{"children":"Customer may access and use the Service solely for its internal business purposes and subject to the Acceptable Use Policy in the Standard Terms."}],["$","h2",null,{"className":"mt-12 text-2xl font-bold","children":"5. Account Management & Termination"}],["$","ul",null,{"children":[["$","li",null,{"children":[["$","strong",null,{"children":"Self-service cancellation or downgrade."}],["$","br",null,{}],"Customer may cancel a Free Plan immediately, or cancel/downgrade a Paid Plan effective at the end of the current billing cycle, via the web dashboard."]}],["$","li",null,{"children":"Either party may otherwise terminate the Agreement as allowed under Section 5 of the Standard Terms."}]]}],["$","h2",null,{"className":"mt-12 text-2xl font-bold","children":"6. Privacy & Data"}],["$","p",null,{"children":["Roo Code's Privacy Notice (",["$","a",null,{"href":"https://roocode.com/privacy","rel":"noopener noreferrer","className":"text-primary hover:underline","children":"https://roocode.com/privacy"}],") explains how Roo Code collects and handles personal information. If Customer requires a DPA, email"," ",["$","a",null,{"href":"mailto:<EMAIL>","className":"text-primary hover:underline","children":"<EMAIL>"}],"."]}],["$","h2",null,{"className":"mt-12 text-2xl font-bold","children":"7. Warranty Disclaimer"}],["$","p",null,{"children":["Except as expressly stated in the Agreement, the Service is provided"," ",["$","strong",null,{"children":"\"as is,\""}]," and all implied warranties are disclaimed to the maximum extent allowed by law."]}],["$","h2",null,{"className":"mt-12 text-2xl font-bold","children":"8. Limitation of Liability"}],["$","p",null,{"children":"The caps in Section 2 apply to all claims under the Agreement, whether in contract, tort, or otherwise, except for Excluded Claims defined in the Standard Terms."}],["$","h2",null,{"className":"mt-12 text-2xl font-bold","children":"9. Miscellaneous"}],["$","ol",null,{"children":[["$","li",null,{"children":[["$","strong",null,{"children":"Assignment."}],["$","br",null,{}],"Customer may not assign the Agreement without Roo Code's prior written consent, except to a successor in a merger or sale of substantially all assets."]}],["$","li",null,{"children":[["$","strong",null,{"children":"Export Compliance."}],["$","br",null,{}],"Each party will comply with all applicable export-control laws and regulations and will not export or re-export any software or technical data without the required government licences."]}],["$","li",null,{"children":[["$","strong",null,{"children":"Entire Agreement."}],["$","br",null,{}],"The Agreement supersedes all prior or contemporaneous agreements for the Service."]}],["$","li",null,{"children":[["$","strong",null,{"children":"Amendments."}],["$","br",null,{}],"Roo Code may update these TOS by posting a revised version at the same URL and emailing or in-app notifying Customer at least 30 days before changes take effect. Continued use after the effective date constitutes acceptance."]}]]}],["$","h2",null,{"className":"mt-12 text-2xl font-bold","children":"10. Contact"}],["$","p",null,{"children":[["$","strong",null,{"children":"Roo Code, Inc."}],["$","br",null,{}],"98 Graceland Dr, San Rafael, CA 94901 USA",["$","br",null,{}],"Email:"," ",["$","a",null,{"href":"mailto:<EMAIL>","className":"text-primary hover:underline","children":"<EMAIL>"}]]}]]}]}],"$undefined",null,["$","$L7",null,{"children":["$L8","$L9",null]}]]}],{},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","XZZ_ikziTAFAvVoIm9qes",{"children":[["$","$La",null,{"children":"$Lb"}],null]}],["$","$Lc",null,{"children":"$Ld"}]]}],false]],"m":"$undefined","G":["$e","$undefined"],"s":false,"S":true}
b:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
8:null
9:null
d:[["$","title","0",{"children":"Terms of Service - Roo Code"}],["$","meta","1",{"name":"description","content":"Terms of Service for Roo Code Cloud. Learn about our service terms, commercial conditions, and legal framework."}],["$","link","2",{"rel":"canonical","href":"https://roocode.com"}],["$","link","3",{"rel":"icon","href":"/favicon.ico"}],["$","link","4",{"rel":"icon","href":"/favicon-16x16.png","sizes":"16x16","type":"image/png"}],["$","link","5",{"rel":"icon","href":"/favicon-32x32.png","sizes":"32x32","type":"image/png"}],["$","link","6",{"rel":"apple-touch-icon","href":"/apple-touch-icon.png"}],["$","link","7",{"rel":"android-chrome-192x192","href":"/android-chrome-192x192.png","sizes":"192x192","type":"image/png"}],["$","link","8",{"rel":"android-chrome-512x512","href":"/android-chrome-512x512.png","sizes":"512x512","type":"image/png"}]]
f:I[91095,["858","static/chunks/aba955ec-9833060e0c799abe.js","840","static/chunks/e5c963a3-f3448fec3e69041a.js","777","static/chunks/88e8e2ac-7a1619a6e98006b4.js","798","static/chunks/2ae967de-afd0847aa04941df.js","180","static/chunks/dd0ec533-85265a862baf3739.js","872","static/chunks/5d6f4545-30a8b6f9fd887cd0.js","604","static/chunks/604-37da8378c60f8591.js","156","static/chunks/156-01d88aff08204544.js","657","static/chunks/657-9a922aaadb9c4bb2.js","954","static/chunks/954-09ef0ac74eac9fe3.js","554","static/chunks/554-3f07505b777a310d.js","177","static/chunks/app/layout-70c3bb938d5edbac.js"],"NavBar"]
10:I[29685,["858","static/chunks/aba955ec-9833060e0c799abe.js","840","static/chunks/e5c963a3-f3448fec3e69041a.js","777","static/chunks/88e8e2ac-7a1619a6e98006b4.js","798","static/chunks/2ae967de-afd0847aa04941df.js","180","static/chunks/dd0ec533-85265a862baf3739.js","872","static/chunks/5d6f4545-30a8b6f9fd887cd0.js","604","static/chunks/604-37da8378c60f8591.js","156","static/chunks/156-01d88aff08204544.js","657","static/chunks/657-9a922aaadb9c4bb2.js","954","static/chunks/954-09ef0ac74eac9fe3.js","554","static/chunks/554-3f07505b777a310d.js","177","static/chunks/app/layout-70c3bb938d5edbac.js"],"Footer"]
4:["$","div",null,{"className":"flex min-h-screen flex-col bg-background text-foreground","children":[["$","$Lf",null,{"stars":"17.6k","downloads":"702.6k"}],["$","main",null,{"className":"flex-1","children":["$","$L5",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L6",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}],["$","$L10",null,{}]]}]
