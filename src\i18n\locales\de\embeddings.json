{"unknownError": "Unbekannter Fehler", "authenticationFailed": "Erstellung von Einbettungen fehlgeschlagen: Authentifizierung fehlgeschlagen. Bitte überprüfe deinen API-Schlüssel.", "failedWithStatus": "Erstellung von Einbettungen nach {{attempts}} Versuchen fehlgeschlagen: HTTP {{statusCode}} - {{errorMessage}}", "failedWithError": "Erstellung von Einbettungen nach {{attempts}} Versuchen fehlgeschlagen: {{errorMessage}}", "failedMaxAttempts": "Erstellung von Einbettungen nach {{attempts}} Versuchen fehlgeschlagen", "textExceedsTokenLimit": "Text bei Index {{index}} überschreitet das maximale Token-Limit ({{itemTokens}} > {{maxTokens}}). Wird übersprungen.", "rateLimitRetry": "Raten<PERSON>it er<PERSON><PERSON><PERSON>, <PERSON>iederholung in {{delayMs}}ms (Versuch {{attempt}}/{{maxRetries}})", "ollama": {"couldNotReadErrorBody": "Fehlerinhalt konnte nicht gelesen werden", "requestFailed": "Ollama API-Anfrage fehlgeschlagen mit Status {{status}} {{statusText}}: {{errorBody}}", "invalidResponseStructure": "Ungültige Antwortstruktur von Ollama API: \"embeddings\" Array nicht gefunden oder kein Array.", "embeddingFailed": "Ollama Einbettung fehlgeschlagen: {{message}}", "serviceNotRunning": "Ollama-<PERSON><PERSON> wird unter {{baseUrl}} nicht ausgeführt", "serviceUnavailable": "Ollama-<PERSON><PERSON> ist nicht verfügbar (Status: {{status}})", "modelNotFound": "Ollama-Modell nicht gefunden: {{modelId}}", "modelNotEmbeddingCapable": "Ollama-Modell ist nicht für Einbettungen geeignet: {{modelId}}", "hostNotFound": "Ollama-Host nicht gefunden: {{baseUrl}}"}, "scanner": {"unknownErrorProcessingFile": "Unbekannter Fehler beim Verarbeiten der Datei {{filePath}}", "unknownErrorDeletingPoints": "Unbekannter Fehler beim Löschen der Punkte für {{filePath}}", "failedToProcessBatchWithError": "Verarbeitung des Batches nach {{maxRetries}} Versuchen fehlgeschlagen: {{errorMessage}}"}, "vectorStore": {"qdrantConnectionFailed": "Verbindung zur Qdrant-Vektordatenbank fehlgeschlagen. <PERSON><PERSON> sic<PERSON>, dass Qdrant läuft und unter {{qdrantUrl}} erreich<PERSON> ist. <PERSON><PERSON>: {{errorMessage}}", "vectorDimensionMismatch": "Aktualisierung des Vektorindex für neues Modell fehlgeschlagen. Bitte versuche, den Index zu löschen und von vorne zu beginnen. Details: {{errorMessage}}"}, "validation": {"authenticationFailed": "Authentifizierung fehlgeschlagen. Bitte überprüfe deinen API-Schlüssel in den Einstellungen.", "connectionFailed": "Verbindung zum Embedder-Dienst fehlgeschlagen. Bitte überprüfe deine Verbindungseinstellungen und stelle sicher, dass der Dienst läuft.", "modelNotAvailable": "Das angegebene Modell ist nicht verfügbar. Bitte überprüfe deine Modellkonfiguration.", "configurationError": "Ungültige Embedder-Konfiguration. Bitte überprüfe deine Einstellungen.", "serviceUnavailable": "Der Embedder-Dienst ist nicht verfügbar. <PERSON>te stelle sicher, dass er läuft und erreichbar ist.", "invalidEndpoint": "Ungültiger API-Endpunkt. Bitte überprüfe deine URL-Konfiguration.", "invalidEmbedderConfig": "Ungültige Embedder-Konfiguration. Bitte überprüfe deine Einstellungen.", "invalidApiKey": "Ungültiger API-Schlüssel. Bitte überprüfe deine API-Schlüssel-Konfiguration.", "invalidBaseUrl": "Ungültige Basis-URL. Bitte überprüfe deine URL-Konfiguration.", "invalidModel": "Ungültiges Modell. Bitte überprüfe deine Modellkonfiguration.", "invalidResponse": "Ungültige Antwort vom Embedder-Dienst. Bitte überprüfe deine Konfiguration.", "apiKeyRequired": "API-Schlüssel ist für diesen Embedder erforderlich", "baseUrlRequired": "Basis-URL ist für diesen Embedder erforderlich"}, "serviceFactory": {"openAiConfigMissing": "OpenAI-Konfiguration fehlt für die Erstellung des Embedders", "ollamaConfigMissing": "Ollama-Konfiguration fehlt für die Erstellung des Embedders", "openAiCompatibleConfigMissing": "OpenAI-kompatible Konfiguration fehlt für die Erstellung des Embedders", "geminiConfigMissing": "Gemini-Konfiguration fehlt für die Erstellung des Embedders", "mistralConfigMissing": "Mistral-Konfiguration fehlt für die Erstellung des Embedders", "invalidEmbedderType": "Ungültiger Embedder-Typ konfiguriert: {{embedderProvider}}", "vectorDimensionNotDeterminedOpenAiCompatible": "Konnte die Vektordimension für Modell '{{modelId}}' mit Anbieter '{{provider}}' nicht bestimmen. <PERSON><PERSON> sic<PERSON>, dass die 'Embedding-Dimension' in den OpenAI-kompatiblen Anbietereinstellungen korrekt eingestellt ist.", "vectorDimensionNotDetermined": "Konnte die Vektordimension für Modell '{{modelId}}' mit Anbieter '{{provider}}' nicht bestimmen. Überprüfe die Modellprofile oder Konfiguration.", "qdrantUrlMissing": "Qdrant-URL fehlt für die Erstellung des Vektorspeichers", "codeIndexingNotConfigured": "<PERSON>nn keine Dienste erstellen: Code-Indizierung ist nicht richtig konfiguriert"}, "orchestrator": {"indexingFailedNoBlocks": "Indizierung fehlgeschlagen: <PERSON>ine Code-Blöcke wurden erfolgreich indiziert. Dies deutet normalerweise auf ein Embedder-Konfigurationsproblem hin.", "indexingFailedCritical": "Indizierung fehlgeschlagen: <PERSON><PERSON>-Bl<PERSON>cke wurden erfolgreich indiziert, obwohl zu verarbeitende Dateien gefunden wurden. Dies deutet auf einen kritischen Embedder-<PERSON><PERSON> hin.", "fileWatcherStarted": "Datei-Watcher gestartet.", "fileWatcherStopped": "Datei-Watcher gestoppt.", "failedDuringInitialScan": "<PERSON><PERSON> während des ersten Scans: {{errorMessage}}", "unknownError": "Unbekannter Fehler", "indexingRequiresWorkspace": "Indexierung erfordert einen offenen Workspace-Ordner"}}